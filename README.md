# Innovative Centre CRM

A modern, scalable CRM system built for English educational centres using microservices architecture.

## 🎯 Overview

This CRM system is designed specifically for the Innovative Centre English Educational Centre, providing comprehensive management of leads, students, courses, teachers, and financial operations.

## 🏗️ Architecture

**3-Service Microservices Architecture:**
- **Staff Service**: Lead management, course creation, teacher operations
- **Admin Service**: Payment records, financial data, system administration (Enhanced Security)
- **Student Service**: Student portal, assignments, academic progress

## 🛠️ Technology Stack

- **Frontend**: Next.js 15.3.4, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL (3 separate databases)
- **Deployment**: Vercel
- **Authentication**: NextAuth.js v5

## 👥 User Roles

### Staff Service
- **Reception**: Lead capture, initial student contact
- **Managers**: Staff oversight, performance monitoring
- **Test Checkers**: Student assessment, progress evaluation
- **Teachers**: Class management, assignment creation

### Admin Service (Enhanced Security)
- **Admin**: System administration, user management
- **Cashier**: Payment processing, financial transactions
- **Accounting**: Financial reporting, audit trails

### Student Service
- **Students**: Academic portal, assignment viewing, progress tracking

## 🔐 Security Features

- **Enhanced Security** for admin service (financial data protection)
- **Separate Databases** for complete data isolation
- **Role-Based Access Control** with granular permissions
- **Comprehensive Audit Logging** for compliance
- **Multi-Factor Authentication** for admin users

## 📋 Core Features

### Lead Management
- Lead capture from multiple sources
- Assignment and conversion tracking
- Communication history and notes

### Course & Group Management
- Course catalog creation and management
- Group formation with capacity limits
- Cabinet (classroom) assignment and scheduling

### Teacher Management
- Teacher profiles and qualifications
- KPI tracking and performance metrics
- Class assignments and scheduling

### Payment Management (Note-keeping)
- Payment record tracking (no actual processing)
- Fee structure management
- Financial reporting and audit trails

### Student Portal
- Personal dashboard with class information
- Assignment viewing and resource access
- Academic progress and grade tracking
- Class schedules and timetables

## 📁 Project Structure

```
crm-server/
├── docs/                       # Documentation
│   ├── ARCHITECTURE.md         # System architecture
│   ├── DATABASE_SCHEMAS.md     # Database schemas
│   ├── IMPLEMENTATION_PLAN.md  # Development timeline
│   ├── FEATURES.md             # Feature specifications
│   └── README.md               # Documentation overview
└── README.md                   # This file
```

## 🚀 Getting Started

1. **Read the Documentation**: Start with `docs/README.md`
2. **Review Architecture**: Understand the system design in `docs/ARCHITECTURE.md`
3. **Set Up Databases**: Follow schemas in `docs/DATABASE_SCHEMAS.md`
4. **Follow Implementation Plan**: Use `docs/IMPLEMENTATION_PLAN.md` for development

## 📈 Development Timeline

**8-10 Week Implementation Plan:**
- **Weeks 1-2**: Foundation & Authentication
- **Weeks 3-6**: Core Service Development
- **Weeks 7-8**: Integration & Polish
- **Weeks 9-10**: Production Deployment

## 🔄 Repository Structure (To Be Created)

```
innovative-centre-crm/
├── crm-staff-service/          # Staff operations
├── crm-admin-service/          # Admin & financial operations
├── crm-student-service/        # Student portal
└── crm-shared-types/           # Shared TypeScript types
```

## 📞 Support

For questions about the architecture or implementation, refer to the comprehensive documentation in the `docs/` folder.

---

**Built for Innovative Centre English Educational Centre**
*Scalable • Secure • Modern*
