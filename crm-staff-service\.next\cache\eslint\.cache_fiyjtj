[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\health\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\leads\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\lib\\db.ts": "5"}, {"size": 642, "mtime": 1750450381543, "results": "6", "hashOfConfig": "7"}, {"size": 3857, "mtime": 1750450612815, "results": "8", "hashOfConfig": "7"}, {"size": 2754, "mtime": 1750450642975, "results": "9", "hashOfConfig": "7"}, {"size": 12293, "mtime": 1750450365816, "results": "10", "hashOfConfig": "7"}, {"size": 322, "mtime": 1750450374105, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hop6ub", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\leads\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\lib\\db.ts", [], []]