(()=>{var K;function I(C){return I=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},I(C)}function N(C,H){var J=Object.keys(C);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(C);H&&(X=X.filter(function(Z){return Object.getOwnPropertyDescriptor(C,Z).enumerable})),J.push.apply(J,X)}return J}function q(C){for(var H=1;H<arguments.length;H++){var J=arguments[H]!=null?arguments[H]:{};H%2?N(Object(J),!0).forEach(function(X){z(C,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(C,Object.getOwnPropertyDescriptors(J)):N(Object(J)).forEach(function(X){Object.defineProperty(C,X,Object.getOwnPropertyDescriptor(J,X))})}return C}function z(C,H,J){if(H=A(H),H in C)Object.defineProperty(C,H,{value:J,enumerable:!0,configurable:!0,writable:!0});else C[H]=J;return C}function A(C){var H=G(C,"string");return I(H)=="symbol"?H:String(H)}function G(C,H){if(I(C)!="object"||!C)return C;var J=C[Symbol.toPrimitive];if(J!==void 0){var X=J.call(C,H||"default");if(I(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(C)}var W=Object.defineProperty,JC=function C(H,J){for(var X in J)W(H,X,{get:J[X],enumerable:!0,configurable:!0,set:function Z(Y){return J[X]=function(){return Y}}})},D={lessThanXSeconds:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u064A\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0648\u0627\u0646\u064A",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0627\u0646\u064A\u0629"},xSeconds:{one:"\u062B\u0627\u0646\u064A\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u062B\u0627\u0646\u062A\u064A\u0646",threeToTen:"{{count}} \u062B\u0648\u0627\u0646\u064A",other:"{{count}} \u062B\u0627\u0646\u064A\u0629"},halfAMinute:"\u0646\u0635\u0641 \u062F\u0642\u064A\u0642\u0629",lessThanXMinutes:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u0627\u0626\u0642",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u064A\u0642\u0629"},xMinutes:{one:"\u062F\u0642\u064A\u0642\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"{{count}} \u062F\u0642\u0627\u0626\u0642",other:"{{count}} \u062F\u0642\u064A\u0642\u0629"},aboutXHours:{one:"\u0633\u0627\u0639\u0629 \u0648\u0627\u062D\u062F\u0629 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0633\u0627\u0639\u062A\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0633\u0627\u0639\u0627\u062A \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0633\u0627\u0639\u0629 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xHours:{one:"\u0633\u0627\u0639\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u0633\u0627\u0639\u062A\u064A\u0646",threeToTen:"{{count}} \u0633\u0627\u0639\u0627\u062A",other:"{{count}} \u0633\u0627\u0639\u0629"},xDays:{one:"\u064A\u0648\u0645 \u0648\u0627\u062D\u062F",two:"\u064A\u0648\u0645\u064A\u0646",threeToTen:"{{count}} \u0623\u064A\u0627\u0645",other:"{{count}} \u064A\u0648\u0645"},aboutXWeeks:{one:"\u0623\u0633\u0628\u0648\u0639 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0623\u0633\u0628\u0648\u0639\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0633\u0627\u0628\u064A\u0639 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0623\u0633\u0628\u0648\u0639 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xWeeks:{one:"\u0623\u0633\u0628\u0648\u0639 \u0648\u0627\u062D\u062F",two:"\u0623\u0633\u0628\u0648\u0639\u064A\u0646",threeToTen:"{{count}} \u0623\u0633\u0627\u0628\u064A\u0639",other:"{{count}} \u0623\u0633\u0628\u0648\u0639"},aboutXMonths:{one:"\u0634\u0647\u0631 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0634\u0647\u0631\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0634\u0647\u0631 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0634\u0647\u0631 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xMonths:{one:"\u0634\u0647\u0631 \u0648\u0627\u062D\u062F",two:"\u0634\u0647\u0631\u064A\u0646",threeToTen:"{{count}} \u0623\u0634\u0647\u0631",other:"{{count}} \u0634\u0647\u0631"},aboutXYears:{one:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0639\u0627\u0645\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xYears:{one:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F",two:"\u0639\u0627\u0645\u064A\u0646",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645",other:"{{count}} \u0639\u0627\u0645"},overXYears:{one:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0639\u0627\u0645",two:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0639\u0627\u0645\u064A\u0646",threeToTen:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0623\u0639\u0648\u0627\u0645",other:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0639\u0627\u0645"},almostXYears:{one:"\u0639\u0627\u0645 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0639\u0627\u0645\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"}},S=function C(H,J,X){X=X||{};var Z=D[H],Y;if(typeof Z==="string")Y=Z;else if(J===1)Y=Z.one;else if(J===2)Y=Z.two;else if(J<=10)Y=Z.threeToTen.replace("{{count}}",String(J));else Y=Z.other.replace("{{count}}",String(J));if(X.addSuffix)if(X.comparison&&X.comparison>0)return"\u0641\u064A \u062E\u0644\u0627\u0644 "+Y;else return"\u0645\u0646\u0630 "+Y;return Y};function x(C){return function(){var H=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=H.width?String(H.width):C.defaultWidth,X=C.formats[J]||C.formats[C.defaultWidth];return X}}var V={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},M={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},R={full:"{{date}} '\u0639\u0646\u062F' {{time}}",long:"{{date}} '\u0639\u0646\u062F' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},L={date:x({formats:V,defaultWidth:"full"}),time:x({formats:M,defaultWidth:"full"}),dateTime:x({formats:R,defaultWidth:"full"})},j={lastWeek:"'\u0623\u062E\u0631' eeee '\u0639\u0646\u062F' p",yesterday:"'\u0623\u0645\u0633 \u0639\u0646\u062F' p",today:"'\u0627\u0644\u064A\u0648\u0645 \u0639\u0646\u062F' p",tomorrow:"'\u063A\u062F\u0627\u064B \u0639\u0646\u062F' p",nextWeek:"eeee '\u0639\u0646\u062F' p",other:"P"},w=function C(H,J,X,Z){return j[H]};function O(C){return function(H,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Z;if(X==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,B=J!==null&&J!==void 0&&J.width?String(J.width):Y;Z=C.formattingValues[B]||C.formattingValues[Y]}else{var T=C.defaultWidth,$=J!==null&&J!==void 0&&J.width?String(J.width):C.defaultWidth;Z=C.values[$]||C.values[T]}var U=C.argumentCallback?C.argumentCallback(H):H;return Z[U]}}var _={narrow:["\u0642","\u0628"],abbreviated:["\u0642.\u0645.","\u0628.\u0645."],wide:["\u0642\u0628\u0644 \u0627\u0644\u0645\u064A\u0644\u0627\u062F","\u0628\u0639\u062F \u0627\u0644\u0645\u064A\u0644\u0627\u062F"]},f={narrow:["1","2","3","4"],abbreviated:["\u06311","\u06312","\u06313","\u06314"],wide:["\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0623\u0648\u0644","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0646\u064A","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0644\u062B","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0631\u0627\u0628\u0639"]},F={narrow:["\u064A","\u0641","\u0645","\u0623","\u0645","\u064A","\u064A","\u063A","\u0634","\u0623","\u0646","\u062F"],abbreviated:["\u064A\u0646\u0627","\u0641\u0628\u0631","\u0645\u0627\u0631\u0633","\u0623\u0628\u0631\u064A\u0644","\u0645\u0627\u064A","\u064A\u0648\u0646\u0640","\u064A\u0648\u0644\u0640","\u063A\u0634\u062A","\u0634\u062A\u0646\u0640","\u0623\u0643\u062A\u0640","\u0646\u0648\u0646\u0640","\u062F\u062C\u0646\u0640"],wide:["\u064A\u0646\u0627\u064A\u0631","\u0641\u0628\u0631\u0627\u064A\u0631","\u0645\u0627\u0631\u0633","\u0623\u0628\u0631\u064A\u0644","\u0645\u0627\u064A","\u064A\u0648\u0646\u064A\u0648","\u064A\u0648\u0644\u064A\u0648\u0632","\u063A\u0634\u062A","\u0634\u062A\u0646\u0628\u0631","\u0623\u0643\u062A\u0648\u0628\u0631","\u0646\u0648\u0646\u0628\u0631","\u062F\u062C\u0646\u0628\u0631"]},v={narrow:["\u062D","\u0646","\u062B","\u0631","\u062E","\u062C","\u0633"],short:["\u0623\u062D\u062F","\u0627\u062B\u0646\u064A\u0646","\u062B\u0644\u0627\u062B\u0627\u0621","\u0623\u0631\u0628\u0639\u0627\u0621","\u062E\u0645\u064A\u0633","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],abbreviated:["\u0623\u062D\u062F","\u0627\u062B\u0646\u0640","\u062B\u0644\u0627","\u0623\u0631\u0628\u0640","\u062E\u0645\u064A\u0640","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],wide:["\u0627\u0644\u0623\u062D\u062F","\u0627\u0644\u0625\u062B\u0646\u064A\u0646","\u0627\u0644\u062B\u0644\u0627\u062B\u0627\u0621","\u0627\u0644\u0623\u0631\u0628\u0639\u0627\u0621","\u0627\u0644\u062E\u0645\u064A\u0633","\u0627\u0644\u062C\u0645\u0639\u0629","\u0627\u0644\u0633\u0628\u062A"]},P={narrow:{am:"\u0635",pm:"\u0645",midnight:"\u0646",noon:"\u0638",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u0627\u064B",night:"\u0644\u064A\u0644\u0627\u064B"},abbreviated:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u0627\u064B",night:"\u0644\u064A\u0644\u0627\u064B"},wide:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0645\u0633\u0627\u0621\u0627\u064B",night:"\u0644\u064A\u0644\u0627\u064B"}},k={narrow:{am:"\u0635",pm:"\u0645",midnight:"\u0646",noon:"\u0638",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0640\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"},abbreviated:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"},wide:{am:"\u0635",pm:"\u0645",midnight:"\u0646\u0635\u0641 \u0627\u0644\u0644\u064A\u0644",noon:"\u0638\u0647\u0631",morning:"\u0635\u0628\u0627\u062D\u0627\u064B",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0640\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644"}},b=function C(H){return String(H)},h={ordinalNumber:b,era:O({values:_,defaultWidth:"wide"}),quarter:O({values:f,defaultWidth:"wide",argumentCallback:function C(H){return Number(H)-1}}),month:O({values:F,defaultWidth:"wide"}),day:O({values:v,defaultWidth:"wide"}),dayPeriod:O({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function m(C){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=H.match(C.matchPattern);if(!X)return null;var Z=X[0],Y=H.match(C.parsePattern);if(!Y)return null;var B=C.valueCallback?C.valueCallback(Y[0]):Y[0];B=J.valueCallback?J.valueCallback(B):B;var T=H.slice(Z.length);return{value:B,rest:T}}}function Q(C){return function(H){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Z=X&&C.matchPatterns[X]||C.matchPatterns[C.defaultMatchWidth],Y=H.match(Z);if(!Y)return null;var B=Y[0],T=X&&C.parsePatterns[X]||C.parsePatterns[C.defaultParseWidth],$=Array.isArray(T)?c(T,function(E){return E.test(B)}):y(T,function(E){return E.test(B)}),U;U=C.valueCallback?C.valueCallback($):$,U=J.valueCallback?J.valueCallback(U):U;var HC=H.slice(B.length);return{value:U,rest:HC}}}function y(C,H){for(var J in C)if(Object.prototype.hasOwnProperty.call(C,J)&&H(C[J]))return J;return}function c(C,H){for(var J=0;J<C.length;J++)if(H(C[J]))return J;return}var d=/^(\d+)(th|st|nd|rd)?/i,p=/\d+/i,g={narrow:/^(ق|ب)/i,abbreviated:/^(ق\.?\s?م\.?|ق\.?\s?م\.?\s?|a\.?\s?d\.?|c\.?\s?)/i,wide:/^(قبل الميلاد|قبل الميلاد|بعد الميلاد|بعد الميلاد)/i},l={any:[/^قبل/i,/^بعد/i]},u={narrow:/^[1234]/i,abbreviated:/^ر[1234]/i,wide:/^الربع [1234]/i},i={any:[/1/i,/2/i,/3/i,/4/i]},n={narrow:/^[يفمأمسند]/i,abbreviated:/^(ين|ف|مار|أب|ماي|يون|يول|غش|شت|أك|ن|د)/i,wide:/^(ين|ف|مار|أب|ماي|يون|يول|غش|شت|أك|ن|د)/i},s={narrow:[/^ي/i,/^ف/i,/^م/i,/^أ/i,/^م/i,/^ي/i,/^ي/i,/^غ/i,/^ش/i,/^أ/i,/^ن/i,/^د/i],any:[/^ين/i,/^فب/i,/^مار/i,/^أب/i,/^ماي/i,/^يون/i,/^يول/i,/^غشت/i,/^ش/i,/^أك/i,/^ن/i,/^د/i]},o={narrow:/^[حنثرخجس]/i,short:/^(أحد|إثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,abbreviated:/^(أحد|إثن|ثلا|أرب|خمي|جمعة|سبت)/i,wide:/^(الأحد|الإثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)/i},r={narrow:[/^ح/i,/^ن/i,/^ث/i,/^ر/i,/^خ/i,/^ج/i,/^س/i],wide:[/^الأحد/i,/^الإثنين/i,/^الثلاثاء/i,/^الأربعاء/i,/^الخميس/i,/^الجمعة/i,/^السبت/i],any:[/^أح/i,/^إث/i,/^ث/i,/^أر/i,/^خ/i,/^ج/i,/^س/i]},e={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},a={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},t={ordinalNumber:m({matchPattern:d,parsePattern:p,valueCallback:function C(H){return parseInt(H,10)}}),era:Q({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),quarter:Q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function C(H){return Number(H)+1}}),month:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:Q({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:a,defaultParseWidth:"any"})},CC={code:"ar-MA",formatDistance:S,formatLong:L,formatRelative:w,localize:h,match:t,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=q(q({},window.dateFns),{},{locale:q(q({},(K=window.dateFns)===null||K===void 0?void 0:K.locale),{},{arMA:CC})})})();

//# debugId=1C73714101582B9564756E2164756E21
