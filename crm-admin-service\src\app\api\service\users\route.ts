import { NextRequest, NextResponse } from 'next/server';
import { withServiceAuth, getAuthenticatedService } from '@/lib/service-auth';
import { prisma } from '@/lib/db';

export const GET = withServiceAuth(async (request: NextRequest) => {
  try {
    const authenticatedService = getAuthenticatedService(request);
    if (!authenticatedService) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Service authentication required',
        },
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const serviceName = searchParams.get('service');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    const where = serviceName ? { serviceName } : {};

    const [users, total] = await Promise.all([
      prisma.userManagement.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          managedByUser: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      }),
      prisma.userManagement.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: users,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        requestedBy: authenticatedService,
      },
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'FETCH_USERS_ERROR',
        message: 'Failed to fetch users',
      },
    }, { status: 500 });
  }
});

export const POST = withServiceAuth(async (request: NextRequest) => {
  try {
    const authenticatedService = getAuthenticatedService(request);
    if (!authenticatedService) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Service authentication required',
        },
      }, { status: 401 });
    }

    const body = await request.json();
    const { serviceName, serviceUserId, email, role, isActive = true } = body;

    // TODO: Get the current admin user ID from session/auth
    const managedBy = 'temp-admin-user-id'; // This should come from authentication

    const user = await prisma.userManagement.create({
      data: {
        serviceName,
        serviceUserId,
        email,
        role,
        isActive,
        managedBy,
      },
      include: {
        managedByUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // Create audit log entry
    await prisma.auditLog.create({
      data: {
        userId: managedBy,
        action: 'CREATE_USER_MANAGEMENT',
        resourceType: 'UserManagement',
        resourceId: user.id,
        newValues: user,
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    });

    return NextResponse.json({
      success: true,
      data: user,
      meta: {
        createdBy: authenticatedService,
      },
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'CREATE_USER_ERROR',
        message: 'Failed to create user',
      },
    }, { status: 500 });
  }
});
