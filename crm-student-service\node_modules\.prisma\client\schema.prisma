// Student Service Database Schema
// Student portal, assignment viewing, progress tracking, and resource access

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("STUDENT_DATABASE_URL")
}

// Student Status Enum
enum StudentStatus {
  active
  inactive
  graduated
  suspended
}

// Submission Status Enum
enum SubmissionStatus {
  submitted
  graded
  returned
  late
}

// Student Users Table
model StudentUser {
  id           String   @id @default(cuid())
  email        String   @unique
  passwordHash String   @map("password_hash")
  firstName    String   @map("first_name")
  lastName     String   @map("last_name")
  phone        String?
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  student Student?

  @@map("student_users")
}

// Students Table (Extended Student Information)
model Student {
  id               String        @id @default(cuid())
  studentUserId    String        @unique @map("student_user_id")
  studentId        String        @unique @map("student_id") // STU123456 format
  dateOfBirth      DateTime?     @map("date_of_birth") @db.Date
  emergencyContact Json?         @map("emergency_contact")
  enrollmentDate   DateTime      @map("enrollment_date") @db.Date
  currentLevel     String?       @map("current_level")
  status           StudentStatus @default(active)
  createdAt        DateTime      @default(now()) @map("created_at")
  updatedAt        DateTime      @updatedAt @map("updated_at")

  // Relations
  studentUser    StudentUser             @relation(fields: [studentUserId], references: [id], onDelete: Cascade)
  progress       StudentProgress[]
  submissions    AssignmentSubmission[]
  resourceAccess StudentResourceAccess[]
  schedules      StudentSchedule[]

  @@map("students")
}

// Student Progress Table
model StudentProgress {
  id                 String    @id @default(cuid())
  studentId          String    @map("student_id")
  assignmentId       String    @map("assignment_id") // Reference to staff service
  progressPercentage Int       @map("progress_percentage") @db.SmallInt
  grade              String?
  feedback           String?
  submittedAt        DateTime? @map("submitted_at")
  gradedAt           DateTime? @map("graded_at")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@unique([studentId, assignmentId])
  @@map("student_progress")
}

// Assignment Submissions Table
model AssignmentSubmission {
  id                String           @id @default(cuid())
  studentId         String           @map("student_id")
  assignmentId      String           @map("assignment_id") // Reference to staff service
  submissionContent String?          @map("submission_content")
  fileUrl           String?          @map("file_url")
  submittedAt       DateTime         @default(now()) @map("submitted_at")
  status            SubmissionStatus @default(submitted)
  teacherFeedback   String?          @map("teacher_feedback")
  grade             String?
  gradedAt          DateTime?        @map("graded_at")

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("assignment_submissions")
}

// Student Resource Access Tracking
model StudentResourceAccess {
  id             String   @id @default(cuid())
  studentId      String   @map("student_id")
  resourceId     String   @map("resource_id") // Reference to staff service
  accessedAt     DateTime @default(now()) @map("accessed_at")
  accessDuration Int?     @map("access_duration") // in minutes

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("student_resource_access")
}

// Student Schedule (Cached from staff service)
model StudentSchedule {
  id            String    @id @default(cuid())
  studentId     String    @map("student_id")
  groupId       String    @map("group_id") // Reference to staff service
  courseTitle   String    @map("course_title")
  teacherName   String    @map("teacher_name")
  cabinetNumber String    @map("cabinet_number")
  schedule      Json // days and times
  startDate     DateTime  @map("start_date") @db.Date
  endDate       DateTime? @map("end_date") @db.Date
  isActive      Boolean   @default(true) @map("is_active")
  lastUpdated   DateTime  @default(now()) @map("last_updated")

  // Relations
  student Student @relation(fields: [studentId], references: [id], onDelete: Cascade)

  @@map("student_schedules")
}
