[{"name": "generate-buildid", "duration": 600, "timestamp": 277925755777, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750479980231, "traceId": "63be10d189c00763"}, {"name": "load-custom-routes", "duration": 756, "timestamp": 277925756549, "id": 5, "parentId": 1, "tags": {}, "startTime": 1750479980231, "traceId": "63be10d189c00763"}, {"name": "create-dist-dir", "duration": 2040, "timestamp": 277925975032, "id": 6, "parentId": 1, "tags": {}, "startTime": 1750479980450, "traceId": "63be10d189c00763"}, {"name": "collect-pages", "duration": 5119, "timestamp": 277926171375, "id": 7, "parentId": 1, "tags": {}, "startTime": 1750479980646, "traceId": "63be10d189c00763"}, {"name": "create-pages-mapping", "duration": 348, "timestamp": 277926178907, "id": 8, "parentId": 1, "tags": {}, "startTime": 1750479980654, "traceId": "63be10d189c00763"}, {"name": "collect-app-paths", "duration": 3200, "timestamp": 277926179323, "id": 9, "parentId": 1, "tags": {}, "startTime": 1750479980654, "traceId": "63be10d189c00763"}, {"name": "create-app-mapping", "duration": 1694, "timestamp": 277926182582, "id": 10, "parentId": 1, "tags": {}, "startTime": 1750479980657, "traceId": "63be10d189c00763"}, {"name": "public-dir-conflict-check", "duration": 1380, "timestamp": 277926185091, "id": 11, "parentId": 1, "tags": {}, "startTime": 1750479980660, "traceId": "63be10d189c00763"}, {"name": "generate-routes-manifest", "duration": 5125, "timestamp": 277926186909, "id": 12, "parentId": 1, "tags": {}, "startTime": 1750479980662, "traceId": "63be10d189c00763"}, {"name": "next-build", "duration": 6746045, "timestamp": 277925341311, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "15.3.4", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1750479979816, "traceId": "63be10d189c00763"}]