(() => {
var _window$dateFns;function _typeof(o) {"@babel/helpers - typeof";return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;}, _typeof(o);}function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, "string");return "symbol" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if ("object" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || "default");if ("object" != _typeof(i)) return i;throw new TypeError("@@toPrimitive must return a primitive value.");}return ("string" === r ? String : Number)(t);}var __defProp = Object.defineProperty;
var __export = function __export(target, all) {
  for (var name in all)
  __defProp(target, name, {
    get: all[name],
    enumerable: true,
    configurable: true,
    set: function set(newValue) {return all[name] = function () {return newValue;};}
  });
};

// lib/locale/ckb/_lib/formatDistance.js
var formatDistanceLocale = {
  lessThanXSeconds: {
    one: "\u06A9\u06D5\u0645\u062A\u0631 \u0644\u06D5 \u06CC\u06D5\u06A9 \u0686\u0631\u06A9\u06D5",
    other: "\u06A9\u06D5\u0645\u062A\u0631 \u0644\u06D5 {{count}} \u0686\u0631\u06A9\u06D5"
  },
  xSeconds: {
    one: "1 \u0686\u0631\u06A9\u06D5",
    other: "{{count}} \u0686\u0631\u06A9\u06D5"
  },
  halfAMinute: "\u0646\u06CC\u0648 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631",
  lessThanXMinutes: {
    one: "\u06A9\u06D5\u0645\u062A\u0631 \u0644\u06D5 \u06CC\u06D5\u06A9 \u062E\u0648\u0644\u06D5\u06A9",
    other: "\u06A9\u06D5\u0645\u062A\u0631 \u0644\u06D5 {{count}} \u062E\u0648\u0644\u06D5\u06A9"
  },
  xMinutes: {
    one: "1 \u062E\u0648\u0644\u06D5\u06A9",
    other: "{{count}} \u062E\u0648\u0644\u06D5\u06A9"
  },
  aboutXHours: {
    one: "\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC 1 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631",
    other: "\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC {{count}} \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631"
  },
  xHours: {
    one: "1 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631",
    other: "{{count}} \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631"
  },
  xDays: {
    one: "1 \u0695\u06C6\u0698",
    other: "{{count}} \u0698\u06C6\u0698"
  },
  aboutXWeeks: {
    one: "\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC 1 \u0647\u06D5\u0641\u062A\u06D5",
    other: "\u062F\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC {{count}} \u0647\u06D5\u0641\u062A\u06D5"
  },
  xWeeks: {
    one: "1 \u0647\u06D5\u0641\u062A\u06D5",
    other: "{{count}} \u0647\u06D5\u0641\u062A\u06D5"
  },
  aboutXMonths: {
    one: "\u062F\u0627\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC 1 \u0645\u0627\u0646\u06AF",
    other: "\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC {{count}} \u0645\u0627\u0646\u06AF"
  },
  xMonths: {
    one: "1 \u0645\u0627\u0646\u06AF",
    other: "{{count}} \u0645\u0627\u0646\u06AF"
  },
  aboutXYears: {
    one: "\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC  1 \u0633\u0627\u06B5",
    other: "\u062F\u06D5\u0648\u0631\u0648\u0628\u06D5\u0631\u06CC {{count}} \u0633\u0627\u06B5"
  },
  xYears: {
    one: "1 \u0633\u0627\u06B5",
    other: "{{count}} \u0633\u0627\u06B5"
  },
  overXYears: {
    one: "\u0632\u06CC\u0627\u062A\u0631 \u0644\u06D5 \u0633\u0627\u06B5\u06CE\u06A9",
    other: "\u0632\u06CC\u0627\u062A\u0631 \u0644\u06D5 {{count}} \u0633\u0627\u06B5"
  },
  almostXYears: {
    one: "\u0628\u06D5\u0646\u0632\u06CC\u06A9\u06D5\u06CC\u06CC \u0633\u0627\u06B5\u06CE\u06A9  ",
    other: "\u0628\u06D5\u0646\u0632\u06CC\u06A9\u06D5\u06CC\u06CC {{count}} \u0633\u0627\u06B5"
  }
};
var formatDistance = function formatDistance(token, count, options) {
  var result;
  var tokenValue = formatDistanceLocale[token];
  if (typeof tokenValue === "string") {
    result = tokenValue;
  } else if (count === 1) {
    result = tokenValue.one;
  } else {
    result = tokenValue.other.replace("{{count}}", count.toString());
  }
  if (options !== null && options !== void 0 && options.addSuffix) {
    if (options.comparison && options.comparison > 0) {
      return "\u0644\u06D5 \u0645\u0627\u0648\u06D5\u06CC " + result + "\u062F\u0627";
    } else {
      return result + "\u067E\u06CE\u0634 \u0626\u06CE\u0633\u062A\u0627";
    }
  }
  return result;
};

// lib/locale/_lib/buildFormatLongFn.js
function buildFormatLongFn(args) {
  return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
    var width = options.width ? String(options.width) : args.defaultWidth;
    var format = args.formats[width] || args.formats[args.defaultWidth];
    return format;
  };
}

// lib/locale/ckb/_lib/formatLong.js
var dateFormats = {
  full: "EEEE, MMMM do, y",
  long: "MMMM do, y",
  medium: "MMM d, y",
  short: "MM/dd/yyyy"
};
var timeFormats = {
  full: "h:mm:ss a zzzz",
  long: "h:mm:ss a z",
  medium: "h:mm:ss a",
  short: "h:mm a"
};
var dateTimeFormats = {
  full: "{{date}} '\u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' {{time}}",
  long: "{{date}} '\u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' {{time}}",
  medium: "{{date}}, {{time}}",
  short: "{{date}}, {{time}}"
};
var formatLong = {
  date: buildFormatLongFn({
    formats: dateFormats,
    defaultWidth: "full"
  }),
  time: buildFormatLongFn({
    formats: timeFormats,
    defaultWidth: "full"
  }),
  dateTime: buildFormatLongFn({
    formats: dateTimeFormats,
    defaultWidth: "full"
  })
};

// lib/locale/ckb/_lib/formatRelative.js
var formatRelativeLocale = {
  lastWeek: "'\u0647\u06D5\u0641\u062A\u06D5\u06CC \u0695\u0627\u0628\u0631\u062F\u0648\u0648' eeee '\u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",
  yesterday: "'\u062F\u0648\u06CE\u0646\u06CE \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",
  today: "'\u0626\u06D5\u0645\u0695\u06C6 \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",
  tomorrow: "'\u0628\u06D5\u06CC\u0627\u0646\u06CC \u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",
  nextWeek: "eeee '\u06A9\u0627\u062A\u0698\u0645\u06CE\u0631' p",
  other: "P"
};
var formatRelative = function formatRelative(token, _date, _baseDate, _options) {return formatRelativeLocale[token];};

// lib/locale/_lib/buildLocalizeFn.js
function buildLocalizeFn(args) {
  return function (value, options) {
    var context = options !== null && options !== void 0 && options.context ? String(options.context) : "standalone";
    var valuesArray;
    if (context === "formatting" && args.formattingValues) {
      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;
      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;
      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];
    } else {
      var _defaultWidth = args.defaultWidth;
      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;
      valuesArray = args.values[_width] || args.values[_defaultWidth];
    }
    var index = args.argumentCallback ? args.argumentCallback(value) : value;
    return valuesArray[index];
  };
}

// lib/locale/ckb/_lib/localize.js
var eraValues = {
  narrow: ["\u067E", "\u062F"],
  abbreviated: ["\u067E-\u0632", "\u062F-\u0632"],
  wide: ["\u067E\u06CE\u0634 \u0632\u0627\u06CC\u0646", "\u062F\u0648\u0627\u06CC \u0632\u0627\u06CC\u0646"]
};
var quarterValues = {
  narrow: ["1", "2", "3", "4"],
  abbreviated: ["\u06861\u0645", "\u06862\u0645", "\u06863\u0645", "\u06864\u0645"],
  wide: ["\u0686\u0627\u0631\u06D5\u06AF\u06CC \u06CC\u06D5\u06A9\u06D5\u0645", "\u0686\u0627\u0631\u06D5\u06AF\u06CC \u062F\u0648\u0648\u06D5\u0645", "\u0686\u0627\u0631\u06D5\u06AF\u06CC \u0633\u06CE\u06CC\u06D5\u0645", "\u0686\u0627\u0631\u06D5\u06AF\u06CC \u0686\u0648\u0627\u0631\u06D5\u0645"]
};
var monthValues = {
  narrow: [
  "\u06A9-\u062F",
  "\u0634",
  "\u0626\u0627",
  "\u0646",
  "\u0645",
  "\u062D",
  "\u062A",
  "\u0626\u0627",
  "\u0626\u06D5",
  "\u062A\u0634-\u06CC",
  "\u062A\u0634-\u062F",
  "\u06A9-\u06CC"],

  abbreviated: [
  "\u06A9\u0627\u0646-\u062F\u0648\u0648",
  "\u0634\u0648\u0628",
  "\u0626\u0627\u062F",
  "\u0646\u06CC\u0633",
  "\u0645\u0627\u06CC\u0633",
  "\u062D\u0648\u0632",
  "\u062A\u06D5\u0645",
  "\u0626\u0627\u0628",
  "\u0626\u06D5\u0644",
  "\u062A\u0634-\u06CC\u06D5\u06A9",
  "\u062A\u0634-\u062F\u0648\u0648",
  "\u06A9\u0627\u0646-\u06CC\u06D5\u06A9"],

  wide: [
  "\u06A9\u0627\u0646\u0648\u0648\u0646\u06CC \u062F\u0648\u0648\u06D5\u0645",
  "\u0634\u0648\u0628\u0627\u062A",
  "\u0626\u0627\u062F\u0627\u0631",
  "\u0646\u06CC\u0633\u0627\u0646",
  "\u0645\u0627\u06CC\u0633",
  "\u062D\u0648\u0632\u06D5\u06CC\u0631\u0627\u0646",
  "\u062A\u06D5\u0645\u0645\u0648\u0632",
  "\u0626\u0627\u0628",
  "\u0626\u06D5\u06CC\u0644\u0648\u0644",
  "\u062A\u0634\u0631\u06CC\u0646\u06CC \u06CC\u06D5\u06A9\u06D5\u0645",
  "\u062A\u0634\u0631\u06CC\u0646\u06CC \u062F\u0648\u0648\u06D5\u0645",
  "\u06A9\u0627\u0646\u0648\u0648\u0646\u06CC \u06CC\u06D5\u06A9\u06D5\u0645"]

};
var dayValues = {
  narrow: ["\u06CC-\u0634", "\u062F-\u0634", "\u0633-\u0634", "\u0686-\u0634", "\u067E-\u0634", "\u0647\u06D5", "\u0634"],
  short: ["\u06CC\u06D5-\u0634\u06D5", "\u062F\u0648\u0648-\u0634\u06D5", "\u0633\u06CE-\u0634\u06D5", "\u0686\u0648-\u0634\u06D5", "\u067E\u06CE-\u0634\u06D5", "\u0647\u06D5\u06CC", "\u0634\u06D5"],
  abbreviated: [
  "\u06CC\u06D5\u06A9-\u0634\u06D5\u0645",
  "\u062F\u0648\u0648-\u0634\u06D5\u0645",
  "\u0633\u06CE-\u0634\u06D5\u0645",
  "\u0686\u0648\u0627\u0631-\u0634\u06D5\u0645",
  "\u067E\u06CE\u0646\u062C-\u0634\u06D5\u0645",
  "\u0647\u06D5\u06CC\u0646\u06CC",
  "\u0634\u06D5\u0645\u06D5"],

  wide: [
  "\u06CC\u06D5\u06A9 \u0634\u06D5\u0645\u06D5",
  "\u062F\u0648\u0648 \u0634\u06D5\u0645\u06D5",
  "\u0633\u06CE \u0634\u06D5\u0645\u06D5",
  "\u0686\u0648\u0627\u0631 \u0634\u06D5\u0645\u06D5",
  "\u067E\u06CE\u0646\u062C \u0634\u06D5\u0645\u06D5",
  "\u0647\u06D5\u06CC\u0646\u06CC",
  "\u0634\u06D5\u0645\u06D5"]

};
var dayPeriodValues = {
  narrow: {
    am: "\u067E",
    pm: "\u062F",
    midnight: "\u0646-\u0634",
    noon: "\u0646",
    morning: "\u0628\u06D5\u06CC\u0627\u0646\u06CC",
    afternoon: "\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",
    evening: "\u0626\u06CE\u0648\u0627\u0631\u06D5",
    night: "\u0634\u06D5\u0648"
  },
  abbreviated: {
    am: "\u067E-\u0646",
    pm: "\u062F-\u0646",
    midnight: "\u0646\u06CC\u0648\u06D5 \u0634\u06D5\u0648",
    noon: "\u0646\u06CC\u0648\u06D5\u0695\u06C6",
    morning: "\u0628\u06D5\u06CC\u0627\u0646\u06CC",
    afternoon: "\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",
    evening: "\u0626\u06CE\u0648\u0627\u0631\u06D5",
    night: "\u0634\u06D5\u0648"
  },
  wide: {
    am: "\u067E\u06CE\u0634 \u0646\u06CC\u0648\u06D5\u0695\u06C6",
    pm: "\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",
    midnight: "\u0646\u06CC\u0648\u06D5 \u0634\u06D5\u0648",
    noon: "\u0646\u06CC\u0648\u06D5\u0695\u06C6",
    morning: "\u0628\u06D5\u06CC\u0627\u0646\u06CC",
    afternoon: "\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",
    evening: "\u0626\u06CE\u0648\u0627\u0631\u06D5",
    night: "\u0634\u06D5\u0648"
  }
};
var formattingDayPeriodValues = {
  narrow: {
    am: "\u067E",
    pm: "\u062F",
    midnight: "\u0646-\u0634",
    noon: "\u0646",
    morning: "\u0644\u06D5 \u0628\u06D5\u06CC\u0627\u0646\u06CC\u062F\u0627",
    afternoon: "\u0644\u06D5 \u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6\u062F\u0627",
    evening: "\u0644\u06D5 \u0626\u06CE\u0648\u0627\u0631\u06D5\u062F\u0627",
    night: "\u0644\u06D5 \u0634\u06D5\u0648\u062F\u0627"
  },
  abbreviated: {
    am: "\u067E-\u0646",
    pm: "\u062F-\u0646",
    midnight: "\u0646\u06CC\u0648\u06D5 \u0634\u06D5\u0648",
    noon: "\u0646\u06CC\u0648\u06D5\u0695\u06C6",
    morning: "\u0644\u06D5 \u0628\u06D5\u06CC\u0627\u0646\u06CC\u062F\u0627",
    afternoon: "\u0644\u06D5 \u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6\u062F\u0627",
    evening: "\u0644\u06D5 \u0626\u06CE\u0648\u0627\u0631\u06D5\u062F\u0627",
    night: "\u0644\u06D5 \u0634\u06D5\u0648\u062F\u0627"
  },
  wide: {
    am: "\u067E\u06CE\u0634 \u0646\u06CC\u0648\u06D5\u0695\u06C6",
    pm: "\u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6",
    midnight: "\u0646\u06CC\u0648\u06D5 \u0634\u06D5\u0648",
    noon: "\u0646\u06CC\u0648\u06D5\u0695\u06C6",
    morning: "\u0644\u06D5 \u0628\u06D5\u06CC\u0627\u0646\u06CC\u062F\u0627",
    afternoon: "\u0644\u06D5 \u062F\u0648\u0627\u06CC \u0646\u06CC\u0648\u06D5\u0695\u06C6\u062F\u0627",
    evening: "\u0644\u06D5 \u0626\u06CE\u0648\u0627\u0631\u06D5\u062F\u0627",
    night: "\u0644\u06D5 \u0634\u06D5\u0648\u062F\u0627"
  }
};
var ordinalNumber = function ordinalNumber(dirtyNumber, _options) {
  return String(dirtyNumber);
};
var localize = {
  ordinalNumber: ordinalNumber,
  era: buildLocalizeFn({
    values: eraValues,
    defaultWidth: "wide"
  }),
  quarter: buildLocalizeFn({
    values: quarterValues,
    defaultWidth: "wide",
    argumentCallback: function argumentCallback(quarter) {return quarter - 1;}
  }),
  month: buildLocalizeFn({
    values: monthValues,
    defaultWidth: "wide"
  }),
  day: buildLocalizeFn({
    values: dayValues,
    defaultWidth: "wide"
  }),
  dayPeriod: buildLocalizeFn({
    values: dayPeriodValues,
    defaultWidth: "wide",
    formattingValues: formattingDayPeriodValues,
    defaultFormattingWidth: "wide"
  })
};

// lib/locale/_lib/buildMatchFn.js
function buildMatchFn(args) {
  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var width = options.width;
    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];
    var matchResult = string.match(matchPattern);
    if (!matchResult) {
      return null;
    }
    var matchedString = matchResult[0];
    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];
    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});
    var value;
    value = args.valueCallback ? args.valueCallback(key) : key;
    value = options.valueCallback ? options.valueCallback(value) : value;
    var rest = string.slice(matchedString.length);
    return { value: value, rest: rest };
  };
}
function findKey(object, predicate) {
  for (var key in object) {
    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {
      return key;
    }
  }
  return;
}
function findIndex(array, predicate) {
  for (var key = 0; key < array.length; key++) {
    if (predicate(array[key])) {
      return key;
    }
  }
  return;
}

// lib/locale/_lib/buildMatchPatternFn.js
function buildMatchPatternFn(args) {
  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    var matchResult = string.match(args.matchPattern);
    if (!matchResult)
    return null;
    var matchedString = matchResult[0];
    var parseResult = string.match(args.parsePattern);
    if (!parseResult)
    return null;
    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];
    value = options.valueCallback ? options.valueCallback(value) : value;
    var rest = string.slice(matchedString.length);
    return { value: value, rest: rest };
  };
}

// lib/locale/ckb/_lib/match.js
var matchOrdinalNumberPattern = /^(\d+)(th|st|nd|rd)?/i;
var parseOrdinalNumberPattern = /\d+/i;
var matchEraPatterns = {
  narrow: /^(پ|د)/i,
  abbreviated: /^(پ-ز|د.ز)/i,
  wide: /^(پێش زاین| دوای زاین)/i
};
var parseEraPatterns = {
  any: [/^د/g, /^پ/g]
};
var matchQuarterPatterns = {
  narrow: /^[1234]/i,
  abbreviated: /^م[1234]چ/i,
  wide: /^(یەکەم|دووەم|سێیەم| چوارەم) (چارەگی)? quarter/i
};
var parseQuarterPatterns = {
  wide: [/چارەگی یەکەم/, /چارەگی دووەم/, /چارەگی سيیەم/, /چارەگی چوارەم/],
  any: [/1/i, /2/i, /3/i, /4/i]
};
var matchMonthPatterns = {
  narrow: /^(ک-د|ش|ئا|ن|م|ح|ت|ئە|تش-ی|تش-د|ک-ی)/i,
  abbreviated: /^(کان-دوو|شوب|ئاد|نیس|مایس|حوز|تەم|ئاب|ئەل|تش-یەک|تش-دوو|کان-یەک)/i,
  wide: /^(کانوونی دووەم|شوبات|ئادار|نیسان|مایس|حوزەیران|تەمموز|ئاب|ئەیلول|تشرینی یەکەم|تشرینی دووەم|کانوونی یەکەم)/i
};
var parseMonthPatterns = {
  narrow: [
  /^ک-د/i,
  /^ش/i,
  /^ئا/i,
  /^ن/i,
  /^م/i,
  /^ح/i,
  /^ت/i,
  /^ئا/i,
  /^ئە/i,
  /^تش-ی/i,
  /^تش-د/i,
  /^ک-ی/i],

  any: [
  /^کان-دوو/i,
  /^شوب/i,
  /^ئاد/i,
  /^نیس/i,
  /^مایس/i,
  /^حوز/i,
  /^تەم/i,
  /^ئاب/i,
  /^ئەل/i,
  /^تش-یەک/i,
  /^تش-دوو/i,
  /^|کان-یەک/i]

};
var matchDayPatterns = {
  narrow: /^(ش|ی|د|س|چ|پ|هە)/i,
  short: /^(یە-شە|دوو-شە|سێ-شە|چو-شە|پێ-شە|هە|شە)/i,
  abbreviated: /^(یەک-شەم|دوو-شەم|سێ-شەم|چوار-شەم|پێنخ-شەم|هەینی|شەمە)/i,
  wide: /^(یەک شەمە|دوو شەمە|سێ شەمە|چوار شەمە|پێنج شەمە|هەینی|شەمە)/i
};
var parseDayPatterns = {
  narrow: [/^s/i, /^m/i, /^t/i, /^w/i, /^t/i, /^f/i, /^s/i],
  any: [/^su/i, /^m/i, /^tu/i, /^w/i, /^th/i, /^f/i, /^sa/i]
};
var matchDayPeriodPatterns = {
  narrow: /^(پ|د|ن-ش|ن| (بەیانی|دوای نیوەڕۆ|ئێوارە|شەو))/i,
  abbreviated: /^(پ-ن|د-ن|نیوە شەو|نیوەڕۆ|بەیانی|دوای نیوەڕۆ|ئێوارە|شەو)/,
  wide: /^(پێش نیوەڕۆ|دوای نیوەڕۆ|نیوەڕۆ|نیوە شەو|لەبەیانیدا|لەدواینیوەڕۆدا|لە ئێوارەدا|لە شەودا)/,
  any: /^(پ|د|بەیانی|نیوەڕۆ|ئێوارە|شەو)/
};
var parseDayPeriodPatterns = {
  any: {
    am: /^د/i,
    pm: /^پ/i,
    midnight: /^ن-ش/i,
    noon: /^ن/i,
    morning: /بەیانی/i,
    afternoon: /دواینیوەڕۆ/i,
    evening: /ئێوارە/i,
    night: /شەو/i
  }
};
var match = {
  ordinalNumber: buildMatchPatternFn({
    matchPattern: matchOrdinalNumberPattern,
    parsePattern: parseOrdinalNumberPattern,
    valueCallback: function valueCallback(value) {return parseInt(value, 10);}
  }),
  era: buildMatchFn({
    matchPatterns: matchEraPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseEraPatterns,
    defaultParseWidth: "any"
  }),
  quarter: buildMatchFn({
    matchPatterns: matchQuarterPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseQuarterPatterns,
    defaultParseWidth: "any",
    valueCallback: function valueCallback(index) {return index + 1;}
  }),
  month: buildMatchFn({
    matchPatterns: matchMonthPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseMonthPatterns,
    defaultParseWidth: "any"
  }),
  day: buildMatchFn({
    matchPatterns: matchDayPatterns,
    defaultMatchWidth: "wide",
    parsePatterns: parseDayPatterns,
    defaultParseWidth: "any"
  }),
  dayPeriod: buildMatchFn({
    matchPatterns: matchDayPeriodPatterns,
    defaultMatchWidth: "any",
    parsePatterns: parseDayPeriodPatterns,
    defaultParseWidth: "any"
  })
};

// lib/locale/ckb.js
var ckb = {
  code: "ckb",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0,
    firstWeekContainsDate: 1
  }
};

// lib/locale/ckb/cdn.js
window.dateFns = _objectSpread(_objectSpread({},
window.dateFns), {}, {
  locale: _objectSpread(_objectSpread({}, (_window$dateFns =
  window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {
    ckb: ckb }) });



//# debugId=74427E9D47BF4BB164756E2164756E21

//# sourceMappingURL=cdn.js.map
})();