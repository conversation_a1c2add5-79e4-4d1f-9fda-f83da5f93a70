(()=>{var e={};e.id=974,e.ids=[974],e.modules={643:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,5814,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1870:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=r(5239),a=r(8088),l=r(8170),i=r.n(l),n=r(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2582)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}],o=["C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2582:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var t=r(7413),a=r(1120);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,r)=>r?r.toUpperCase():s.toLowerCase()),n=e=>{let s=i(e);return s.charAt(0).toUpperCase()+s.slice(1)},d=(...e)=>e.filter((e,s,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===s).join(" ").trim(),c=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=(0,a.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:r=2,absoluteStrokeWidth:t,className:l="",children:i,iconNode:n,...m},x)=>(0,a.createElement)("svg",{ref:x,...o,width:s,height:s,stroke:e,strokeWidth:t?24*Number(r)/Number(s):r,className:d("lucide",l),...!i&&!c(m)&&{"aria-hidden":"true"},...m},[...n.map(([e,s])=>(0,a.createElement)(e,s)),...Array.isArray(i)?i:[i]])),x=(e,s)=>{let r=(0,a.forwardRef)(({className:r,...t},i)=>(0,a.createElement)(m,{ref:i,iconNode:s,className:d(`lucide-${l(n(e))}`,`lucide-${e}`,r),...t}));return r.displayName=n(e),r},h=x("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),g=x("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),p=x("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]),u=x("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),v=x("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),y=x("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),j=x("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]]);function b(){return(0,t.jsxs)("div",{className:"p-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Welcome Back, Student!"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Track your progress, view assignments, and access learning resources"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h,{className:"h-8 w-8 text-blue-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending Assignments"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g,{className:"h-8 w-8 text-green-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Overall Progress"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0%"})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p,{className:"h-8 w-8 text-purple-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Current Grade"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"-"})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(u,{className:"h-8 w-8 text-orange-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Next Class"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"-"})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(h,{className:"h-5 w-5 mr-2 text-blue-600"}),"Recent Assignments"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"View and submit your assignments"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,t.jsx)(h,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No assignments available"}),(0,t.jsx)("p",{className:"text-sm",children:"New assignments will appear here"})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(g,{className:"h-5 w-5 mr-2 text-green-600"}),"Progress Overview"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Track your learning progress"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Course Progress"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"0%"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"0%"}})}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Assignment Completion"}),(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"0%"})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full",style:{width:"0%"}})})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(u,{className:"h-5 w-5 mr-2 text-purple-600"}),"Upcoming Classes"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Your class schedule"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,t.jsx)(u,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No upcoming classes"}),(0,t.jsx)("p",{className:"text-sm",children:"Your schedule will appear here"})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(v,{className:"h-5 w-5 mr-2 text-indigo-600"}),"Learning Resources"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Access course materials"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Course Materials"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Books, documents, and notes"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Practice Exercises"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Additional practice materials"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Reference Links"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"External learning resources"})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(p,{className:"h-5 w-5 mr-2 text-yellow-600"}),"Recent Grades"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Your latest assignment grades"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,t.jsx)(p,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No grades available"}),(0,t.jsx)("p",{className:"text-sm",children:"Graded assignments will appear here"})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(y,{className:"h-5 w-5 mr-2 text-red-600"}),"Quick Actions"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Common student tasks"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Submit Assignment"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Upload your completed work"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"View Feedback"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Check teacher comments"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Download Resources"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Access course materials"})]})]})})]})]}),(0,t.jsx)("div",{className:"mt-8",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(j,{className:"h-5 w-5 mr-2 text-blue-600"}),"Recent Notifications"]})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,t.jsx)(j,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No new notifications"}),(0,t.jsx)("p",{className:"text-sm",children:"Updates and announcements will appear here"})]})})]})})]})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n,metadata:()=>i});var t=r(7413),a=r(4536),l=r.n(a);r(5692);let i={title:"Student Portal - Innovative Centre CRM",description:"Student portal for assignments, progress tracking, and resource access"};function n({children:e}){return(0,t.jsx)("html",{lang:"en",children:(0,t.jsx)("body",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"flex min-h-screen",children:[(0,t.jsxs)("aside",{className:"w-64 bg-white shadow-sm border-r border-gray-200",children:[(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Student Portal"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Innovative Centre"})]}),(0,t.jsx)("nav",{className:"mt-6",children:(0,t.jsx)("div",{className:"px-3",children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(l(),{href:"/",className:"bg-blue-50 text-blue-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Dashboard"}),(0,t.jsx)(l(),{href:"/assignments",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"My Assignments"}),(0,t.jsx)(l(),{href:"/progress",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Progress Tracking"}),(0,t.jsx)(l(),{href:"/resources",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Learning Resources"}),(0,t.jsx)(l(),{href:"/schedule",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Class Schedule"}),(0,t.jsx)(l(),{href:"/grades",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Grades & Feedback"}),(0,t.jsx)(l(),{href:"/profile",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"My Profile"})]})})})]}),(0,t.jsx)("main",{className:"flex-1",children:e})]})})})}},4619:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},5385:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},5692:()=>{},6487:()=>{},8335:()=>{},8761:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,663],()=>r(1870));module.exports=t})();