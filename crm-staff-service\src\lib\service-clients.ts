// Service Clients Configuration for Staff Service

import { ServiceClientFactory, AdminServiceClient, StudentServiceClient } from 'crm-shared-types';

// Service client factory instance
const serviceClientFactory = new ServiceClientFactory({
  adminServiceUrl: process.env.ADMIN_SERVICE_URL || 'http://localhost:3001',
  staffServiceUrl: process.env.STAFF_SERVICE_URL || 'http://localhost:3002',
  studentServiceUrl: process.env.STUDENT_SERVICE_URL || 'http://localhost:3003',
  serviceName: 'crm-staff-service',
  apiKey: process.env.SERVICE_API_KEY || 'default-api-key',
});

// Export service clients
export const adminServiceClient = serviceClientFactory.getAdminClient();
export const studentServiceClient = serviceClientFactory.getStudentClient();

// Helper functions for common operations

// Convert lead to student
export async function convertLeadToStudent(leadData: any) {
  try {
    // Create student in student service
    const studentResponse = await studentServiceClient.createStudent({
      email: leadData.email,
      firstName: leadData.firstName,
      lastName: leadData.lastName,
      dateOfBirth: leadData.dateOfBirth,
      emergencyContact: leadData.emergencyContact,
      enrollmentDate: new Date(),
      currentLevel: leadData.currentLevel || 'beginner',
    });

    if (!studentResponse.success) {
      throw new Error('Failed to create student');
    }

    // Create user management record in admin service
    await adminServiceClient.createUser({
      serviceName: 'crm-student-service',
      serviceUserId: studentResponse.data.studentUserId,
      email: leadData.email,
      role: 'student',
      isActive: true,
    });

    return studentResponse.data;
  } catch (error) {
    console.error('Error converting lead to student:', error);
    throw error;
  }
}

// Sync student schedule
export async function syncStudentSchedule(studentId: string, groupId: string, scheduleData: any) {
  try {
    const response = await studentServiceClient.syncSchedule(studentId);
    return response;
  } catch (error) {
    console.error('Error syncing student schedule:', error);
    throw error;
  }
}

// Track assignment creation
export async function notifyStudentOfAssignment(studentId: string, assignmentData: any) {
  try {
    // Update student progress with new assignment
    const response = await studentServiceClient.updateProgress({
      assignmentId: assignmentData.id,
      progressPercentage: 0,
      feedback: 'Assignment created',
    });
    
    return response;
  } catch (error) {
    console.error('Error notifying student of assignment:', error);
    throw error;
  }
}

// Get student information for staff operations
export async function getStudentInfo(studentId: string) {
  try {
    const response = await studentServiceClient.getStudent(studentId);
    return response.data;
  } catch (error) {
    console.error('Error getting student info:', error);
    throw error;
  }
}

// Record payment information (staff can initiate payment records)
export async function recordPaymentForStudent(studentId: string, paymentData: any) {
  try {
    const response = await adminServiceClient.createPayment({
      studentId,
      amount: paymentData.amount,
      paymentDate: paymentData.paymentDate,
      paymentMethod: paymentData.paymentMethod,
      description: paymentData.description,
      notes: paymentData.notes,
    });
    
    return response;
  } catch (error) {
    console.error('Error recording payment:', error);
    throw error;
  }
}
