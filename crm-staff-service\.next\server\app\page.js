(()=>{var e={};e.id=974,e.ids=[974],e.modules={173:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>d.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=r(5239),a=r(8088),l=r(8170),d=r.n(l),n=r(893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);r.d(s,i);let o=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6367)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2791:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n,metadata:()=>d});var t=r(7413),a=r(4536),l=r.n(a);r(5692);let d={title:"Staff Portal - Innovative Centre CRM",description:"Lead management, course catalog, and teacher administration portal"};function n({children:e}){return(0,t.jsx)("html",{lang:"en",children:(0,t.jsx)("body",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"flex min-h-screen",children:[(0,t.jsxs)("aside",{className:"w-64 bg-white shadow-sm border-r border-gray-200",children:[(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Staff Portal"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"Innovative Centre CRM"})]}),(0,t.jsx)("nav",{className:"mt-6",children:(0,t.jsx)("div",{className:"px-3",children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(l(),{href:"/",className:"bg-blue-50 text-blue-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Dashboard"}),(0,t.jsx)(l(),{href:"/leads",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Lead Management"}),(0,t.jsx)(l(),{href:"/courses",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Course Catalog"}),(0,t.jsx)(l(),{href:"/groups",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Group Management"}),(0,t.jsx)(l(),{href:"/teachers",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Teacher Management"}),(0,t.jsx)(l(),{href:"/assignments",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Assignments"}),(0,t.jsx)(l(),{href:"/resources",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Resources"})]})})})]}),(0,t.jsx)("main",{className:"flex-1",children:e})]})})})}},5472:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,5814,23))},5692:()=>{},5759:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},6367:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>N});var t=r(7413),a=r(1120);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),d=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,r)=>r?r.toUpperCase():s.toLowerCase()),n=e=>{let s=d(e);return s.charAt(0).toUpperCase()+s.slice(1)},i=(...e)=>e.filter((e,s,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===s).join(" ").trim(),o=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=(0,a.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:r=2,absoluteStrokeWidth:t,className:l="",children:d,iconNode:n,...m},x)=>(0,a.createElement)("svg",{ref:x,...c,width:s,height:s,stroke:e,strokeWidth:t?24*Number(r)/Number(s):r,className:i("lucide",l),...!d&&!o(m)&&{"aria-hidden":"true"},...m},[...n.map(([e,s])=>(0,a.createElement)(e,s)),...Array.isArray(d)?d:[d]])),x=(e,s)=>{let r=(0,a.forwardRef)(({className:r,...t},d)=>(0,a.createElement)(m,{ref:d,iconNode:s,className:i(`lucide-${l(n(e))}`,`lucide-${e}`,r),...t}));return r.displayName=n(e),r},h=x("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),g=x("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),u=x("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]),p=x("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),v=x("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),y=x("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),b=x("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]),j=x("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);function N(){return(0,t.jsxs)("div",{className:"p-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Staff Dashboard"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Lead management, course catalog, and teacher administration"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h,{className:"h-8 w-8 text-blue-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Leads"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g,{className:"h-8 w-8 text-green-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Courses"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(u,{className:"h-8 w-8 text-purple-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Groups"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p,{className:"h-8 w-8 text-orange-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Teachers"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"0"})]})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(h,{className:"h-5 w-5 mr-2 text-blue-600"}),"Lead Management"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Track and convert potential students"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"New Leads"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"View and assign new leads"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Lead Activities"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Track follow-ups and interactions"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Conversion Pipeline"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Monitor lead conversion progress"})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(g,{className:"h-5 w-5 mr-2 text-green-600"}),"Course Catalog"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Manage courses and curriculum"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Create Course"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Add new courses to catalog"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Course Levels"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Manage course difficulty levels"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Pricing & Duration"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Set course fees and schedules"})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(u,{className:"h-5 w-5 mr-2 text-purple-600"}),"Group Management"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Create and manage student groups"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Create Group"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Form new student groups"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Assign Teachers"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Match teachers to groups"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Schedule Classes"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Set class times and rooms"})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(p,{className:"h-5 w-5 mr-2 text-orange-600"}),"Teacher Management"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Manage teacher profiles and KPIs"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Teacher Profiles"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"View teacher information"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"KPI Tracking"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Monitor performance metrics"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Performance Reviews"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Conduct teacher evaluations"})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(v,{className:"h-5 w-5 mr-2 text-red-600"}),"Assignment Management"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Create and track student assignments"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Create Assignment"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Assign tasks to students"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Track Progress"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Monitor completion status"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Grade Assignments"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Review and grade submissions"})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(y,{className:"h-5 w-5 mr-2 text-indigo-600"}),"Resource Management"]}),(0,t.jsx)("p",{className:"text-gray-600 text-sm mt-1",children:"Manage learning materials and resources"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Upload Resources"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Add books, documents, and materials"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Organize by Course"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Categorize learning materials"})]}),(0,t.jsxs)("button",{className:"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Access Control"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Manage resource permissions"})]})]})})]})]}),(0,t.jsx)("div",{className:"mt-8",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,t.jsx)(b,{className:"h-5 w-5 mr-2 text-green-600"}),"Recent Activity"]})}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"text-center text-gray-500 py-8",children:[(0,t.jsx)(j,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No recent activity to display"}),(0,t.jsx)("p",{className:"text-sm",children:"Activity will appear here as you use the system"})]})})]})})]})}},6487:()=>{},6651:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,663],()=>r(173));module.exports=t})();