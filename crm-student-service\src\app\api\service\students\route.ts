import { NextRequest, NextResponse } from 'next/server';
import { withServiceAuth, getAuthenticatedService } from '@/lib/service-auth';
import { prisma } from '@/lib/db';
import { z } from 'zod';

// Validation schema for student creation
const createStudentSchema = z.object({
  email: z.string().email(),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  dateOfBirth: z.string().optional().transform((str) => str ? new Date(str) : undefined),
  emergencyContact: z.record(z.any()).optional(),
  enrollmentDate: z.string().transform((str) => new Date(str)),
  currentLevel: z.string().optional(),
});

export const GET = withServiceAuth(async (request: NextRequest) => {
  try {
    const authenticatedService = getAuthenticatedService(request);
    if (!authenticatedService) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Service authentication required',
        },
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    if (studentId) {
      // Get specific student
      const student = await prisma.student.findUnique({
        where: { id: studentId },
        include: {
          studentUser: {
            select: {
              email: true,
              firstName: true,
              lastName: true,
              phone: true,
              isActive: true,
            },
          },
        },
      });

      if (!student) {
        return NextResponse.json({
          success: false,
          error: {
            code: 'STUDENT_NOT_FOUND',
            message: 'Student not found',
          },
        }, { status: 404 });
      }

      return NextResponse.json({
        success: true,
        data: student,
        meta: {
          requestedBy: authenticatedService,
        },
      });
    } else {
      // Get all students with pagination
      const [students, total] = await Promise.all([
        prisma.student.findMany({
          skip,
          take: limit,
          orderBy: { createdAt: 'desc' },
          include: {
            studentUser: {
              select: {
                email: true,
                firstName: true,
                lastName: true,
                phone: true,
                isActive: true,
              },
            },
          },
        }),
        prisma.student.count(),
      ]);

      return NextResponse.json({
        success: true,
        data: students,
        meta: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          requestedBy: authenticatedService,
        },
      });
    }
  } catch (error) {
    console.error('Error fetching students:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'FETCH_STUDENTS_ERROR',
        message: 'Failed to fetch students',
      },
    }, { status: 500 });
  }
});

export const POST = withServiceAuth(async (request: NextRequest) => {
  try {
    const authenticatedService = getAuthenticatedService(request);
    if (!authenticatedService) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Service authentication required',
        },
      }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createStudentSchema.parse(body);

    // Generate student ID
    const studentCount = await prisma.student.count();
    const studentId = `STU${(studentCount + 1).toString().padStart(6, '0')}`;

    // Create student user first
    const studentUser = await prisma.studentUser.create({
      data: {
        email: validatedData.email,
        passwordHash: 'temp-hash', // This should be properly hashed
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
      },
    });

    // Create student profile
    const student = await prisma.student.create({
      data: {
        studentUserId: studentUser.id,
        studentId,
        dateOfBirth: validatedData.dateOfBirth,
        emergencyContact: validatedData.emergencyContact,
        enrollmentDate: validatedData.enrollmentDate,
        currentLevel: validatedData.currentLevel,
      },
      include: {
        studentUser: {
          select: {
            email: true,
            firstName: true,
            lastName: true,
            phone: true,
            isActive: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: student,
      meta: {
        createdBy: authenticatedService,
      },
    }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid student data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    console.error('Error creating student:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'CREATE_STUDENT_ERROR',
        message: 'Failed to create student',
      },
    }, { status: 500 });
  }
});
