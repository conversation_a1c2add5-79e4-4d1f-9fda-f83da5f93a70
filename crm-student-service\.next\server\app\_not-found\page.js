(()=>{var e={};e.id=492,e.ids=[492],e.modules={643:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,5814,23))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3194:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>x,tree:()=>l});var s=t(5239),n=t(8088),o=t(8170),i=t.n(o),d=t(893),a={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>d[e]);t.d(r,a);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,m=[],c={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>i});var s=t(7413),n=t(4536),o=t.n(n);t(5692);let i={title:"Student Portal - Innovative Centre CRM",description:"Student portal for assignments, progress tracking, and resource access"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"flex min-h-screen",children:[(0,s.jsxs)("aside",{className:"w-64 bg-white shadow-sm border-r border-gray-200",children:[(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"Student Portal"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Innovative Centre"})]}),(0,s.jsx)("nav",{className:"mt-6",children:(0,s.jsx)("div",{className:"px-3",children:(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(o(),{href:"/",className:"bg-blue-50 text-blue-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Dashboard"}),(0,s.jsx)(o(),{href:"/assignments",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"My Assignments"}),(0,s.jsx)(o(),{href:"/progress",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Progress Tracking"}),(0,s.jsx)(o(),{href:"/resources",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Learning Resources"}),(0,s.jsx)(o(),{href:"/schedule",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Class Schedule"}),(0,s.jsx)(o(),{href:"/grades",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"Grades & Feedback"}),(0,s.jsx)(o(),{href:"/profile",className:"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md",children:"My Profile"})]})})})]}),(0,s.jsx)("main",{className:"flex-1",children:e})]})})})}},4619:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},5385:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},5692:()=>{},8761:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,663],()=>t(3194));module.exports=s})();