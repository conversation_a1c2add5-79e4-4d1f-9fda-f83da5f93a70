import { NextRequest, NextResponse } from 'next/server';
import { withServiceAuth, getAuthenticatedService } from '@/lib/service-auth';
import { prisma } from '@/lib/db';

export const GET = withServiceAuth(async (request: NextRequest) => {
  try {
    const authenticatedService = getAuthenticatedService(request);
    if (!authenticatedService) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Service authentication required',
        },
      }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    const groupId = searchParams.get('groupId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const skip = (page - 1) * limit;

    const where: any = {};
    if (studentId) where.studentId = studentId;
    if (groupId) where.groupId = groupId;

    const [assignments, total] = await Promise.all([
      prisma.studentAssignment.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          group: {
            include: {
              course: {
                select: {
                  title: true,
                  courseCode: true,
                },
              },
              teacher: {
                include: {
                  staffUser: {
                    select: {
                      firstName: true,
                      lastName: true,
                    },
                  },
                },
              },
            },
          },
          createdByUser: {
            select: {
              firstName: true,
              lastName: true,
            },
          },
        },
      }),
      prisma.studentAssignment.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: assignments,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        requestedBy: authenticatedService,
      },
    });
  } catch (error) {
    console.error('Error fetching assignments:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'FETCH_ASSIGNMENTS_ERROR',
        message: 'Failed to fetch assignments',
      },
    }, { status: 500 });
  }
});
