import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { staffServiceClient } from '@/lib/service-clients';
import { z } from 'zod';

// Validation schema for schedule sync
const syncScheduleSchema = z.object({
  studentId: z.string().min(1),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { studentId } = syncScheduleSchema.parse(body);

    // Verify student exists
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      include: {
        studentUser: {
          select: {
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (!student) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'STUDENT_NOT_FOUND',
          message: 'Student not found',
        },
      }, { status: 404 });
    }

    try {
      // Get groups from staff service
      const groupsResponse = await staffServiceClient.getGroups();
      
      if (!groupsResponse.success) {
        throw new Error('Failed to fetch groups from staff service');
      }

      // Filter groups that include this student (in a real implementation, 
      // the staff service would have an endpoint to get groups by student)
      const allGroups = groupsResponse.data;
      
      // For demonstration, we'll sync all active groups
      // In production, you'd filter by actual student enrollment
      const activeGroups = allGroups.filter((group: any) => group.status === 'active');

      // Clear existing schedules for this student
      await prisma.studentSchedule.deleteMany({
        where: { studentId },
      });

      // Create new schedule entries
      const schedulePromises = activeGroups.map(async (group: any) => {
        // Get course details
        const courseResponse = await staffServiceClient.getCourse(group.courseId);
        const course = courseResponse.success ? courseResponse.data : null;

        // Get teacher details (this would come from the group data in a real scenario)
        const teacherName = group.teacher ? 
          `${group.teacher.staffUser.firstName} ${group.teacher.staffUser.lastName}` : 
          'TBD';

        return prisma.studentSchedule.create({
          data: {
            studentId,
            groupId: group.id,
            courseTitle: course?.title || group.groupName,
            teacherName,
            cabinetNumber: group.cabinet?.cabinetNumber || 'TBD',
            schedule: group.schedule,
            startDate: new Date(group.startDate),
            endDate: group.endDate ? new Date(group.endDate) : null,
            isActive: group.status === 'active',
            lastUpdated: new Date(),
          },
        });
      });

      const syncedSchedules = await Promise.all(schedulePromises);

      return NextResponse.json({
        success: true,
        data: {
          studentId,
          syncedSchedules: syncedSchedules.length,
          schedules: syncedSchedules,
          message: 'Schedule successfully synchronized',
        },
      });

    } catch (syncError) {
      console.error('Error during schedule sync:', syncError);
      
      return NextResponse.json({
        success: false,
        error: {
          code: 'SYNC_FAILED',
          message: 'Failed to synchronize schedule',
          details: syncError instanceof Error ? syncError.message : 'Unknown error',
        },
      }, { status: 500 });
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid sync data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    console.error('Error syncing schedule:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'SYNC_SCHEDULE_ERROR',
        message: 'Failed to sync schedule',
      },
    }, { status: 500 });
  }
}
