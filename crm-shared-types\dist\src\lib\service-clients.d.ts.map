{"version": 3, "file": "service-clients.d.ts", "sourceRoot": "", "sources": ["../../../src/lib/service-clients.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,aAAa,EAAE,MAAM,QAAQ,CAAC;AACvC,OAAO,KAAK,EACV,IAAI,EACJ,iBAAiB,EACjB,OAAO,EACP,MAAM,EACN,KAAK,EACL,aAAa,EACb,0BAA0B,EAC1B,eAAe,EACf,eAAe,EACf,WAAW,EACX,iBAAiB,EAClB,MAAM,UAAU,CAAC;AAGlB,qBAAa,kBAAmB,SAAQ,aAAa;gBACvC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAK1D,WAAW,CAAC,IAAI,SAAI,EAAE,KAAK,SAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;IAI5E,aAAa,CAAC,IAAI,EAAE,0BAA0B,GAAG,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;IAIpF,aAAa,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;IAKrE,QAAQ,CAAC,WAAW,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;IAK3D,UAAU,CAAC,QAAQ,EAAE,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAIpD,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAKpE,SAAS,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAKlD,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;CAGvE;AAGD,qBAAa,kBAAmB,SAAQ,aAAa;gBACvC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAK1D,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,IAAI,SAAI,EAAE,KAAK,SAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAMjF,UAAU,CAAC,IAAI,EAAE,iBAAiB,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAI/D,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAKxE,UAAU,CAAC,IAAI,SAAI,EAAE,KAAK,SAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;IAIpE,SAAS,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAIzD,YAAY,CAAC,UAAU,EAAE,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAK3D,SAAS,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;IAK3D,WAAW,CAAC,SAAS,EAAE,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAIxD,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAK5E,cAAc,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;IAK/D,gBAAgB,CAAC,cAAc,EAAE,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAKhE,YAAY,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;IAO9E,cAAc,CAAC,YAAY,EAAE,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAK5D,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;IAI1C,cAAc,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;CAGrE;AAGD,qBAAa,oBAAqB,SAAQ,aAAa;gBACzC,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM;IAK1D,UAAU,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAI5D,aAAa,CAAC,WAAW,EAAE,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAI9D,aAAa,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAKjF,WAAW,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAIzD,cAAc,CAAC,YAAY,EAAE,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;IAKxE,cAAc,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,CAAC;IAI9D,gBAAgB,CAAC,cAAc,EAAE,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAKhE,WAAW,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,eAAe,EAAE,CAAC,CAAC;IAIvE,YAAY,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IAK1D,mBAAmB,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;CAG5F;AAGD,qBAAa,oBAAoB;IAM7B,OAAO,CAAC,MAAM;IALhB,OAAO,CAAC,WAAW,CAAC,CAAqB;IACzC,OAAO,CAAC,WAAW,CAAC,CAAqB;IACzC,OAAO,CAAC,aAAa,CAAC,CAAuB;gBAGnC,MAAM,EAAE;QACd,eAAe,EAAE,MAAM,CAAC;QACxB,eAAe,EAAE,MAAM,CAAC;QACxB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,WAAW,EAAE,MAAM,CAAC;QACpB,MAAM,EAAE,MAAM,CAAC;KAChB;IAGH,cAAc,IAAI,kBAAkB;IAWpC,cAAc,IAAI,kBAAkB;IAWpC,gBAAgB,IAAI,oBAAoB;CAUzC"}