{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "59bc4c0a7610329544889b26739fa307", "previewModeSigningKey": "89d460980728dc892d7b77191f29ff75ee6ac15c829d4517c0bba907d29c9c0f", "previewModeEncryptionKey": "549d1abcd1123c6a175ca3f478b0e6c7ba0833229420201f2ca473fd248f81f7"}}