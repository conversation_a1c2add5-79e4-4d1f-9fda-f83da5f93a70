# Staff Service Environment Variables

# Database Connection
STAFF_DATABASE_URL="postgresql://username:password@host:port/staff_database"

# Authentication (NextAuth.js v5)
NEXTAUTH_SECRET="your-nextauth-secret-for-staff-service"
NEXTAUTH_URL="https://crm-staff-service.vercel.app"

# Service URLs for Inter-Service Communication
ADMIN_SERVICE_URL="https://crm-admin-service.vercel.app"
STUDENT_SERVICE_URL="https://crm-student-service.vercel.app"
SHARED_TYPES_URL="https://crm-shared-types.vercel.app"

# Service Authentication
SERVICE_API_KEY="your-service-api-key"
SERVICE_NAME="crm-staff-service"

# Security Settings
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# JWT Configuration
JWT_SECRET="your-jwt-secret-for-staff-service"

# Email Configuration (for lead notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-email-password"

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH="/uploads"
ALLOWED_FILE_TYPES="pdf,doc,docx,txt,jpg,png,gif"

# Lead Management Settings
AUTO_ASSIGN_LEADS=true
LEAD_FOLLOW_UP_DAYS=3
LEAD_CONVERSION_TIMEOUT_DAYS=30

# Course Management Settings
DEFAULT_COURSE_DURATION=12
MAX_STUDENTS_PER_GROUP=15
AUTO_CREATE_GROUPS=false

# Teacher KPI Settings
KPI_CALCULATION_FREQUENCY="weekly"
KPI_TARGET_RETENTION_RATE=85
KPI_TARGET_PROGRESS_SCORE=75
KPI_TARGET_ATTENDANCE_RATE=90

# Notification Settings
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false
LEAD_NOTIFICATION_THRESHOLD=24

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=200

# Logging
LOG_LEVEL="info"
LOG_RETENTION_DAYS=90
