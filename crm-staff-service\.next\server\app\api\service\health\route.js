(()=>{var e={};e.id=232,e.ids=[232],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2553:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>u,serverHooks:()=>v,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>o});var i=t(6559),a=t(8088),n=t(7719),c=t(8731);let o=(0,c.A0)(async e=>(0,c._q)()),u=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/service/health/route",pathname:"/api/service/health",filename:"route",bundlePath:"app/api/service/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\service\\health\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:v}=u;function l(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},8731:(e,r,t)=>{"use strict";t.d(r,{A0:()=>n,TB:()=>c,_q:()=>o});var s=t(2190);!function(){var e=Error("Cannot find module 'crm-shared-types'");throw e.code="MODULE_NOT_FOUND",e}();let i={serviceName:"crm-staff-service",apiKey:process.env.SERVICE_API_KEY||"default-api-key",allowedServices:["crm-admin-service","crm-student-service","crm-staff-service"]},a=Object(function(){var e=Error("Cannot find module 'crm-shared-types'");throw e.code="MODULE_NOT_FOUND",e}())(i);function n(e){return async function(r){let t=a(r);return r.url.includes("/api/service/")&&!t.serviceAuth?.isAuthenticated?s.NextResponse.json({success:!1,error:{code:"UNAUTHORIZED",message:"Service authentication required"}},{status:401}):e(t)}}function c(e){return e.serviceAuth?.isAuthenticated?e.serviceAuth.serviceName:null}async function o(){try{return s.NextResponse.json({service:"crm-staff-service",status:"healthy",timestamp:new Date().toISOString(),version:"1.0.0",allowedServices:i.allowedServices})}catch(e){return s.NextResponse.json({service:"crm-staff-service",status:"unhealthy",timestamp:new Date().toISOString(),error:"Service health check failed"},{status:500})}}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(2553));module.exports=s})();