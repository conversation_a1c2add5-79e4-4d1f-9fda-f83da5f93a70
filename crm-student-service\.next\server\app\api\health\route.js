(()=>{var e={};e.id=772,e.ids=[772],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1282:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p});var a=r(6559),n=r(8088),i=r(7719),o=r(2190),u=r(1678);async function p(){try{return await u.z.$queryRaw`SELECT 1`,o.NextResponse.json({status:"healthy",service:"crm-student-service",timestamp:new Date().toISOString(),database:"connected",version:"1.0.0"})}catch{return o.NextResponse.json({status:"unhealthy",service:"crm-student-service",timestamp:new Date().toISOString(),database:"disconnected",error:"Database connection failed"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\api\\health\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=c;function m(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},1678:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient({log:["query","error","warn"]})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(1282));module.exports=s})();