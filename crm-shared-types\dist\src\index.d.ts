export * from './types';
export * from './lib/auth';
export * from './lib/service-clients';
export declare const VERSION = "1.0.0";
export declare const PACKAGE_NAME = "crm-shared-types";
export declare const SERVICES: {
    readonly STAFF: "crm-staff-service";
    readonly ADMIN: "crm-admin-service";
    readonly STUDENT: "crm-student-service";
    readonly SHARED_TYPES: "crm-shared-types";
};
export declare const DEFAULT_CONFIG: {
    readonly PAGINATION: {
        readonly DEFAULT_PAGE: 1;
        readonly DEFAULT_LIMIT: 20;
        readonly MAX_LIMIT: 100;
    };
    readonly SESSION: {
        readonly DEFAULT_TIMEOUT: 3600;
        readonly ADMIN_TIMEOUT: 1800;
        readonly STUDENT_TIMEOUT: 7200;
    };
    readonly SECURITY: {
        readonly MAX_LOGIN_ATTEMPTS: 5;
        readonly LOCKOUT_DURATION: 900;
        readonly PASSWORD_MIN_LENGTH: 8;
        readonly MFA_REQUIRED_FOR_ADMIN: true;
    };
    readonly FILE_UPLOAD: {
        readonly MAX_SIZE: number;
        readonly ALLOWED_TYPES: readonly ["pdf", "doc", "docx", "txt", "jpg", "png", "gif"];
        readonly UPLOAD_PATH: "/uploads";
    };
};
//# sourceMappingURL=index.d.ts.map