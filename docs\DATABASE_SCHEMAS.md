# Simplified Database Schemas - 3 Service Architecture

## 🗄️ Database Overview

**3 Separate Databases**: Each service has its own dedicated database for complete data isolation and security.

---

## 1. Staff Database (`STAFF_DATABASE_URL`)
**Service**: crm-staff-service
**Users**: Reception, Managers, Test Checkers, Teachers
**Responsibilities**: Leads, Courses, Groups, Teachers, Cabinets, Assignments

```sql
-- Staff Users Table
CREATE TABLE staff_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  role staff_role NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
  phone VARCHAR(20),
  employee_id VARCHAR(20) UNIQUE NOT NULL,
  department VARCHAR(100),
  hire_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE staff_role AS ENUM ('reception', 'manager', 'test_checker', 'teacher');

-- Teachers (Extended staff information)
CREATE TABLE teachers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  staff_user_id UUID REFERENCES staff_users(id),
  specialization VARCHAR(100),
  qualifications JSONB,
  kpi_score DECIMAL(5,2) DEFAULT 0.00,
  performance_metrics JSONB,
  status teacher_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE teacher_status AS ENUM ('active', 'inactive', 'on_leave');

-- Leads Table
CREATE TABLE leads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(20),
  source lead_source NOT NULL,
  status lead_status DEFAULT 'new',
  notes TEXT,
  assigned_to UUID REFERENCES staff_users(id),
  converted_to_student_id UUID, -- Reference to student service
  conversion_date DATE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE lead_source AS ENUM ('website', 'referral', 'social_media', 'walk_in', 'phone', 'advertisement');
CREATE TYPE lead_status AS ENUM ('new', 'contacted', 'qualified', 'converted', 'lost');

-- Courses Table
CREATE TABLE courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_code VARCHAR(20) UNIQUE NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  level VARCHAR(50) NOT NULL,
  duration_weeks INTEGER NOT NULL,
  max_students INTEGER DEFAULT 20,
  price DECIMAL(10,2) NOT NULL,
  status course_status DEFAULT 'active',
  created_by UUID REFERENCES staff_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE course_status AS ENUM ('active', 'inactive', 'archived');

-- Groups Table
CREATE TABLE groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_name VARCHAR(100) NOT NULL,
  course_id UUID REFERENCES courses(id),
  teacher_id UUID REFERENCES teachers(id),
  cabinet_id UUID REFERENCES cabinets(id),
  max_students INTEGER DEFAULT 15,
  current_students INTEGER DEFAULT 0,
  start_date DATE NOT NULL,
  end_date DATE,
  schedule JSONB, -- days and times
  status group_status DEFAULT 'active',
  created_by UUID REFERENCES staff_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE group_status AS ENUM ('active', 'completed', 'cancelled');

-- Cabinets (Classrooms)
CREATE TABLE cabinets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cabinet_number VARCHAR(20) UNIQUE NOT NULL,
  cabinet_name VARCHAR(100),
  capacity INTEGER NOT NULL,
  equipment JSONB, -- projector, whiteboard, etc.
  location VARCHAR(100),
  status cabinet_status DEFAULT 'available',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE cabinet_status AS ENUM ('available', 'occupied', 'maintenance');

-- Student Assignments (Created by teachers)
CREATE TABLE student_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  teacher_id UUID REFERENCES teachers(id),
  student_id UUID NOT NULL, -- Reference to student service
  group_id UUID REFERENCES groups(id),
  title VARCHAR(200) NOT NULL,
  description TEXT,
  due_date DATE,
  status assignment_status DEFAULT 'assigned',
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE assignment_status AS ENUM ('assigned', 'submitted', 'graded', 'overdue');

-- Student Resources (Notes, books, materials)
CREATE TABLE student_resources (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(200) NOT NULL,
  type resource_type NOT NULL,
  content TEXT, -- for notes
  file_url TEXT, -- for books/documents
  course_id UUID REFERENCES courses(id),
  group_id UUID REFERENCES groups(id),
  is_public BOOLEAN DEFAULT false,
  created_by UUID REFERENCES staff_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE resource_type AS ENUM ('note', 'book', 'document', 'link');

-- Lead Activities
CREATE TABLE lead_activities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID REFERENCES leads(id),
  activity_type activity_type NOT NULL,
  description TEXT,
  performed_by UUID REFERENCES staff_users(id),
  scheduled_at TIMESTAMP,
  completed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE activity_type AS ENUM ('call', 'email', 'meeting', 'follow_up', 'assessment');

-- Teacher KPIs
CREATE TABLE teacher_kpis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  teacher_id UUID REFERENCES teachers(id),
  metric_name VARCHAR(100) NOT NULL,
  metric_value DECIMAL(10,2) NOT NULL,
  target_value DECIMAL(10,2),
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  calculated_at TIMESTAMP DEFAULT NOW()
);
```

---

## 2. Admin Database (`ADMIN_DATABASE_URL`)
**Service**: crm-admin-service (Enhanced Security)
**Users**: Admin, Cashier, Accounting
**Responsibilities**: Payments, Financial Data, User Management, System Configuration

```sql
-- Admin Users Table
CREATE TABLE admin_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  role admin_role NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  mfa_enabled BOOLEAN DEFAULT true,
  mfa_secret VARCHAR(255),
  last_login TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE admin_role AS ENUM ('admin', 'cashier', 'accounting');

-- Payment Records Table (Note-taking only, no actual processing)
CREATE TABLE payment_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID NOT NULL, -- Reference to student service
  amount DECIMAL(10,2) NOT NULL,
  payment_date DATE NOT NULL,
  payment_method payment_method NOT NULL,
  description TEXT,
  status payment_status DEFAULT 'recorded',
  notes TEXT,
  recorded_by UUID REFERENCES admin_users(id),
  verified_by UUID REFERENCES admin_users(id),
  verification_date DATE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE payment_method AS ENUM ('cash', 'card', 'bank_transfer', 'check', 'online');
CREATE TYPE payment_status AS ENUM ('recorded', 'verified', 'disputed', 'refunded');

-- Fee Structures
CREATE TABLE fee_structures (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID NOT NULL, -- Reference to staff service
  fee_type fee_type NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  effective_date DATE NOT NULL,
  expiry_date DATE,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE fee_type AS ENUM ('tuition', 'registration', 'material', 'exam', 'late_payment');

-- Financial Transactions (Audit trail)
CREATE TABLE financial_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_record_id UUID REFERENCES payment_records(id),
  transaction_type transaction_type NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  description TEXT,
  performed_by UUID REFERENCES admin_users(id),
  ip_address INET,
  timestamp TIMESTAMP DEFAULT NOW()
);

CREATE TYPE transaction_type AS ENUM ('payment', 'refund', 'adjustment', 'fee_waiver');

-- System Configuration
CREATE TABLE system_config (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  config_key VARCHAR(100) UNIQUE NOT NULL,
  config_value JSONB NOT NULL,
  description TEXT,
  updated_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Audit Logs (Comprehensive logging for admin actions)
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES admin_users(id),
  action VARCHAR(100) NOT NULL,
  resource_type VARCHAR(50) NOT NULL,
  resource_id VARCHAR(100),
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  timestamp TIMESTAMP DEFAULT NOW()
);

-- Cross-Service User Management
CREATE TABLE user_management (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  service_name VARCHAR(50) NOT NULL,
  service_user_id UUID NOT NULL,
  email VARCHAR(255) NOT NULL,
  role VARCHAR(50) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  managed_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Financial Reports
CREATE TABLE financial_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  report_type VARCHAR(50) NOT NULL,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  data JSONB NOT NULL,
  generated_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW()
);
```

---

## 3. Student Database (`STUDENT_DATABASE_URL`)
**Service**: crm-student-service
**Users**: Students
**Responsibilities**: Student Portal, Assignments, Progress, Resources

```sql
-- Student Users Table
CREATE TABLE student_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Students (Extended student information)
CREATE TABLE students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_user_id UUID REFERENCES student_users(id),
  student_id VARCHAR(20) UNIQUE NOT NULL,
  date_of_birth DATE,
  emergency_contact JSONB,
  enrollment_date DATE NOT NULL,
  current_level VARCHAR(50),
  status student_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE student_status AS ENUM ('active', 'inactive', 'graduated', 'suspended');

-- Student Enrollments
CREATE TABLE student_enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  course_id UUID NOT NULL, -- Reference to staff service
  group_id UUID NOT NULL, -- Reference to staff service
  enrollment_date DATE NOT NULL,
  completion_date DATE,
  status enrollment_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE enrollment_status AS ENUM ('active', 'completed', 'dropped', 'transferred');

-- Student Progress
CREATE TABLE student_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  assignment_id UUID NOT NULL, -- Reference to staff service
  progress_percentage DECIMAL(5,2) DEFAULT 0.00,
  grade VARCHAR(10),
  feedback TEXT,
  submitted_at TIMESTAMP,
  graded_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Student Resource Access
CREATE TABLE student_resource_access (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  resource_id UUID NOT NULL, -- Reference to staff service
  accessed_at TIMESTAMP DEFAULT NOW(),
  access_duration INTEGER -- in minutes
);

-- Assignment Submissions
CREATE TABLE assignment_submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  assignment_id UUID NOT NULL, -- Reference to staff service
  submission_content TEXT,
  file_url TEXT,
  submitted_at TIMESTAMP DEFAULT NOW(),
  status submission_status DEFAULT 'submitted',
  teacher_feedback TEXT,
  grade VARCHAR(10),
  graded_at TIMESTAMP
);

CREATE TYPE submission_status AS ENUM ('submitted', 'graded', 'returned', 'late');

-- Student Schedules (Cached from staff service)
CREATE TABLE student_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  group_id UUID NOT NULL, -- Reference to staff service
  course_title VARCHAR(200),
  teacher_name VARCHAR(200),
  cabinet_number VARCHAR(20),
  schedule JSONB, -- days and times
  start_date DATE,
  end_date DATE,
  is_active BOOLEAN DEFAULT true,
  last_updated TIMESTAMP DEFAULT NOW()
);
```

## 🔄 Inter-Service Data References

### **Cross-Service References**
- **Staff Service** references Student IDs for assignments and enrollments
- **Admin Service** references Student IDs for payment records
- **Student Service** references Course/Group/Assignment IDs from Staff Service

### **Data Synchronization**
- Services communicate via REST APIs to fetch related data
- Critical data is cached locally for performance
- Event-driven updates for real-time synchronization

### **Example API Calls**
```typescript
// Staff service getting student info for assignment
const student = await fetch(`${STUDENT_SERVICE_URL}/api/students/${studentId}`);

// Admin service getting course info for payment
const course = await fetch(`${STAFF_SERVICE_URL}/api/courses/${courseId}`);

// Student service getting assignment details
const assignment = await fetch(`${STAFF_SERVICE_URL}/api/assignments/${assignmentId}`);
```

This simplified database structure maintains clear separation while enabling efficient communication between the three core services.
