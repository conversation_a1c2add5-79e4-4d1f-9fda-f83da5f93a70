import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { z } from 'zod';

// Validation schema for progress update
const updateProgressSchema = z.object({
  assignmentId: z.string().min(1),
  progressPercentage: z.number().min(0).max(100),
  feedback: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const studentId = searchParams.get('studentId');
    
    if (!studentId) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'MISSING_STUDENT_ID',
          message: 'Student ID is required',
        },
      }, { status: 400 });
    }

    const progress = await prisma.studentProgress.findMany({
      where: { studentId },
      orderBy: { createdAt: 'desc' },
    });

    // Calculate overall progress
    const totalProgress = progress.length > 0 
      ? progress.reduce((sum, p) => sum + p.progressPercentage, 0) / progress.length 
      : 0;

    return NextResponse.json({
      success: true,
      data: {
        overallProgress: Math.round(totalProgress),
        assignments: progress,
        totalAssignments: progress.length,
        completedAssignments: progress.filter(p => p.progressPercentage === 100).length,
      },
    });
  } catch (error) {
    console.error('Error fetching progress:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'FETCH_PROGRESS_ERROR',
        message: 'Failed to fetch student progress',
      },
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = updateProgressSchema.parse(body);

    // TODO: Get the current student ID from session/auth
    const studentId = 'temp-student-id'; // This should come from authentication

    const progress = await prisma.studentProgress.upsert({
      where: {
        studentId_assignmentId: {
          studentId,
          assignmentId: validatedData.assignmentId,
        },
      },
      update: {
        progressPercentage: validatedData.progressPercentage,
        feedback: validatedData.feedback,
      },
      create: {
        studentId,
        assignmentId: validatedData.assignmentId,
        progressPercentage: validatedData.progressPercentage,
        feedback: validatedData.feedback,
      },
    });

    return NextResponse.json({
      success: true,
      data: progress,
    }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid progress data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    console.error('Error updating progress:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'UPDATE_PROGRESS_ERROR',
        message: 'Failed to update progress',
      },
    }, { status: 500 });
  }
}
