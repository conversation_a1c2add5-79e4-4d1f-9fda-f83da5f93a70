// Export all user-related types
export * from './users';
// Export all lead-related types
export * from './leads';
// Export all course-related types
export * from './courses';
// Export all assignment-related types
export * from './assignments';
// Export all payment-related types
export * from './payments';
// Export all KPI-related types
export * from './kpis';
// Export all system-related types
export * from './system';
// Common Constants
export const USER_ROLES = {
    STAFF: ['reception', 'manager', 'test_checker', 'teacher'],
    ADMIN: ['admin', 'cashier', 'accounting'],
    STUDENT: ['student'],
};
export const LEAD_SOURCES = [
    'website',
    'referral',
    'social_media',
    'walk_in',
    'phone',
    'advertisement'
];
export const PAYMENT_METHODS = [
    'cash',
    'card',
    'bank_transfer',
    'check',
    'online'
];
export const RESOURCE_TYPES = [
    'note',
    'book',
    'document',
    'link'
];
export const KPI_METRICS = [
    'student_retention_rate',
    'student_progress_score',
    'class_attendance_rate',
    'student_satisfaction_rating',
    'assignment_completion_rate',
    'professional_development_hours'
];
// Validation schemas (for use with zod or similar)
export const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
export const PHONE_REGEX = /^\+?[\d\s\-\(\)]+$/;
export const STUDENT_ID_REGEX = /^STU\d{6}$/;
export const EMPLOYEE_ID_REGEX = /^EMP\d{6}$/;
export const COURSE_CODE_REGEX = /^[A-Z]{2,4}\d{3}$/;
