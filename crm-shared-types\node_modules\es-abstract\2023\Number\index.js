'use strict';

var add = require('./add');
var bitwiseAND = require('./bitwiseAND');
var bitwiseNOT = require('./bitwiseNOT');
var bitwiseOR = require('./bitwiseOR');
var bitwiseXOR = require('./bitwiseXOR');
var divide = require('./divide');
var equal = require('./equal');
var exponentiate = require('./exponentiate');
var leftShift = require('./leftShift');
var lessThan = require('./lessThan');
var multiply = require('./multiply');
var remainder = require('./remainder');
var sameValue = require('./sameValue');
var sameValueZero = require('./sameValueZero');
var signedRightShift = require('./signedRightShift');
var subtract = require('./subtract');
var toString = require('./toString');
var unaryMinus = require('./unaryMinus');
var unsignedRightShift = require('./unsignedRightShift');

module.exports = {
	add: add,
	bitwiseAND: bitwiseAND,
	bitwiseNOT: bitwiseNOT,
	bitwiseOR: bitwiseOR,
	bitwiseXOR: bitwiseXOR,
	divide: divide,
	equal: equal,
	exponentiate: exponentiate,
	leftShift: leftShift,
	lessThan: lessThan,
	multiply: multiply,
	remainder: remainder,
	sameValue: sameValue,
	sameValueZero: sameValueZero,
	signedRightShift: signedRightShift,
	subtract: subtract,
	toString: toString,
	unaryMinus: unaryMinus,
	unsignedRightShift: unsignedRightShift
};
