<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/80b60b691969e32f.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-ceb7eeaf6d559a68.js"/><script src="/_next/static/chunks/4bd1b696-67ee12fb04071d3b.js" async=""></script><script src="/_next/static/chunks/684-42609a042cd3abcf.js" async=""></script><script src="/_next/static/chunks/main-app-490992a072d83410.js" async=""></script><script src="/_next/static/chunks/app/layout-3f0448a0e33a5b46.js" async=""></script><title>Staff Portal - Innovative Centre CRM</title><meta name="description" content="Lead management, course catalog, and teacher administration portal"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="min-h-screen bg-gray-50"><div hidden=""><!--$--><!--/$--></div><div class="flex min-h-screen"><aside class="w-64 bg-white shadow-sm border-r border-gray-200"><div class="p-6"><h1 class="text-xl font-bold text-gray-900">Staff Portal</h1><p class="text-sm text-gray-600">Innovative Centre CRM</p></div><nav class="mt-6"><div class="px-3"><div class="space-y-1"><a class="bg-blue-50 text-blue-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md" href="/">Dashboard</a><a class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md" href="/leads">Lead Management</a><a class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md" href="/courses">Course Catalog</a><a class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md" href="/groups">Group Management</a><a class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md" href="/teachers">Teacher Management</a><a class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md" href="/assignments">Assignments</a><a class="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md" href="/resources">Resources</a></div></div></nav></aside><main class="flex-1"><div class="p-8"><div class="mb-8"><h1 class="text-3xl font-bold text-gray-900 mb-2">Staff Dashboard</h1><p class="text-gray-600">Lead management, course catalog, and teacher administration</p></div><div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"><div class="bg-white rounded-lg shadow p-6"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users h-8 w-8 text-blue-600" aria-hidden="true"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><path d="M16 3.128a4 4 0 0 1 0 7.744"></path><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><circle cx="9" cy="7" r="4"></circle></svg><div class="ml-4"><p class="text-sm font-medium text-gray-600">Active Leads</p><p class="text-2xl font-bold text-gray-900">0</p></div></div></div><div class="bg-white rounded-lg shadow p-6"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open h-8 w-8 text-green-600" aria-hidden="true"><path d="M12 7v14"></path><path d="M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z"></path></svg><div class="ml-4"><p class="text-sm font-medium text-gray-600">Active Courses</p><p class="text-2xl font-bold text-gray-900">0</p></div></div></div><div class="bg-white rounded-lg shadow p-6"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-graduation-cap h-8 w-8 text-purple-600" aria-hidden="true"><path d="M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z"></path><path d="M22 10v6"></path><path d="M6 12.5V16a6 3 0 0 0 12 0v-3.5"></path></svg><div class="ml-4"><p class="text-sm font-medium text-gray-600">Active Groups</p><p class="text-2xl font-bold text-gray-900">0</p></div></div></div><div class="bg-white rounded-lg shadow p-6"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-check h-8 w-8 text-orange-600" aria-hidden="true"><path d="m16 11 2 2 4-4"></path><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle></svg><div class="ml-4"><p class="text-sm font-medium text-gray-600">Active Teachers</p><p class="text-2xl font-bold text-gray-900">0</p></div></div></div></div><div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8"><div class="bg-white rounded-lg shadow"><div class="p-6 border-b border-gray-200"><h2 class="text-xl font-semibold text-gray-900 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users h-5 w-5 mr-2 text-blue-600" aria-hidden="true"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><path d="M16 3.128a4 4 0 0 1 0 7.744"></path><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><circle cx="9" cy="7" r="4"></circle></svg>Lead Management</h2><p class="text-gray-600 text-sm mt-1">Track and convert potential students</p></div><div class="p-6"><div class="space-y-4"><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">New Leads</div><div class="text-sm text-gray-600">View and assign new leads</div></button><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Lead Activities</div><div class="text-sm text-gray-600">Track follow-ups and interactions</div></button><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Conversion Pipeline</div><div class="text-sm text-gray-600">Monitor lead conversion progress</div></button></div></div></div><div class="bg-white rounded-lg shadow"><div class="p-6 border-b border-gray-200"><h2 class="text-xl font-semibold text-gray-900 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open h-5 w-5 mr-2 text-green-600" aria-hidden="true"><path d="M12 7v14"></path><path d="M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z"></path></svg>Course Catalog</h2><p class="text-gray-600 text-sm mt-1">Manage courses and curriculum</p></div><div class="p-6"><div class="space-y-4"><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Create Course</div><div class="text-sm text-gray-600">Add new courses to catalog</div></button><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Course Levels</div><div class="text-sm text-gray-600">Manage course difficulty levels</div></button><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Pricing &amp; Duration</div><div class="text-sm text-gray-600">Set course fees and schedules</div></button></div></div></div><div class="bg-white rounded-lg shadow"><div class="p-6 border-b border-gray-200"><h2 class="text-xl font-semibold text-gray-900 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-graduation-cap h-5 w-5 mr-2 text-purple-600" aria-hidden="true"><path d="M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z"></path><path d="M22 10v6"></path><path d="M6 12.5V16a6 3 0 0 0 12 0v-3.5"></path></svg>Group Management</h2><p class="text-gray-600 text-sm mt-1">Create and manage student groups</p></div><div class="p-6"><div class="space-y-4"><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Create Group</div><div class="text-sm text-gray-600">Form new student groups</div></button><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Assign Teachers</div><div class="text-sm text-gray-600">Match teachers to groups</div></button><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Schedule Classes</div><div class="text-sm text-gray-600">Set class times and rooms</div></button></div></div></div><div class="bg-white rounded-lg shadow"><div class="p-6 border-b border-gray-200"><h2 class="text-xl font-semibold text-gray-900 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-user-check h-5 w-5 mr-2 text-orange-600" aria-hidden="true"><path d="m16 11 2 2 4-4"></path><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle></svg>Teacher Management</h2><p class="text-gray-600 text-sm mt-1">Manage teacher profiles and KPIs</p></div><div class="p-6"><div class="space-y-4"><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Teacher Profiles</div><div class="text-sm text-gray-600">View teacher information</div></button><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">KPI Tracking</div><div class="text-sm text-gray-600">Monitor performance metrics</div></button><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Performance Reviews</div><div class="text-sm text-gray-600">Conduct teacher evaluations</div></button></div></div></div><div class="bg-white rounded-lg shadow"><div class="p-6 border-b border-gray-200"><h2 class="text-xl font-semibold text-gray-900 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text h-5 w-5 mr-2 text-red-600" aria-hidden="true"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg>Assignment Management</h2><p class="text-gray-600 text-sm mt-1">Create and track student assignments</p></div><div class="p-6"><div class="space-y-4"><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Create Assignment</div><div class="text-sm text-gray-600">Assign tasks to students</div></button><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Track Progress</div><div class="text-sm text-gray-600">Monitor completion status</div></button><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Grade Assignments</div><div class="text-sm text-gray-600">Review and grade submissions</div></button></div></div></div><div class="bg-white rounded-lg shadow"><div class="p-6 border-b border-gray-200"><h2 class="text-xl font-semibold text-gray-900 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-target h-5 w-5 mr-2 text-indigo-600" aria-hidden="true"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="6"></circle><circle cx="12" cy="12" r="2"></circle></svg>Resource Management</h2><p class="text-gray-600 text-sm mt-1">Manage learning materials and resources</p></div><div class="p-6"><div class="space-y-4"><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Upload Resources</div><div class="text-sm text-gray-600">Add books, documents, and materials</div></button><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Organize by Course</div><div class="text-sm text-gray-600">Categorize learning materials</div></button><button class="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"><div class="font-medium text-gray-900">Access Control</div><div class="text-sm text-gray-600">Manage resource permissions</div></button></div></div></div></div><div class="mt-8"><div class="bg-white rounded-lg shadow"><div class="p-6 border-b border-gray-200"><h2 class="text-xl font-semibold text-gray-900 flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up h-5 w-5 mr-2 text-green-600" aria-hidden="true"><path d="M16 7h6v6"></path><path d="m22 7-8.5 8.5-5-5L2 17"></path></svg>Recent Activity</h2></div><div class="p-6"><div class="text-center text-gray-500 py-8"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-calendar h-12 w-12 mx-auto mb-4 text-gray-300" aria-hidden="true"><path d="M8 2v4"></path><path d="M16 2v4"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><path d="M3 10h18"></path></svg><p>No recent activity to display</p><p class="text-sm">Activity will appear here as you use the system</p></div></div></div></div></div><!--$--><!--/$--></main></div><script src="/_next/static/chunks/webpack-ceb7eeaf6d559a68.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[6874,[\"177\",\"static/chunks/app/layout-3f0448a0e33a5b46.js\"],\"\"]\n3:I[7555,[],\"\"]\n4:I[1295,[],\"\"]\n5:I[9665,[],\"OutletBoundary\"]\n8:I[4911,[],\"AsyncMetadataOutlet\"]\na:I[9665,[],\"ViewportBoundary\"]\nc:I[9665,[],\"MetadataBoundary\"]\ne:I[6614,[],\"\"]\n:HL[\"/_next/static/css/80b60b691969e32f.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"sWZBy8H0be1GMGwaa9bUW\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/80b60b691969e32f.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"min-h-screen bg-gray-50\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex min-h-screen\",\"children\":[[\"$\",\"aside\",null,{\"className\":\"w-64 bg-white shadow-sm border-r border-gray-200\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-xl font-bold text-gray-900\",\"children\":\"Staff Portal\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Innovative Centre CRM\"}]]}],[\"$\",\"nav\",null,{\"className\":\"mt-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"px-3\",\"children\":[\"$\",\"div\",null,{\"className\":\"space-y-1\",\"children\":[[\"$\",\"$L2\",null,{\"href\":\"/\",\"className\":\"bg-blue-50 text-blue-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md\",\"children\":\"Dashboard\"}],[\"$\",\"$L2\",null,{\"href\":\"/leads\",\"className\":\"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md\",\"children\":\"Lead Management\"}],[\"$\",\"$L2\",null,{\"href\":\"/courses\",\"className\":\"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md\",\"children\":\"Course Catalog\"}],[\"$\",\"$L2\",null,{\"href\":\"/groups\",\"className\":\"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md\",\"children\":\"Group Management\"}],[\"$\",\"$L2\",null,{\"href\":\"/teachers\",\"className\":\"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md\",\"children\":\"Teacher Management\"}],[\"$\",\"$L2\",null,{\"href\":\"/assignments\",\"className\":\"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md\",\"children\":\"Assignments\"}],[\"$\",\"$L2\",null,{\"href\":\"/resources\",\"className\":\"text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md\",\"children\":\"Resources\"}]]}]}]}]]}],[\"$\",\"main\",null,{\"className\":\"flex-1\",\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"div\",null,{\"className\":\"p-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-8\",\"children\":[[\"$\",\"h1\",null,{\"className\":\"text-3xl font-bold text-gray-900 mb-2\",\"children\":\"Staff Dashboard\"}],[\"$\",\"p\",null,{\"className\":\"text-gray-600\",\"children\":\"Lead management, course catalog, and teacher administration\"}]]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow p-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-users h-8 w-8 text-blue-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"1yyitq\",{\"d\":\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"}],[\"$\",\"path\",\"16gr8j\",{\"d\":\"M16 3.128a4 4 0 0 1 0 7.744\"}],[\"$\",\"path\",\"kshegd\",{\"d\":\"M22 21v-2a4 4 0 0 0-3-3.87\"}],[\"$\",\"circle\",\"nufk8\",{\"cx\":\"9\",\"cy\":\"7\",\"r\":\"4\"}],\"$undefined\"]}],[\"$\",\"div\",null,{\"className\":\"ml-4\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-sm font-medium text-gray-600\",\"children\":\"Active Leads\"}],[\"$\",\"p\",null,{\"className\":\"text-2xl font-bold text-gray-900\",\"children\":\"0\"}]]}]]}]}],[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow p-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-book-open h-8 w-8 text-green-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"1akyts\",{\"d\":\"M12 7v14\"}],[\"$\",\"path\",\"ruj8y\",{\"d\":\"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z\"}],\"$undefined\"]}],[\"$\",\"div\",null,{\"className\":\"ml-4\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-sm font-medium text-gray-600\",\"children\":\"Active Courses\"}],[\"$\",\"p\",null,{\"className\":\"text-2xl font-bold text-gray-900\",\"children\":\"0\"}]]}]]}]}],[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow p-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-graduation-cap h-8 w-8 text-purple-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"j76jl0\",{\"d\":\"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z\"}],[\"$\",\"path\",\"1lu8f3\",{\"d\":\"M22 10v6\"}],[\"$\",\"path\",\"1r8lef\",{\"d\":\"M6 12.5V16a6 3 0 0 0 12 0v-3.5\"}],\"$undefined\"]}],[\"$\",\"div\",null,{\"className\":\"ml-4\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-sm font-medium text-gray-600\",\"children\":\"Active Groups\"}],[\"$\",\"p\",null,{\"className\":\"text-2xl font-bold text-gray-900\",\"children\":\"0\"}]]}]]}]}],[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow p-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-user-check h-8 w-8 text-orange-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"9rsbq5\",{\"d\":\"m16 11 2 2 4-4\"}],[\"$\",\"path\",\"1yyitq\",{\"d\":\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"}],[\"$\",\"circle\",\"nufk8\",{\"cx\":\"9\",\"cy\":\"7\",\"r\":\"4\"}],\"$undefined\"]}],[\"$\",\"div\",null,{\"className\":\"ml-4\",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-sm font-medium text-gray-600\",\"children\":\"Active Teachers\"}],[\"$\",\"p\",null,{\"className\":\"text-2xl font-bold text-gray-900\",\"children\":\"0\"}]]}]]}]}]]}],[\"$\",\"div\",null,{\"className\":\"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6 border-b border-gray-200\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold text-gray-900 flex items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-users h-5 w-5 mr-2 text-blue-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"1yyitq\",{\"d\":\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"}],[\"$\",\"path\",\"16gr8j\",{\"d\":\"M16 3.128a4 4 0 0 1 0 7.744\"}],[\"$\",\"path\",\"kshegd\",{\"d\":\"M22 21v-2a4 4 0 0 0-3-3.87\"}],[\"$\",\"circle\",\"nufk8\",{\"cx\":\"9\",\"cy\":\"7\",\"r\":\"4\"}],\"$undefined\"]}],\"Lead Management\"]}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm mt-1\",\"children\":\"Track and convert potential students\"}]]}],[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"New Leads\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"View and assign new leads\"}]]}],[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Lead Activities\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Track follow-ups and interactions\"}]]}],[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Conversion Pipeline\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Monitor lead conversion progress\"}]]}]]}]}]]}],[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6 border-b border-gray-200\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold text-gray-900 flex items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-book-open h-5 w-5 mr-2 text-green-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"1akyts\",{\"d\":\"M12 7v14\"}],[\"$\",\"path\",\"ruj8y\",{\"d\":\"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z\"}],\"$undefined\"]}],\"Course Catalog\"]}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm mt-1\",\"children\":\"Manage courses and curriculum\"}]]}],[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Create Course\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Add new courses to catalog\"}]]}],[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Course Levels\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Manage course difficulty levels\"}]]}],[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Pricing \u0026 Duration\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Set course fees and schedules\"}]]}]]}]}]]}],[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6 border-b border-gray-200\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold text-gray-900 flex items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-graduation-cap h-5 w-5 mr-2 text-purple-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"j76jl0\",{\"d\":\"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z\"}],[\"$\",\"path\",\"1lu8f3\",{\"d\":\"M22 10v6\"}],[\"$\",\"path\",\"1r8lef\",{\"d\":\"M6 12.5V16a6 3 0 0 0 12 0v-3.5\"}],\"$undefined\"]}],\"Group Management\"]}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm mt-1\",\"children\":\"Create and manage student groups\"}]]}],[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Create Group\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Form new student groups\"}]]}],[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Assign Teachers\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Match teachers to groups\"}]]}],[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Schedule Classes\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Set class times and rooms\"}]]}]]}]}]]}],[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6 border-b border-gray-200\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold text-gray-900 flex items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-user-check h-5 w-5 mr-2 text-orange-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"9rsbq5\",{\"d\":\"m16 11 2 2 4-4\"}],[\"$\",\"path\",\"1yyitq\",{\"d\":\"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\"}],[\"$\",\"circle\",\"nufk8\",{\"cx\":\"9\",\"cy\":\"7\",\"r\":\"4\"}],\"$undefined\"]}],\"Teacher Management\"]}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm mt-1\",\"children\":\"Manage teacher profiles and KPIs\"}]]}],[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Teacher Profiles\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"View teacher information\"}]]}],[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"KPI Tracking\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Monitor performance metrics\"}]]}],[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Performance Reviews\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Conduct teacher evaluations\"}]]}]]}]}]]}],[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6 border-b border-gray-200\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold text-gray-900 flex items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-file-text h-5 w-5 mr-2 text-red-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"1rqfz7\",{\"d\":\"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\"}],[\"$\",\"path\",\"tnqrlb\",{\"d\":\"M14 2v4a2 2 0 0 0 2 2h4\"}],[\"$\",\"path\",\"b1mrlr\",{\"d\":\"M10 9H8\"}],[\"$\",\"path\",\"t4e002\",{\"d\":\"M16 13H8\"}],[\"$\",\"path\",\"z1uh3a\",{\"d\":\"M16 17H8\"}],\"$undefined\"]}],\"Assignment Management\"]}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm mt-1\",\"children\":\"Create and track student assignments\"}]]}],[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Create Assignment\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Assign tasks to students\"}]]}],[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Track Progress\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Monitor completion status\"}]]}],[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Grade Assignments\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Review and grade submissions\"}]]}]]}]}]]}],[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6 border-b border-gray-200\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold text-gray-900 flex items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-target h-5 w-5 mr-2 text-indigo-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"circle\",\"1mglay\",{\"cx\":\"12\",\"cy\":\"12\",\"r\":\"10\"}],[\"$\",\"circle\",\"1vlfrh\",{\"cx\":\"12\",\"cy\":\"12\",\"r\":\"6\"}],[\"$\",\"circle\",\"1c9p78\",{\"cx\":\"12\",\"cy\":\"12\",\"r\":\"2\"}],\"$undefined\"]}],\"Resource Management\"]}],[\"$\",\"p\",null,{\"className\":\"text-gray-600 text-sm mt-1\",\"children\":\"Manage learning materials and resources\"}]]}],[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Upload Resources\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Add books, documents, and materials\"}]]}],[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Organize by Course\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Categorize learning materials\"}]]}],[\"$\",\"button\",null,{\"className\":\"w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-medium text-gray-900\",\"children\":\"Access Control\"}],[\"$\",\"div\",null,{\"className\":\"text-sm text-gray-600\",\"children\":\"Manage resource permissions\"}]]}]]}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"mt-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"bg-white rounded-lg shadow\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6 border-b border-gray-200\",\"children\":[\"$\",\"h2\",null,{\"className\":\"text-xl font-semibold text-gray-900 flex items-center\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-trending-up h-5 w-5 mr-2 text-green-600\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"box55l\",{\"d\":\"M16 7h6v6\"}],[\"$\",\"path\",\"1t1m79\",{\"d\":\"m22 7-8.5 8.5-5-5L2 17\"}],\"$undefined\"]}],\"Recent Activity\"]}]}],[\"$\",\"div\",null,{\"className\":\"p-6\",\"children\":[\"$\",\"div\",null,{\"className\":\"text-center text-gray-500 py-8\",\"children\":[[\"$\",\"svg\",null,{\"ref\":\"$undefined\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-calendar h-12 w-12 mx-auto mb-4 text-gray-300\",\"aria-hidden\":\"true\",\"children\":[[\"$\",\"path\",\"1cmpym\",{\"d\":\"M8 2v4\"}],[\"$\",\"path\",\"4m81vk\",{\"d\":\"M16 2v4\"}],[\"$\",\"rect\",\"1hopcy\",{\"width\":\"18\",\"height\":\"18\",\"x\":\"3\",\"y\":\"4\",\"rx\":\"2\"}],[\"$\",\"path\",\"8toen8\",{\"d\":\"M3 10h18\"}],\"$undefined\"]}],[\"$\",\"p\",null,{\"children\":\"No recent activity to display\"}],[\"$\",\"p\",null,{\"className\":\"text-sm\",\"children\":\"Activity will appear here as you use the system\"}]]}]}]]}]}]]}],null,[\"$\",\"$L5\",null,{\"children\":[\"$L6\",\"$L7\",[\"$\",\"$L8\",null,{\"promise\":\"$@9\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"3vF1fJ1iNN4FQZotGtvyTv\",{\"children\":[[\"$\",\"$La\",null,{\"children\":\"$Lb\"}],null]}],[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$e\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"f:\"$Sreact.suspense\"\n10:I[4911,[],\"AsyncMetadata\"]\nd:[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$f\",null,{\"fallback\":null,\"children\":[\"$\",\"$L10\",null,{\"promise\":\"$@11\"}]}]}]\n7:null\n"])</script><script>self.__next_f.push([1,"b:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n6:null\n"])</script><script>self.__next_f.push([1,"9:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Staff Portal - Innovative Centre CRM\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Lead management, course catalog, and teacher administration portal\"}]],\"error\":null,\"digest\":\"$undefined\"}\n11:{\"metadata\":\"$9:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>