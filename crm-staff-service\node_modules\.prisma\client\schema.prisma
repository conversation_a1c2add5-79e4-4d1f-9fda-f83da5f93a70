// Staff Service Database Schema
// Lead management, course catalog, group creation, and teacher management

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("STAFF_DATABASE_URL")
}

// Staff User Roles Enum
enum StaffRole {
  reception
  manager
  test_checker
  teacher
}

// Lead Source Enum
enum LeadSource {
  website
  referral
  social_media
  walk_in
  phone
  advertisement
}

// Lead Status Enum
enum LeadStatus {
  new
  contacted
  qualified
  converted
  lost
}

// Activity Type Enum
enum ActivityType {
  call
  email
  meeting
  follow_up
  assessment
}

// Course Status Enum
enum CourseStatus {
  active
  inactive
  archived
}

// Group Status Enum
enum GroupStatus {
  active
  completed
  cancelled
}

// Cabinet Status Enum
enum CabinetStatus {
  available
  occupied
  maintenance
}

// Teacher Status Enum
enum TeacherStatus {
  active
  inactive
  on_leave
}

// Enrollment Status Enum
enum EnrollmentStatus {
  active
  completed
  dropped
  transferred
}

// Assignment Status Enum
enum AssignmentStatus {
  assigned
  submitted
  graded
  overdue
}

// Resource Type Enum
enum ResourceType {
  note
  book
  document
  link
}

// Staff Users Table
model StaffUser {
  id           String    @id @default(cuid())
  email        String    @unique
  passwordHash String    @map("password_hash")
  role         StaffRole
  firstName    String    @map("first_name")
  lastName     String    @map("last_name")
  phone        String?
  employeeId   String    @unique @map("employee_id")
  department   String?
  hireDate     DateTime  @map("hire_date") @db.Date
  isActive     Boolean   @default(true) @map("is_active")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // Relations
  teacher            Teacher?
  leadsAssigned      Lead[]              @relation("LeadAssignedTo")
  leadActivities     LeadActivity[]
  coursesCreated     Course[]            @relation("CourseCreatedBy")
  groupsCreated      Group[]             @relation("GroupCreatedBy")
  assignmentsCreated StudentAssignment[] @relation("AssignmentCreatedBy")
  resourcesCreated   StudentResource[]   @relation("ResourceCreatedBy")

  @@map("staff_users")
}

// Teachers Table (Extended Staff Information)
model Teacher {
  id                 String        @id @default(cuid())
  staffUserId        String        @unique @map("staff_user_id")
  specialization     String?
  qualifications     Json?
  kpiScore           Decimal       @default(0) @db.Decimal(5, 2)
  performanceMetrics Json?         @map("performance_metrics")
  status             TeacherStatus @default(active)
  createdAt          DateTime      @default(now()) @map("created_at")
  updatedAt          DateTime      @updatedAt @map("updated_at")

  // Relations
  staffUser          StaffUser           @relation(fields: [staffUserId], references: [id], onDelete: Cascade)
  groups             Group[]
  kpis               TeacherKPI[]
  performanceReviews PerformanceReview[]

  @@map("teachers")
}

// Leads Table
model Lead {
  id                   String     @id @default(cuid())
  firstName            String     @map("first_name")
  lastName             String     @map("last_name")
  email                String?
  phone                String?
  source               LeadSource
  status               LeadStatus @default(new)
  notes                String?
  assignedTo           String?    @map("assigned_to")
  convertedToStudentId String?    @map("converted_to_student_id") // Reference to student service
  conversionDate       DateTime?  @map("conversion_date") @db.Date
  createdAt            DateTime   @default(now()) @map("created_at")
  updatedAt            DateTime   @updatedAt @map("updated_at")

  // Relations
  assignedToUser StaffUser?     @relation("LeadAssignedTo", fields: [assignedTo], references: [id])
  activities     LeadActivity[]

  @@map("leads")
}

// Lead Activities Table
model LeadActivity {
  id           String       @id @default(cuid())
  leadId       String       @map("lead_id")
  activityType ActivityType @map("activity_type")
  description  String?
  performedBy  String       @map("performed_by")
  scheduledAt  DateTime?    @map("scheduled_at")
  completedAt  DateTime?    @map("completed_at")
  createdAt    DateTime     @default(now()) @map("created_at")

  // Relations
  lead            Lead      @relation(fields: [leadId], references: [id], onDelete: Cascade)
  performedByUser StaffUser @relation(fields: [performedBy], references: [id])

  @@map("lead_activities")
}

// Courses Table
model Course {
  id            String       @id @default(cuid())
  courseCode    String       @unique @map("course_code")
  title         String
  description   String?
  level         String
  durationWeeks Int          @map("duration_weeks")
  maxStudents   Int          @map("max_students")
  price         Decimal      @db.Decimal(10, 2)
  status        CourseStatus @default(active)
  createdBy     String       @map("created_by")
  createdAt     DateTime     @default(now()) @map("created_at")
  updatedAt     DateTime     @updatedAt @map("updated_at")

  // Relations
  createdByUser StaffUser           @relation("CourseCreatedBy", fields: [createdBy], references: [id])
  groups        Group[]
  enrollments   StudentEnrollment[]
  resources     StudentResource[]

  @@map("courses")
}

// Groups Table
model Group {
  id              String      @id @default(cuid())
  groupName       String      @map("group_name")
  courseId        String      @map("course_id")
  teacherId       String      @map("teacher_id")
  cabinetId       String      @map("cabinet_id")
  maxStudents     Int         @map("max_students")
  currentStudents Int         @default(0) @map("current_students")
  startDate       DateTime    @map("start_date") @db.Date
  endDate         DateTime?   @map("end_date") @db.Date
  schedule        Json // days and times
  status          GroupStatus @default(active)
  createdBy       String      @map("created_by")
  createdAt       DateTime    @default(now()) @map("created_at")
  updatedAt       DateTime    @updatedAt @map("updated_at")

  // Relations
  course        Course              @relation(fields: [courseId], references: [id])
  teacher       Teacher             @relation(fields: [teacherId], references: [id])
  cabinet       Cabinet             @relation(fields: [cabinetId], references: [id])
  createdByUser StaffUser           @relation("GroupCreatedBy", fields: [createdBy], references: [id])
  enrollments   StudentEnrollment[]
  assignments   StudentAssignment[]
  resources     StudentResource[]

  @@map("groups")
}

// Cabinets Table (Classrooms)
model Cabinet {
  id            String        @id @default(cuid())
  cabinetNumber String        @unique @map("cabinet_number")
  cabinetName   String?       @map("cabinet_name")
  capacity      Int
  equipment     Json? // projector, whiteboard, etc.
  location      String?
  status        CabinetStatus @default(available)
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")

  // Relations
  groups Group[]

  @@map("cabinets")
}

// Student Enrollments Table
model StudentEnrollment {
  id             String           @id @default(cuid())
  studentId      String           @map("student_id") // Reference to student service
  courseId       String           @map("course_id")
  groupId        String           @map("group_id")
  enrollmentDate DateTime         @map("enrollment_date") @db.Date
  completionDate DateTime?        @map("completion_date") @db.Date
  status         EnrollmentStatus @default(active)
  createdAt      DateTime         @default(now()) @map("created_at")
  updatedAt      DateTime         @updatedAt @map("updated_at")

  // Relations
  course Course @relation(fields: [courseId], references: [id])
  group  Group  @relation(fields: [groupId], references: [id])

  @@map("student_enrollments")
}

// Student Assignments Table
model StudentAssignment {
  id          String           @id @default(cuid())
  teacherId   String           @map("teacher_id")
  studentId   String           @map("student_id") // Reference to student service
  groupId     String           @map("group_id")
  title       String
  description String?
  dueDate     DateTime?        @map("due_date")
  status      AssignmentStatus @default(assigned)
  notes       String?
  createdBy   String           @map("created_by")
  createdAt   DateTime         @default(now()) @map("created_at")
  updatedAt   DateTime         @updatedAt @map("updated_at")

  // Relations
  group         Group     @relation(fields: [groupId], references: [id])
  createdByUser StaffUser @relation("AssignmentCreatedBy", fields: [createdBy], references: [id])

  @@map("student_assignments")
}

// Student Resources Table
model StudentResource {
  id        String       @id @default(cuid())
  title     String
  type      ResourceType
  content   String? // for notes
  fileUrl   String?      @map("file_url") // for books/documents
  courseId  String       @map("course_id")
  groupId   String       @map("group_id")
  isPublic  Boolean      @default(false) @map("is_public")
  createdBy String       @map("created_by")
  createdAt DateTime     @default(now()) @map("created_at")
  updatedAt DateTime     @updatedAt @map("updated_at")

  // Relations
  course        Course    @relation(fields: [courseId], references: [id])
  group         Group     @relation(fields: [groupId], references: [id])
  createdByUser StaffUser @relation("ResourceCreatedBy", fields: [createdBy], references: [id])

  @@map("student_resources")
}

// Teacher KPI Metrics
enum KPIMetricName {
  student_retention_rate
  student_progress_score
  class_attendance_rate
  student_satisfaction_rating
  assignment_completion_rate
  professional_development_hours
}

// Teacher KPI Table
model TeacherKPI {
  id           String        @id @default(cuid())
  teacherId    String        @map("teacher_id")
  metricName   KPIMetricName @map("metric_name")
  metricValue  Decimal       @map("metric_value") @db.Decimal(10, 2)
  targetValue  Decimal?      @map("target_value") @db.Decimal(10, 2)
  periodStart  DateTime      @map("period_start") @db.Date
  periodEnd    DateTime      @map("period_end") @db.Date
  calculatedAt DateTime      @default(now()) @map("calculated_at")

  // Relations
  teacher Teacher @relation(fields: [teacherId], references: [id], onDelete: Cascade)

  @@map("teacher_kpis")
}

// Performance Reviews Table
model PerformanceReview {
  id              String   @id @default(cuid())
  teacherId       String   @map("teacher_id")
  reviewerId      String   @map("reviewer_id") // Staff User ID (Manager/Admin)
  periodStart     DateTime @map("period_start") @db.Date
  periodEnd       DateTime @map("period_end") @db.Date
  overallRating   Decimal  @map("overall_rating") @db.Decimal(3, 2)
  kpiScores       Json     @map("kpi_scores") // Record of KPI scores
  feedback        String
  goals           Json // Array of goals
  improvementPlan String?  @map("improvement_plan")
  nextReviewDate  DateTime @map("next_review_date") @db.Date
  status          String   @default("draft") // draft, completed, approved
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  teacher Teacher @relation(fields: [teacherId], references: [id], onDelete: Cascade)

  @@map("performance_reviews")
}
