(()=>{var e={};e.id=248,e.ids=[248],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1678:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});let s=require("@prisma/client"),n=globalThis.prisma??new s.PrismaClient({log:["query","error","warn"]})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},8731:(e,t,r)=>{"use strict";r.d(t,{A0:()=>i,TB:()=>c,_q:()=>o});var s=r(2190);!function(){var e=Error("Cannot find module 'crm-shared-types'");throw e.code="MODULE_NOT_FOUND",e}();let n={serviceName:"crm-staff-service",apiKey:process.env.SERVICE_API_KEY||"default-api-key",allowedServices:["crm-admin-service","crm-student-service","crm-staff-service"]},a=Object(function(){var e=Error("Cannot find module 'crm-shared-types'");throw e.code="MODULE_NOT_FOUND",e}())(n);function i(e){return async function(t){let r=a(t);return t.url.includes("/api/service/")&&!r.serviceAuth?.isAuthenticated?s.NextResponse.json({success:!1,error:{code:"UNAUTHORIZED",message:"Service authentication required"}},{status:401}):e(r)}}function c(e){return e.serviceAuth?.isAuthenticated?e.serviceAuth.serviceName:null}async function o(){try{return s.NextResponse.json({service:"crm-staff-service",status:"healthy",timestamp:new Date().toISOString(),version:"1.0.0",allowedServices:n.allowedServices})}catch(e){return s.NextResponse.json({service:"crm-staff-service",status:"unhealthy",timestamp:new Date().toISOString(),error:"Service health check failed"},{status:500})}}},9275:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>v,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>d});var n=r(6559),a=r(8088),i=r(7719),c=r(2190),o=r(8731),u=r(1678);let d=(0,o.A0)(async e=>{try{let t=(0,o.TB)(e);if(!t)return c.NextResponse.json({success:!1,error:{code:"UNAUTHORIZED",message:"Service authentication required"}},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("studentId"),n=r.get("groupId"),a=parseInt(r.get("page")||"1"),i=parseInt(r.get("limit")||"20"),d=(a-1)*i,p={};s&&(p.studentId=s),n&&(p.groupId=n);let[l,m]=await Promise.all([u.z.studentAssignment.findMany({where:p,skip:d,take:i,orderBy:{createdAt:"desc"},include:{group:{include:{course:{select:{title:!0,courseCode:!0}},teacher:{include:{staffUser:{select:{firstName:!0,lastName:!0}}}}}},createdByUser:{select:{firstName:!0,lastName:!0}}}}),u.z.studentAssignment.count({where:p})]);return c.NextResponse.json({success:!0,data:l,meta:{page:a,limit:i,total:m,totalPages:Math.ceil(m/i),requestedBy:t}})}catch(e){return console.error("Error fetching assignments:",e),c.NextResponse.json({success:!1,error:{code:"FETCH_ASSIGNMENTS_ERROR",message:"Failed to fetch assignments"}},{status:500})}}),p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/service/assignments/route",pathname:"/api/service/assignments",filename:"route",bundlePath:"app/api/service/assignments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\service\\assignments\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:v}=p;function g(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(9275));module.exports=s})();