[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\api\\health\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\api\\progress\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\lib\\db.ts": "5"}, {"size": 646, "mtime": 1750451502993, "results": "6", "hashOfConfig": "7"}, {"size": 3075, "mtime": 1750451518751, "results": "8", "hashOfConfig": "7"}, {"size": 2768, "mtime": 1750451450002, "results": "9", "hashOfConfig": "7"}, {"size": 9852, "mtime": 1750451689426, "results": "10", "hashOfConfig": "7"}, {"size": 322, "mtime": 1750451494314, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1j56q5j", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\api\\health\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\api\\progress\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-student-service\\src\\lib\\db.ts", [], []]