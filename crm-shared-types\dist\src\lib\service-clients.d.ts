import { ServiceClient } from './auth';
import type { Lead, CreateLeadRequest, Student, Course, Group, PaymentRecord, CreatePaymentRecordRequest, StudentProgress, StudentSchedule, ApiResponse, PaginatedResponse } from '../types';
export declare class AdminServiceClient extends ServiceClient {
    constructor(baseUrl: string, serviceName: string, apiKey: string);
    getPayments(page?: number, limit?: number): Promise<PaginatedResponse<PaymentRecord>>;
    createPayment(data: CreatePaymentRecordRequest): Promise<ApiResponse<PaymentRecord>>;
    verifyPayment(paymentId: string): Promise<ApiResponse<PaymentRecord>>;
    getUsers(serviceName?: string): Promise<ApiResponse<any[]>>;
    createUser(userData: any): Promise<ApiResponse<any>>;
    updateUser(userId: string, userData: any): Promise<ApiResponse<any>>;
    getConfig(key?: string): Promise<ApiResponse<any>>;
    updateConfig(key: string, value: any): Promise<ApiResponse<any>>;
}
export declare class StaffServiceClient extends ServiceClient {
    constructor(baseUrl: string, serviceName: string, apiKey: string);
    getLeads(status?: string, page?: number, limit?: number): Promise<PaginatedResponse<Lead>>;
    createLead(data: CreateLeadRequest): Promise<ApiResponse<Lead>>;
    convertLead(leadId: string, studentData: any): Promise<ApiResponse<any>>;
    getCourses(page?: number, limit?: number): Promise<PaginatedResponse<Course>>;
    getCourse(courseId: string): Promise<ApiResponse<Course>>;
    createCourse(courseData: any): Promise<ApiResponse<Course>>;
    getGroups(courseId?: string): Promise<ApiResponse<Group[]>>;
    createGroup(groupData: any): Promise<ApiResponse<Group>>;
    enrollStudent(groupId: string, studentId: string): Promise<ApiResponse<any>>;
    getAssignments(studentId?: string): Promise<ApiResponse<any[]>>;
    createAssignment(assignmentData: any): Promise<ApiResponse<any>>;
    getResources(courseId?: string, groupId?: string): Promise<ApiResponse<any[]>>;
    createResource(resourceData: any): Promise<ApiResponse<any>>;
    getTeachers(): Promise<ApiResponse<any[]>>;
    getTeacherKPIs(teacherId: string): Promise<ApiResponse<any[]>>;
}
export declare class StudentServiceClient extends ServiceClient {
    constructor(baseUrl: string, serviceName: string, apiKey: string);
    getStudent(studentId: string): Promise<ApiResponse<Student>>;
    createStudent(studentData: any): Promise<ApiResponse<Student>>;
    updateStudent(studentId: string, studentData: any): Promise<ApiResponse<Student>>;
    getProgress(studentId: string): Promise<ApiResponse<any>>;
    updateProgress(progressData: any): Promise<ApiResponse<StudentProgress>>;
    getSubmissions(studentId: string): Promise<ApiResponse<any[]>>;
    submitAssignment(submissionData: any): Promise<ApiResponse<any>>;
    getSchedule(studentId: string): Promise<ApiResponse<StudentSchedule[]>>;
    syncSchedule(studentId: string): Promise<ApiResponse<any>>;
    trackResourceAccess(studentId: string, resourceId: string): Promise<ApiResponse<any>>;
}
export declare class ServiceClientFactory {
    private config;
    private adminClient?;
    private staffClient?;
    private studentClient?;
    constructor(config: {
        adminServiceUrl: string;
        staffServiceUrl: string;
        studentServiceUrl: string;
        serviceName: string;
        apiKey: string;
    });
    getAdminClient(): AdminServiceClient;
    getStaffClient(): StaffServiceClient;
    getStudentClient(): StudentServiceClient;
}
//# sourceMappingURL=service-clients.d.ts.map