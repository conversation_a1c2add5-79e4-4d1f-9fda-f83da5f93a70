
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.1
 * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
 */
Prisma.prismaVersion = {
  client: "6.10.1",
  engine: "9b628578b3b7cae625e8c927178f15a170e74a9c"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.StaffUserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  passwordHash: 'passwordHash',
  role: 'role',
  firstName: 'firstName',
  lastName: 'lastName',
  phone: 'phone',
  employeeId: 'employeeId',
  department: 'department',
  hireDate: 'hireDate',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TeacherScalarFieldEnum = {
  id: 'id',
  staffUserId: 'staffUserId',
  specialization: 'specialization',
  qualifications: 'qualifications',
  kpiScore: 'kpiScore',
  performanceMetrics: 'performanceMetrics',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LeadScalarFieldEnum = {
  id: 'id',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  phone: 'phone',
  source: 'source',
  status: 'status',
  notes: 'notes',
  assignedTo: 'assignedTo',
  convertedToStudentId: 'convertedToStudentId',
  conversionDate: 'conversionDate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LeadActivityScalarFieldEnum = {
  id: 'id',
  leadId: 'leadId',
  activityType: 'activityType',
  description: 'description',
  performedBy: 'performedBy',
  scheduledAt: 'scheduledAt',
  completedAt: 'completedAt',
  createdAt: 'createdAt'
};

exports.Prisma.CourseScalarFieldEnum = {
  id: 'id',
  courseCode: 'courseCode',
  title: 'title',
  description: 'description',
  level: 'level',
  durationWeeks: 'durationWeeks',
  maxStudents: 'maxStudents',
  price: 'price',
  status: 'status',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.GroupScalarFieldEnum = {
  id: 'id',
  groupName: 'groupName',
  courseId: 'courseId',
  teacherId: 'teacherId',
  cabinetId: 'cabinetId',
  maxStudents: 'maxStudents',
  currentStudents: 'currentStudents',
  startDate: 'startDate',
  endDate: 'endDate',
  schedule: 'schedule',
  status: 'status',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CabinetScalarFieldEnum = {
  id: 'id',
  cabinetNumber: 'cabinetNumber',
  cabinetName: 'cabinetName',
  capacity: 'capacity',
  equipment: 'equipment',
  location: 'location',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StudentEnrollmentScalarFieldEnum = {
  id: 'id',
  studentId: 'studentId',
  courseId: 'courseId',
  groupId: 'groupId',
  enrollmentDate: 'enrollmentDate',
  completionDate: 'completionDate',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StudentAssignmentScalarFieldEnum = {
  id: 'id',
  teacherId: 'teacherId',
  studentId: 'studentId',
  groupId: 'groupId',
  title: 'title',
  description: 'description',
  dueDate: 'dueDate',
  status: 'status',
  notes: 'notes',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StudentResourceScalarFieldEnum = {
  id: 'id',
  title: 'title',
  type: 'type',
  content: 'content',
  fileUrl: 'fileUrl',
  courseId: 'courseId',
  groupId: 'groupId',
  isPublic: 'isPublic',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TeacherKPIScalarFieldEnum = {
  id: 'id',
  teacherId: 'teacherId',
  metricName: 'metricName',
  metricValue: 'metricValue',
  targetValue: 'targetValue',
  periodStart: 'periodStart',
  periodEnd: 'periodEnd',
  calculatedAt: 'calculatedAt'
};

exports.Prisma.PerformanceReviewScalarFieldEnum = {
  id: 'id',
  teacherId: 'teacherId',
  reviewerId: 'reviewerId',
  periodStart: 'periodStart',
  periodEnd: 'periodEnd',
  overallRating: 'overallRating',
  kpiScores: 'kpiScores',
  feedback: 'feedback',
  goals: 'goals',
  improvementPlan: 'improvementPlan',
  nextReviewDate: 'nextReviewDate',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.StaffRole = exports.$Enums.StaffRole = {
  reception: 'reception',
  manager: 'manager',
  test_checker: 'test_checker',
  teacher: 'teacher'
};

exports.TeacherStatus = exports.$Enums.TeacherStatus = {
  active: 'active',
  inactive: 'inactive',
  on_leave: 'on_leave'
};

exports.LeadSource = exports.$Enums.LeadSource = {
  website: 'website',
  referral: 'referral',
  social_media: 'social_media',
  walk_in: 'walk_in',
  phone: 'phone',
  advertisement: 'advertisement'
};

exports.LeadStatus = exports.$Enums.LeadStatus = {
  new: 'new',
  contacted: 'contacted',
  qualified: 'qualified',
  converted: 'converted',
  lost: 'lost'
};

exports.ActivityType = exports.$Enums.ActivityType = {
  call: 'call',
  email: 'email',
  meeting: 'meeting',
  follow_up: 'follow_up',
  assessment: 'assessment'
};

exports.CourseStatus = exports.$Enums.CourseStatus = {
  active: 'active',
  inactive: 'inactive',
  archived: 'archived'
};

exports.GroupStatus = exports.$Enums.GroupStatus = {
  active: 'active',
  completed: 'completed',
  cancelled: 'cancelled'
};

exports.CabinetStatus = exports.$Enums.CabinetStatus = {
  available: 'available',
  occupied: 'occupied',
  maintenance: 'maintenance'
};

exports.EnrollmentStatus = exports.$Enums.EnrollmentStatus = {
  active: 'active',
  completed: 'completed',
  dropped: 'dropped',
  transferred: 'transferred'
};

exports.AssignmentStatus = exports.$Enums.AssignmentStatus = {
  assigned: 'assigned',
  submitted: 'submitted',
  graded: 'graded',
  overdue: 'overdue'
};

exports.ResourceType = exports.$Enums.ResourceType = {
  note: 'note',
  book: 'book',
  document: 'document',
  link: 'link'
};

exports.KPIMetricName = exports.$Enums.KPIMetricName = {
  student_retention_rate: 'student_retention_rate',
  student_progress_score: 'student_progress_score',
  class_attendance_rate: 'class_attendance_rate',
  student_satisfaction_rating: 'student_satisfaction_rating',
  assignment_completion_rate: 'assignment_completion_rate',
  professional_development_hours: 'professional_development_hours'
};

exports.Prisma.ModelName = {
  StaffUser: 'StaffUser',
  Teacher: 'Teacher',
  Lead: 'Lead',
  LeadActivity: 'LeadActivity',
  Course: 'Course',
  Group: 'Group',
  Cabinet: 'Cabinet',
  StudentEnrollment: 'StudentEnrollment',
  StudentAssignment: 'StudentAssignment',
  StudentResource: 'StudentResource',
  TeacherKPI: 'TeacherKPI',
  PerformanceReview: 'PerformanceReview'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
