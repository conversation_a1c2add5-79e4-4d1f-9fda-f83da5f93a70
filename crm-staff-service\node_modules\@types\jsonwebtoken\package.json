{"name": "@types/jsonwebtoken", "version": "9.0.10", "description": "TypeScript definitions for jsonwebtoken", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jsonwebtoken", "license": "MIT", "contributors": [{"name": "Maxime LUCE", "githubUsername": "SomaticIT", "url": "https://github.com/SomaticIT"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/danielheim"}, {"name": "Brice BERNARD", "githubUsername": "brikou", "url": "https://github.com/brikou"}, {"name": "Veli-Pekka Kestilä", "githubUsername": "vpk", "url": "https://github.com/vpk"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/GeneralistDev"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "kettil", "url": "https://github.com/kettil"}, {"name": "<PERSON>", "githubUsername": "RunAge", "url": "https://github.com/RunAge"}, {"name": "<PERSON>", "githubUsername": "nflaig", "url": "https://github.com/nflaig"}, {"name": "<PERSON><PERSON>", "githubUsername": "LinusU", "url": "https://github.com/LinusU"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ivansieder"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "githubUsername": "nandi95", "url": "https://github.com/nandi95"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/jsonwebtoken"}, "scripts": {}, "dependencies": {"@types/ms": "*", "@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "d51483f13412c41d8e77e914b45268c47b0cde11c20fa17f2dee720012292dca", "typeScriptVersion": "5.1"}