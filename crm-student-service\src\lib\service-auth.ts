// Service Authentication for Student Service

import { NextRequest, NextResponse } from 'next/server';
import { createServiceAuthMiddleware, ServiceAuthConfig } from 'crm-shared-types';

// Student service authentication configuration
const serviceAuthConfig: ServiceAuthConfig = {
  serviceName: 'crm-student-service',
  apiKey: process.env.SERVICE_API_KEY || 'default-api-key',
  allowedServices: [
    'crm-admin-service',
    'crm-staff-service',
    'crm-student-service'
  ],
};

// Create the middleware
const authMiddleware = createServiceAuthMiddleware(serviceAuthConfig);

// Wrapper function for API routes that require service authentication
export function withServiceAuth(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async function authenticatedHandler(request: NextRequest): Promise<NextResponse> {
    // Apply authentication middleware
    const authenticatedRequest = authMiddleware(request);
    
    // Check if authentication is required for this endpoint
    const isServiceEndpoint = request.url.includes('/api/service/');
    
    if (isServiceEndpoint && !authenticatedRequest.serviceAuth?.isAuthenticated) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Service authentication required',
        },
      }, { status: 401 });
    }
    
    // Call the original handler
    return handler(authenticatedRequest);
  };
}

// Helper function to get authenticated service name
export function getAuthenticatedService(request: NextRequest): string | null {
  const authenticatedRequest = request as any;
  return authenticatedRequest.serviceAuth?.isAuthenticated 
    ? authenticatedRequest.serviceAuth.serviceName 
    : null;
}

// Service health check endpoint
export async function handleServiceHealthCheck(): Promise<NextResponse> {
  try {
    return NextResponse.json({
      service: 'crm-student-service',
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      allowedServices: serviceAuthConfig.allowedServices,
    });
  } catch (error) {
    return NextResponse.json({
      service: 'crm-student-service',
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Service health check failed',
    }, { status: 500 });
  }
}
