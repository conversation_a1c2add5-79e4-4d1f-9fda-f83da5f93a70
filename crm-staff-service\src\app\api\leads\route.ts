import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { z } from 'zod';

// Validation schema for lead creation
const createLeadSchema = z.object({
  firstName: z.string().min(1),
  lastName: z.string().min(1),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  source: z.enum(['website', 'referral', 'social_media', 'walk_in', 'phone', 'advertisement']),
  notes: z.string().optional(),
  assignedTo: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');
    const skip = (page - 1) * limit;

    const where = status ? { status: status as 'new' | 'contacted' | 'qualified' | 'converted' | 'lost' } : {};

    const [leads, total] = await Promise.all([
      prisma.lead.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          assignedToUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          activities: {
            orderBy: { createdAt: 'desc' },
            take: 3,
            include: {
              performedByUser: {
                select: {
                  firstName: true,
                  lastName: true,
                },
              },
            },
          },
        },
      }),
      prisma.lead.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: leads,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching leads:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'FETCH_LEADS_ERROR',
        message: 'Failed to fetch leads',
      },
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = createLeadSchema.parse(body);

    const lead = await prisma.lead.create({
      data: {
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        email: validatedData.email,
        phone: validatedData.phone,
        source: validatedData.source,
        notes: validatedData.notes,
        assignedTo: validatedData.assignedTo,
      },
      include: {
        assignedToUser: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    // TODO: Create initial activity log
    if (lead.assignedTo) {
      await prisma.leadActivity.create({
        data: {
          leadId: lead.id,
          activityType: 'follow_up',
          description: 'Lead assigned for initial contact',
          performedBy: lead.assignedTo,
          scheduledAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // Schedule for tomorrow
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: lead,
    }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid lead data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    console.error('Error creating lead:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'CREATE_LEAD_ERROR',
        message: 'Failed to create lead',
      },
    }, { status: 500 });
  }
}
