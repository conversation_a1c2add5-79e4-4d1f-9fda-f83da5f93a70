
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model StudentUser
 * 
 */
export type StudentUser = $Result.DefaultSelection<Prisma.$StudentUserPayload>
/**
 * Model Student
 * 
 */
export type Student = $Result.DefaultSelection<Prisma.$StudentPayload>
/**
 * Model StudentProgress
 * 
 */
export type StudentProgress = $Result.DefaultSelection<Prisma.$StudentProgressPayload>
/**
 * Model AssignmentSubmission
 * 
 */
export type AssignmentSubmission = $Result.DefaultSelection<Prisma.$AssignmentSubmissionPayload>
/**
 * Model StudentResourceAccess
 * 
 */
export type StudentResourceAccess = $Result.DefaultSelection<Prisma.$StudentResourceAccessPayload>
/**
 * Model StudentSchedule
 * 
 */
export type StudentSchedule = $Result.DefaultSelection<Prisma.$StudentSchedulePayload>

/**
 * Enums
 */
export namespace $Enums {
  export const StudentStatus: {
  active: 'active',
  inactive: 'inactive',
  graduated: 'graduated',
  suspended: 'suspended'
};

export type StudentStatus = (typeof StudentStatus)[keyof typeof StudentStatus]


export const SubmissionStatus: {
  submitted: 'submitted',
  graded: 'graded',
  returned: 'returned',
  late: 'late'
};

export type SubmissionStatus = (typeof SubmissionStatus)[keyof typeof SubmissionStatus]

}

export type StudentStatus = $Enums.StudentStatus

export const StudentStatus: typeof $Enums.StudentStatus

export type SubmissionStatus = $Enums.SubmissionStatus

export const SubmissionStatus: typeof $Enums.SubmissionStatus

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more StudentUsers
 * const studentUsers = await prisma.studentUser.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more StudentUsers
   * const studentUsers = await prisma.studentUser.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.studentUser`: Exposes CRUD operations for the **StudentUser** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more StudentUsers
    * const studentUsers = await prisma.studentUser.findMany()
    * ```
    */
  get studentUser(): Prisma.StudentUserDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.student`: Exposes CRUD operations for the **Student** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Students
    * const students = await prisma.student.findMany()
    * ```
    */
  get student(): Prisma.StudentDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.studentProgress`: Exposes CRUD operations for the **StudentProgress** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more StudentProgresses
    * const studentProgresses = await prisma.studentProgress.findMany()
    * ```
    */
  get studentProgress(): Prisma.StudentProgressDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.assignmentSubmission`: Exposes CRUD operations for the **AssignmentSubmission** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more AssignmentSubmissions
    * const assignmentSubmissions = await prisma.assignmentSubmission.findMany()
    * ```
    */
  get assignmentSubmission(): Prisma.AssignmentSubmissionDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.studentResourceAccess`: Exposes CRUD operations for the **StudentResourceAccess** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more StudentResourceAccesses
    * const studentResourceAccesses = await prisma.studentResourceAccess.findMany()
    * ```
    */
  get studentResourceAccess(): Prisma.StudentResourceAccessDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.studentSchedule`: Exposes CRUD operations for the **StudentSchedule** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more StudentSchedules
    * const studentSchedules = await prisma.studentSchedule.findMany()
    * ```
    */
  get studentSchedule(): Prisma.StudentScheduleDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.10.1
   * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    StudentUser: 'StudentUser',
    Student: 'Student',
    StudentProgress: 'StudentProgress',
    AssignmentSubmission: 'AssignmentSubmission',
    StudentResourceAccess: 'StudentResourceAccess',
    StudentSchedule: 'StudentSchedule'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "studentUser" | "student" | "studentProgress" | "assignmentSubmission" | "studentResourceAccess" | "studentSchedule"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      StudentUser: {
        payload: Prisma.$StudentUserPayload<ExtArgs>
        fields: Prisma.StudentUserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.StudentUserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentUserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.StudentUserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentUserPayload>
          }
          findFirst: {
            args: Prisma.StudentUserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentUserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.StudentUserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentUserPayload>
          }
          findMany: {
            args: Prisma.StudentUserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentUserPayload>[]
          }
          create: {
            args: Prisma.StudentUserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentUserPayload>
          }
          createMany: {
            args: Prisma.StudentUserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.StudentUserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentUserPayload>[]
          }
          delete: {
            args: Prisma.StudentUserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentUserPayload>
          }
          update: {
            args: Prisma.StudentUserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentUserPayload>
          }
          deleteMany: {
            args: Prisma.StudentUserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.StudentUserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.StudentUserUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentUserPayload>[]
          }
          upsert: {
            args: Prisma.StudentUserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentUserPayload>
          }
          aggregate: {
            args: Prisma.StudentUserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateStudentUser>
          }
          groupBy: {
            args: Prisma.StudentUserGroupByArgs<ExtArgs>
            result: $Utils.Optional<StudentUserGroupByOutputType>[]
          }
          count: {
            args: Prisma.StudentUserCountArgs<ExtArgs>
            result: $Utils.Optional<StudentUserCountAggregateOutputType> | number
          }
        }
      }
      Student: {
        payload: Prisma.$StudentPayload<ExtArgs>
        fields: Prisma.StudentFieldRefs
        operations: {
          findUnique: {
            args: Prisma.StudentFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.StudentFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentPayload>
          }
          findFirst: {
            args: Prisma.StudentFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.StudentFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentPayload>
          }
          findMany: {
            args: Prisma.StudentFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentPayload>[]
          }
          create: {
            args: Prisma.StudentCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentPayload>
          }
          createMany: {
            args: Prisma.StudentCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.StudentCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentPayload>[]
          }
          delete: {
            args: Prisma.StudentDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentPayload>
          }
          update: {
            args: Prisma.StudentUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentPayload>
          }
          deleteMany: {
            args: Prisma.StudentDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.StudentUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.StudentUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentPayload>[]
          }
          upsert: {
            args: Prisma.StudentUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentPayload>
          }
          aggregate: {
            args: Prisma.StudentAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateStudent>
          }
          groupBy: {
            args: Prisma.StudentGroupByArgs<ExtArgs>
            result: $Utils.Optional<StudentGroupByOutputType>[]
          }
          count: {
            args: Prisma.StudentCountArgs<ExtArgs>
            result: $Utils.Optional<StudentCountAggregateOutputType> | number
          }
        }
      }
      StudentProgress: {
        payload: Prisma.$StudentProgressPayload<ExtArgs>
        fields: Prisma.StudentProgressFieldRefs
        operations: {
          findUnique: {
            args: Prisma.StudentProgressFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentProgressPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.StudentProgressFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentProgressPayload>
          }
          findFirst: {
            args: Prisma.StudentProgressFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentProgressPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.StudentProgressFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentProgressPayload>
          }
          findMany: {
            args: Prisma.StudentProgressFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentProgressPayload>[]
          }
          create: {
            args: Prisma.StudentProgressCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentProgressPayload>
          }
          createMany: {
            args: Prisma.StudentProgressCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.StudentProgressCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentProgressPayload>[]
          }
          delete: {
            args: Prisma.StudentProgressDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentProgressPayload>
          }
          update: {
            args: Prisma.StudentProgressUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentProgressPayload>
          }
          deleteMany: {
            args: Prisma.StudentProgressDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.StudentProgressUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.StudentProgressUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentProgressPayload>[]
          }
          upsert: {
            args: Prisma.StudentProgressUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentProgressPayload>
          }
          aggregate: {
            args: Prisma.StudentProgressAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateStudentProgress>
          }
          groupBy: {
            args: Prisma.StudentProgressGroupByArgs<ExtArgs>
            result: $Utils.Optional<StudentProgressGroupByOutputType>[]
          }
          count: {
            args: Prisma.StudentProgressCountArgs<ExtArgs>
            result: $Utils.Optional<StudentProgressCountAggregateOutputType> | number
          }
        }
      }
      AssignmentSubmission: {
        payload: Prisma.$AssignmentSubmissionPayload<ExtArgs>
        fields: Prisma.AssignmentSubmissionFieldRefs
        operations: {
          findUnique: {
            args: Prisma.AssignmentSubmissionFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignmentSubmissionPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.AssignmentSubmissionFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignmentSubmissionPayload>
          }
          findFirst: {
            args: Prisma.AssignmentSubmissionFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignmentSubmissionPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.AssignmentSubmissionFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignmentSubmissionPayload>
          }
          findMany: {
            args: Prisma.AssignmentSubmissionFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignmentSubmissionPayload>[]
          }
          create: {
            args: Prisma.AssignmentSubmissionCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignmentSubmissionPayload>
          }
          createMany: {
            args: Prisma.AssignmentSubmissionCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.AssignmentSubmissionCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignmentSubmissionPayload>[]
          }
          delete: {
            args: Prisma.AssignmentSubmissionDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignmentSubmissionPayload>
          }
          update: {
            args: Prisma.AssignmentSubmissionUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignmentSubmissionPayload>
          }
          deleteMany: {
            args: Prisma.AssignmentSubmissionDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.AssignmentSubmissionUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.AssignmentSubmissionUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignmentSubmissionPayload>[]
          }
          upsert: {
            args: Prisma.AssignmentSubmissionUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignmentSubmissionPayload>
          }
          aggregate: {
            args: Prisma.AssignmentSubmissionAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateAssignmentSubmission>
          }
          groupBy: {
            args: Prisma.AssignmentSubmissionGroupByArgs<ExtArgs>
            result: $Utils.Optional<AssignmentSubmissionGroupByOutputType>[]
          }
          count: {
            args: Prisma.AssignmentSubmissionCountArgs<ExtArgs>
            result: $Utils.Optional<AssignmentSubmissionCountAggregateOutputType> | number
          }
        }
      }
      StudentResourceAccess: {
        payload: Prisma.$StudentResourceAccessPayload<ExtArgs>
        fields: Prisma.StudentResourceAccessFieldRefs
        operations: {
          findUnique: {
            args: Prisma.StudentResourceAccessFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentResourceAccessPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.StudentResourceAccessFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentResourceAccessPayload>
          }
          findFirst: {
            args: Prisma.StudentResourceAccessFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentResourceAccessPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.StudentResourceAccessFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentResourceAccessPayload>
          }
          findMany: {
            args: Prisma.StudentResourceAccessFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentResourceAccessPayload>[]
          }
          create: {
            args: Prisma.StudentResourceAccessCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentResourceAccessPayload>
          }
          createMany: {
            args: Prisma.StudentResourceAccessCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.StudentResourceAccessCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentResourceAccessPayload>[]
          }
          delete: {
            args: Prisma.StudentResourceAccessDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentResourceAccessPayload>
          }
          update: {
            args: Prisma.StudentResourceAccessUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentResourceAccessPayload>
          }
          deleteMany: {
            args: Prisma.StudentResourceAccessDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.StudentResourceAccessUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.StudentResourceAccessUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentResourceAccessPayload>[]
          }
          upsert: {
            args: Prisma.StudentResourceAccessUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentResourceAccessPayload>
          }
          aggregate: {
            args: Prisma.StudentResourceAccessAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateStudentResourceAccess>
          }
          groupBy: {
            args: Prisma.StudentResourceAccessGroupByArgs<ExtArgs>
            result: $Utils.Optional<StudentResourceAccessGroupByOutputType>[]
          }
          count: {
            args: Prisma.StudentResourceAccessCountArgs<ExtArgs>
            result: $Utils.Optional<StudentResourceAccessCountAggregateOutputType> | number
          }
        }
      }
      StudentSchedule: {
        payload: Prisma.$StudentSchedulePayload<ExtArgs>
        fields: Prisma.StudentScheduleFieldRefs
        operations: {
          findUnique: {
            args: Prisma.StudentScheduleFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentSchedulePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.StudentScheduleFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentSchedulePayload>
          }
          findFirst: {
            args: Prisma.StudentScheduleFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentSchedulePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.StudentScheduleFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentSchedulePayload>
          }
          findMany: {
            args: Prisma.StudentScheduleFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentSchedulePayload>[]
          }
          create: {
            args: Prisma.StudentScheduleCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentSchedulePayload>
          }
          createMany: {
            args: Prisma.StudentScheduleCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.StudentScheduleCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentSchedulePayload>[]
          }
          delete: {
            args: Prisma.StudentScheduleDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentSchedulePayload>
          }
          update: {
            args: Prisma.StudentScheduleUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentSchedulePayload>
          }
          deleteMany: {
            args: Prisma.StudentScheduleDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.StudentScheduleUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.StudentScheduleUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentSchedulePayload>[]
          }
          upsert: {
            args: Prisma.StudentScheduleUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StudentSchedulePayload>
          }
          aggregate: {
            args: Prisma.StudentScheduleAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateStudentSchedule>
          }
          groupBy: {
            args: Prisma.StudentScheduleGroupByArgs<ExtArgs>
            result: $Utils.Optional<StudentScheduleGroupByOutputType>[]
          }
          count: {
            args: Prisma.StudentScheduleCountArgs<ExtArgs>
            result: $Utils.Optional<StudentScheduleCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    studentUser?: StudentUserOmit
    student?: StudentOmit
    studentProgress?: StudentProgressOmit
    assignmentSubmission?: AssignmentSubmissionOmit
    studentResourceAccess?: StudentResourceAccessOmit
    studentSchedule?: StudentScheduleOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type StudentCountOutputType
   */

  export type StudentCountOutputType = {
    progress: number
    submissions: number
    resourceAccess: number
    schedules: number
  }

  export type StudentCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    progress?: boolean | StudentCountOutputTypeCountProgressArgs
    submissions?: boolean | StudentCountOutputTypeCountSubmissionsArgs
    resourceAccess?: boolean | StudentCountOutputTypeCountResourceAccessArgs
    schedules?: boolean | StudentCountOutputTypeCountSchedulesArgs
  }

  // Custom InputTypes
  /**
   * StudentCountOutputType without action
   */
  export type StudentCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentCountOutputType
     */
    select?: StudentCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * StudentCountOutputType without action
   */
  export type StudentCountOutputTypeCountProgressArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: StudentProgressWhereInput
  }

  /**
   * StudentCountOutputType without action
   */
  export type StudentCountOutputTypeCountSubmissionsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AssignmentSubmissionWhereInput
  }

  /**
   * StudentCountOutputType without action
   */
  export type StudentCountOutputTypeCountResourceAccessArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: StudentResourceAccessWhereInput
  }

  /**
   * StudentCountOutputType without action
   */
  export type StudentCountOutputTypeCountSchedulesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: StudentScheduleWhereInput
  }


  /**
   * Models
   */

  /**
   * Model StudentUser
   */

  export type AggregateStudentUser = {
    _count: StudentUserCountAggregateOutputType | null
    _min: StudentUserMinAggregateOutputType | null
    _max: StudentUserMaxAggregateOutputType | null
  }

  export type StudentUserMinAggregateOutputType = {
    id: string | null
    email: string | null
    passwordHash: string | null
    firstName: string | null
    lastName: string | null
    phone: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type StudentUserMaxAggregateOutputType = {
    id: string | null
    email: string | null
    passwordHash: string | null
    firstName: string | null
    lastName: string | null
    phone: string | null
    isActive: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type StudentUserCountAggregateOutputType = {
    id: number
    email: number
    passwordHash: number
    firstName: number
    lastName: number
    phone: number
    isActive: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type StudentUserMinAggregateInputType = {
    id?: true
    email?: true
    passwordHash?: true
    firstName?: true
    lastName?: true
    phone?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type StudentUserMaxAggregateInputType = {
    id?: true
    email?: true
    passwordHash?: true
    firstName?: true
    lastName?: true
    phone?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
  }

  export type StudentUserCountAggregateInputType = {
    id?: true
    email?: true
    passwordHash?: true
    firstName?: true
    lastName?: true
    phone?: true
    isActive?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type StudentUserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which StudentUser to aggregate.
     */
    where?: StudentUserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentUsers to fetch.
     */
    orderBy?: StudentUserOrderByWithRelationInput | StudentUserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: StudentUserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentUsers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentUsers.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned StudentUsers
    **/
    _count?: true | StudentUserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: StudentUserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: StudentUserMaxAggregateInputType
  }

  export type GetStudentUserAggregateType<T extends StudentUserAggregateArgs> = {
        [P in keyof T & keyof AggregateStudentUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateStudentUser[P]>
      : GetScalarType<T[P], AggregateStudentUser[P]>
  }




  export type StudentUserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: StudentUserWhereInput
    orderBy?: StudentUserOrderByWithAggregationInput | StudentUserOrderByWithAggregationInput[]
    by: StudentUserScalarFieldEnum[] | StudentUserScalarFieldEnum
    having?: StudentUserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: StudentUserCountAggregateInputType | true
    _min?: StudentUserMinAggregateInputType
    _max?: StudentUserMaxAggregateInputType
  }

  export type StudentUserGroupByOutputType = {
    id: string
    email: string
    passwordHash: string
    firstName: string
    lastName: string
    phone: string | null
    isActive: boolean
    createdAt: Date
    updatedAt: Date
    _count: StudentUserCountAggregateOutputType | null
    _min: StudentUserMinAggregateOutputType | null
    _max: StudentUserMaxAggregateOutputType | null
  }

  type GetStudentUserGroupByPayload<T extends StudentUserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<StudentUserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof StudentUserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], StudentUserGroupByOutputType[P]>
            : GetScalarType<T[P], StudentUserGroupByOutputType[P]>
        }
      >
    >


  export type StudentUserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    passwordHash?: boolean
    firstName?: boolean
    lastName?: boolean
    phone?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    student?: boolean | StudentUser$studentArgs<ExtArgs>
  }, ExtArgs["result"]["studentUser"]>

  export type StudentUserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    passwordHash?: boolean
    firstName?: boolean
    lastName?: boolean
    phone?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["studentUser"]>

  export type StudentUserSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    email?: boolean
    passwordHash?: boolean
    firstName?: boolean
    lastName?: boolean
    phone?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["studentUser"]>

  export type StudentUserSelectScalar = {
    id?: boolean
    email?: boolean
    passwordHash?: boolean
    firstName?: boolean
    lastName?: boolean
    phone?: boolean
    isActive?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type StudentUserOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "email" | "passwordHash" | "firstName" | "lastName" | "phone" | "isActive" | "createdAt" | "updatedAt", ExtArgs["result"]["studentUser"]>
  export type StudentUserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentUser$studentArgs<ExtArgs>
  }
  export type StudentUserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type StudentUserIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $StudentUserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "StudentUser"
    objects: {
      student: Prisma.$StudentPayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      email: string
      passwordHash: string
      firstName: string
      lastName: string
      phone: string | null
      isActive: boolean
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["studentUser"]>
    composites: {}
  }

  type StudentUserGetPayload<S extends boolean | null | undefined | StudentUserDefaultArgs> = $Result.GetResult<Prisma.$StudentUserPayload, S>

  type StudentUserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<StudentUserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: StudentUserCountAggregateInputType | true
    }

  export interface StudentUserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['StudentUser'], meta: { name: 'StudentUser' } }
    /**
     * Find zero or one StudentUser that matches the filter.
     * @param {StudentUserFindUniqueArgs} args - Arguments to find a StudentUser
     * @example
     * // Get one StudentUser
     * const studentUser = await prisma.studentUser.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends StudentUserFindUniqueArgs>(args: SelectSubset<T, StudentUserFindUniqueArgs<ExtArgs>>): Prisma__StudentUserClient<$Result.GetResult<Prisma.$StudentUserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one StudentUser that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {StudentUserFindUniqueOrThrowArgs} args - Arguments to find a StudentUser
     * @example
     * // Get one StudentUser
     * const studentUser = await prisma.studentUser.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends StudentUserFindUniqueOrThrowArgs>(args: SelectSubset<T, StudentUserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__StudentUserClient<$Result.GetResult<Prisma.$StudentUserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first StudentUser that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentUserFindFirstArgs} args - Arguments to find a StudentUser
     * @example
     * // Get one StudentUser
     * const studentUser = await prisma.studentUser.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends StudentUserFindFirstArgs>(args?: SelectSubset<T, StudentUserFindFirstArgs<ExtArgs>>): Prisma__StudentUserClient<$Result.GetResult<Prisma.$StudentUserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first StudentUser that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentUserFindFirstOrThrowArgs} args - Arguments to find a StudentUser
     * @example
     * // Get one StudentUser
     * const studentUser = await prisma.studentUser.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends StudentUserFindFirstOrThrowArgs>(args?: SelectSubset<T, StudentUserFindFirstOrThrowArgs<ExtArgs>>): Prisma__StudentUserClient<$Result.GetResult<Prisma.$StudentUserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more StudentUsers that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentUserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all StudentUsers
     * const studentUsers = await prisma.studentUser.findMany()
     * 
     * // Get first 10 StudentUsers
     * const studentUsers = await prisma.studentUser.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const studentUserWithIdOnly = await prisma.studentUser.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends StudentUserFindManyArgs>(args?: SelectSubset<T, StudentUserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentUserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a StudentUser.
     * @param {StudentUserCreateArgs} args - Arguments to create a StudentUser.
     * @example
     * // Create one StudentUser
     * const StudentUser = await prisma.studentUser.create({
     *   data: {
     *     // ... data to create a StudentUser
     *   }
     * })
     * 
     */
    create<T extends StudentUserCreateArgs>(args: SelectSubset<T, StudentUserCreateArgs<ExtArgs>>): Prisma__StudentUserClient<$Result.GetResult<Prisma.$StudentUserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many StudentUsers.
     * @param {StudentUserCreateManyArgs} args - Arguments to create many StudentUsers.
     * @example
     * // Create many StudentUsers
     * const studentUser = await prisma.studentUser.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends StudentUserCreateManyArgs>(args?: SelectSubset<T, StudentUserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many StudentUsers and returns the data saved in the database.
     * @param {StudentUserCreateManyAndReturnArgs} args - Arguments to create many StudentUsers.
     * @example
     * // Create many StudentUsers
     * const studentUser = await prisma.studentUser.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many StudentUsers and only return the `id`
     * const studentUserWithIdOnly = await prisma.studentUser.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends StudentUserCreateManyAndReturnArgs>(args?: SelectSubset<T, StudentUserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentUserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a StudentUser.
     * @param {StudentUserDeleteArgs} args - Arguments to delete one StudentUser.
     * @example
     * // Delete one StudentUser
     * const StudentUser = await prisma.studentUser.delete({
     *   where: {
     *     // ... filter to delete one StudentUser
     *   }
     * })
     * 
     */
    delete<T extends StudentUserDeleteArgs>(args: SelectSubset<T, StudentUserDeleteArgs<ExtArgs>>): Prisma__StudentUserClient<$Result.GetResult<Prisma.$StudentUserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one StudentUser.
     * @param {StudentUserUpdateArgs} args - Arguments to update one StudentUser.
     * @example
     * // Update one StudentUser
     * const studentUser = await prisma.studentUser.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends StudentUserUpdateArgs>(args: SelectSubset<T, StudentUserUpdateArgs<ExtArgs>>): Prisma__StudentUserClient<$Result.GetResult<Prisma.$StudentUserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more StudentUsers.
     * @param {StudentUserDeleteManyArgs} args - Arguments to filter StudentUsers to delete.
     * @example
     * // Delete a few StudentUsers
     * const { count } = await prisma.studentUser.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends StudentUserDeleteManyArgs>(args?: SelectSubset<T, StudentUserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more StudentUsers.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentUserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many StudentUsers
     * const studentUser = await prisma.studentUser.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends StudentUserUpdateManyArgs>(args: SelectSubset<T, StudentUserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more StudentUsers and returns the data updated in the database.
     * @param {StudentUserUpdateManyAndReturnArgs} args - Arguments to update many StudentUsers.
     * @example
     * // Update many StudentUsers
     * const studentUser = await prisma.studentUser.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more StudentUsers and only return the `id`
     * const studentUserWithIdOnly = await prisma.studentUser.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends StudentUserUpdateManyAndReturnArgs>(args: SelectSubset<T, StudentUserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentUserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one StudentUser.
     * @param {StudentUserUpsertArgs} args - Arguments to update or create a StudentUser.
     * @example
     * // Update or create a StudentUser
     * const studentUser = await prisma.studentUser.upsert({
     *   create: {
     *     // ... data to create a StudentUser
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the StudentUser we want to update
     *   }
     * })
     */
    upsert<T extends StudentUserUpsertArgs>(args: SelectSubset<T, StudentUserUpsertArgs<ExtArgs>>): Prisma__StudentUserClient<$Result.GetResult<Prisma.$StudentUserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of StudentUsers.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentUserCountArgs} args - Arguments to filter StudentUsers to count.
     * @example
     * // Count the number of StudentUsers
     * const count = await prisma.studentUser.count({
     *   where: {
     *     // ... the filter for the StudentUsers we want to count
     *   }
     * })
    **/
    count<T extends StudentUserCountArgs>(
      args?: Subset<T, StudentUserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], StudentUserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a StudentUser.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentUserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends StudentUserAggregateArgs>(args: Subset<T, StudentUserAggregateArgs>): Prisma.PrismaPromise<GetStudentUserAggregateType<T>>

    /**
     * Group by StudentUser.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentUserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends StudentUserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: StudentUserGroupByArgs['orderBy'] }
        : { orderBy?: StudentUserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, StudentUserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetStudentUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the StudentUser model
   */
  readonly fields: StudentUserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for StudentUser.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__StudentUserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    student<T extends StudentUser$studentArgs<ExtArgs> = {}>(args?: Subset<T, StudentUser$studentArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the StudentUser model
   */
  interface StudentUserFieldRefs {
    readonly id: FieldRef<"StudentUser", 'String'>
    readonly email: FieldRef<"StudentUser", 'String'>
    readonly passwordHash: FieldRef<"StudentUser", 'String'>
    readonly firstName: FieldRef<"StudentUser", 'String'>
    readonly lastName: FieldRef<"StudentUser", 'String'>
    readonly phone: FieldRef<"StudentUser", 'String'>
    readonly isActive: FieldRef<"StudentUser", 'Boolean'>
    readonly createdAt: FieldRef<"StudentUser", 'DateTime'>
    readonly updatedAt: FieldRef<"StudentUser", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * StudentUser findUnique
   */
  export type StudentUserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentUser
     */
    select?: StudentUserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentUser
     */
    omit?: StudentUserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentUserInclude<ExtArgs> | null
    /**
     * Filter, which StudentUser to fetch.
     */
    where: StudentUserWhereUniqueInput
  }

  /**
   * StudentUser findUniqueOrThrow
   */
  export type StudentUserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentUser
     */
    select?: StudentUserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentUser
     */
    omit?: StudentUserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentUserInclude<ExtArgs> | null
    /**
     * Filter, which StudentUser to fetch.
     */
    where: StudentUserWhereUniqueInput
  }

  /**
   * StudentUser findFirst
   */
  export type StudentUserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentUser
     */
    select?: StudentUserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentUser
     */
    omit?: StudentUserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentUserInclude<ExtArgs> | null
    /**
     * Filter, which StudentUser to fetch.
     */
    where?: StudentUserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentUsers to fetch.
     */
    orderBy?: StudentUserOrderByWithRelationInput | StudentUserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for StudentUsers.
     */
    cursor?: StudentUserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentUsers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentUsers.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of StudentUsers.
     */
    distinct?: StudentUserScalarFieldEnum | StudentUserScalarFieldEnum[]
  }

  /**
   * StudentUser findFirstOrThrow
   */
  export type StudentUserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentUser
     */
    select?: StudentUserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentUser
     */
    omit?: StudentUserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentUserInclude<ExtArgs> | null
    /**
     * Filter, which StudentUser to fetch.
     */
    where?: StudentUserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentUsers to fetch.
     */
    orderBy?: StudentUserOrderByWithRelationInput | StudentUserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for StudentUsers.
     */
    cursor?: StudentUserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentUsers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentUsers.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of StudentUsers.
     */
    distinct?: StudentUserScalarFieldEnum | StudentUserScalarFieldEnum[]
  }

  /**
   * StudentUser findMany
   */
  export type StudentUserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentUser
     */
    select?: StudentUserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentUser
     */
    omit?: StudentUserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentUserInclude<ExtArgs> | null
    /**
     * Filter, which StudentUsers to fetch.
     */
    where?: StudentUserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentUsers to fetch.
     */
    orderBy?: StudentUserOrderByWithRelationInput | StudentUserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing StudentUsers.
     */
    cursor?: StudentUserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentUsers from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentUsers.
     */
    skip?: number
    distinct?: StudentUserScalarFieldEnum | StudentUserScalarFieldEnum[]
  }

  /**
   * StudentUser create
   */
  export type StudentUserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentUser
     */
    select?: StudentUserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentUser
     */
    omit?: StudentUserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentUserInclude<ExtArgs> | null
    /**
     * The data needed to create a StudentUser.
     */
    data: XOR<StudentUserCreateInput, StudentUserUncheckedCreateInput>
  }

  /**
   * StudentUser createMany
   */
  export type StudentUserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many StudentUsers.
     */
    data: StudentUserCreateManyInput | StudentUserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * StudentUser createManyAndReturn
   */
  export type StudentUserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentUser
     */
    select?: StudentUserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the StudentUser
     */
    omit?: StudentUserOmit<ExtArgs> | null
    /**
     * The data used to create many StudentUsers.
     */
    data: StudentUserCreateManyInput | StudentUserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * StudentUser update
   */
  export type StudentUserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentUser
     */
    select?: StudentUserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentUser
     */
    omit?: StudentUserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentUserInclude<ExtArgs> | null
    /**
     * The data needed to update a StudentUser.
     */
    data: XOR<StudentUserUpdateInput, StudentUserUncheckedUpdateInput>
    /**
     * Choose, which StudentUser to update.
     */
    where: StudentUserWhereUniqueInput
  }

  /**
   * StudentUser updateMany
   */
  export type StudentUserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update StudentUsers.
     */
    data: XOR<StudentUserUpdateManyMutationInput, StudentUserUncheckedUpdateManyInput>
    /**
     * Filter which StudentUsers to update
     */
    where?: StudentUserWhereInput
    /**
     * Limit how many StudentUsers to update.
     */
    limit?: number
  }

  /**
   * StudentUser updateManyAndReturn
   */
  export type StudentUserUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentUser
     */
    select?: StudentUserSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the StudentUser
     */
    omit?: StudentUserOmit<ExtArgs> | null
    /**
     * The data used to update StudentUsers.
     */
    data: XOR<StudentUserUpdateManyMutationInput, StudentUserUncheckedUpdateManyInput>
    /**
     * Filter which StudentUsers to update
     */
    where?: StudentUserWhereInput
    /**
     * Limit how many StudentUsers to update.
     */
    limit?: number
  }

  /**
   * StudentUser upsert
   */
  export type StudentUserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentUser
     */
    select?: StudentUserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentUser
     */
    omit?: StudentUserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentUserInclude<ExtArgs> | null
    /**
     * The filter to search for the StudentUser to update in case it exists.
     */
    where: StudentUserWhereUniqueInput
    /**
     * In case the StudentUser found by the `where` argument doesn't exist, create a new StudentUser with this data.
     */
    create: XOR<StudentUserCreateInput, StudentUserUncheckedCreateInput>
    /**
     * In case the StudentUser was found with the provided `where` argument, update it with this data.
     */
    update: XOR<StudentUserUpdateInput, StudentUserUncheckedUpdateInput>
  }

  /**
   * StudentUser delete
   */
  export type StudentUserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentUser
     */
    select?: StudentUserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentUser
     */
    omit?: StudentUserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentUserInclude<ExtArgs> | null
    /**
     * Filter which StudentUser to delete.
     */
    where: StudentUserWhereUniqueInput
  }

  /**
   * StudentUser deleteMany
   */
  export type StudentUserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which StudentUsers to delete
     */
    where?: StudentUserWhereInput
    /**
     * Limit how many StudentUsers to delete.
     */
    limit?: number
  }

  /**
   * StudentUser.student
   */
  export type StudentUser$studentArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentInclude<ExtArgs> | null
    where?: StudentWhereInput
  }

  /**
   * StudentUser without action
   */
  export type StudentUserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentUser
     */
    select?: StudentUserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentUser
     */
    omit?: StudentUserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentUserInclude<ExtArgs> | null
  }


  /**
   * Model Student
   */

  export type AggregateStudent = {
    _count: StudentCountAggregateOutputType | null
    _min: StudentMinAggregateOutputType | null
    _max: StudentMaxAggregateOutputType | null
  }

  export type StudentMinAggregateOutputType = {
    id: string | null
    studentUserId: string | null
    studentId: string | null
    dateOfBirth: Date | null
    enrollmentDate: Date | null
    currentLevel: string | null
    status: $Enums.StudentStatus | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type StudentMaxAggregateOutputType = {
    id: string | null
    studentUserId: string | null
    studentId: string | null
    dateOfBirth: Date | null
    enrollmentDate: Date | null
    currentLevel: string | null
    status: $Enums.StudentStatus | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type StudentCountAggregateOutputType = {
    id: number
    studentUserId: number
    studentId: number
    dateOfBirth: number
    emergencyContact: number
    enrollmentDate: number
    currentLevel: number
    status: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type StudentMinAggregateInputType = {
    id?: true
    studentUserId?: true
    studentId?: true
    dateOfBirth?: true
    enrollmentDate?: true
    currentLevel?: true
    status?: true
    createdAt?: true
    updatedAt?: true
  }

  export type StudentMaxAggregateInputType = {
    id?: true
    studentUserId?: true
    studentId?: true
    dateOfBirth?: true
    enrollmentDate?: true
    currentLevel?: true
    status?: true
    createdAt?: true
    updatedAt?: true
  }

  export type StudentCountAggregateInputType = {
    id?: true
    studentUserId?: true
    studentId?: true
    dateOfBirth?: true
    emergencyContact?: true
    enrollmentDate?: true
    currentLevel?: true
    status?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type StudentAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Student to aggregate.
     */
    where?: StudentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Students to fetch.
     */
    orderBy?: StudentOrderByWithRelationInput | StudentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: StudentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Students from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Students.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Students
    **/
    _count?: true | StudentCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: StudentMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: StudentMaxAggregateInputType
  }

  export type GetStudentAggregateType<T extends StudentAggregateArgs> = {
        [P in keyof T & keyof AggregateStudent]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateStudent[P]>
      : GetScalarType<T[P], AggregateStudent[P]>
  }




  export type StudentGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: StudentWhereInput
    orderBy?: StudentOrderByWithAggregationInput | StudentOrderByWithAggregationInput[]
    by: StudentScalarFieldEnum[] | StudentScalarFieldEnum
    having?: StudentScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: StudentCountAggregateInputType | true
    _min?: StudentMinAggregateInputType
    _max?: StudentMaxAggregateInputType
  }

  export type StudentGroupByOutputType = {
    id: string
    studentUserId: string
    studentId: string
    dateOfBirth: Date | null
    emergencyContact: JsonValue | null
    enrollmentDate: Date
    currentLevel: string | null
    status: $Enums.StudentStatus
    createdAt: Date
    updatedAt: Date
    _count: StudentCountAggregateOutputType | null
    _min: StudentMinAggregateOutputType | null
    _max: StudentMaxAggregateOutputType | null
  }

  type GetStudentGroupByPayload<T extends StudentGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<StudentGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof StudentGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], StudentGroupByOutputType[P]>
            : GetScalarType<T[P], StudentGroupByOutputType[P]>
        }
      >
    >


  export type StudentSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentUserId?: boolean
    studentId?: boolean
    dateOfBirth?: boolean
    emergencyContact?: boolean
    enrollmentDate?: boolean
    currentLevel?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    studentUser?: boolean | StudentUserDefaultArgs<ExtArgs>
    progress?: boolean | Student$progressArgs<ExtArgs>
    submissions?: boolean | Student$submissionsArgs<ExtArgs>
    resourceAccess?: boolean | Student$resourceAccessArgs<ExtArgs>
    schedules?: boolean | Student$schedulesArgs<ExtArgs>
    _count?: boolean | StudentCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["student"]>

  export type StudentSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentUserId?: boolean
    studentId?: boolean
    dateOfBirth?: boolean
    emergencyContact?: boolean
    enrollmentDate?: boolean
    currentLevel?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    studentUser?: boolean | StudentUserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["student"]>

  export type StudentSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentUserId?: boolean
    studentId?: boolean
    dateOfBirth?: boolean
    emergencyContact?: boolean
    enrollmentDate?: boolean
    currentLevel?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    studentUser?: boolean | StudentUserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["student"]>

  export type StudentSelectScalar = {
    id?: boolean
    studentUserId?: boolean
    studentId?: boolean
    dateOfBirth?: boolean
    emergencyContact?: boolean
    enrollmentDate?: boolean
    currentLevel?: boolean
    status?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type StudentOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "studentUserId" | "studentId" | "dateOfBirth" | "emergencyContact" | "enrollmentDate" | "currentLevel" | "status" | "createdAt" | "updatedAt", ExtArgs["result"]["student"]>
  export type StudentInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    studentUser?: boolean | StudentUserDefaultArgs<ExtArgs>
    progress?: boolean | Student$progressArgs<ExtArgs>
    submissions?: boolean | Student$submissionsArgs<ExtArgs>
    resourceAccess?: boolean | Student$resourceAccessArgs<ExtArgs>
    schedules?: boolean | Student$schedulesArgs<ExtArgs>
    _count?: boolean | StudentCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type StudentIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    studentUser?: boolean | StudentUserDefaultArgs<ExtArgs>
  }
  export type StudentIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    studentUser?: boolean | StudentUserDefaultArgs<ExtArgs>
  }

  export type $StudentPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Student"
    objects: {
      studentUser: Prisma.$StudentUserPayload<ExtArgs>
      progress: Prisma.$StudentProgressPayload<ExtArgs>[]
      submissions: Prisma.$AssignmentSubmissionPayload<ExtArgs>[]
      resourceAccess: Prisma.$StudentResourceAccessPayload<ExtArgs>[]
      schedules: Prisma.$StudentSchedulePayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      studentUserId: string
      studentId: string
      dateOfBirth: Date | null
      emergencyContact: Prisma.JsonValue | null
      enrollmentDate: Date
      currentLevel: string | null
      status: $Enums.StudentStatus
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["student"]>
    composites: {}
  }

  type StudentGetPayload<S extends boolean | null | undefined | StudentDefaultArgs> = $Result.GetResult<Prisma.$StudentPayload, S>

  type StudentCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<StudentFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: StudentCountAggregateInputType | true
    }

  export interface StudentDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Student'], meta: { name: 'Student' } }
    /**
     * Find zero or one Student that matches the filter.
     * @param {StudentFindUniqueArgs} args - Arguments to find a Student
     * @example
     * // Get one Student
     * const student = await prisma.student.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends StudentFindUniqueArgs>(args: SelectSubset<T, StudentFindUniqueArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Student that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {StudentFindUniqueOrThrowArgs} args - Arguments to find a Student
     * @example
     * // Get one Student
     * const student = await prisma.student.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends StudentFindUniqueOrThrowArgs>(args: SelectSubset<T, StudentFindUniqueOrThrowArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Student that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentFindFirstArgs} args - Arguments to find a Student
     * @example
     * // Get one Student
     * const student = await prisma.student.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends StudentFindFirstArgs>(args?: SelectSubset<T, StudentFindFirstArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Student that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentFindFirstOrThrowArgs} args - Arguments to find a Student
     * @example
     * // Get one Student
     * const student = await prisma.student.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends StudentFindFirstOrThrowArgs>(args?: SelectSubset<T, StudentFindFirstOrThrowArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Students that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Students
     * const students = await prisma.student.findMany()
     * 
     * // Get first 10 Students
     * const students = await prisma.student.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const studentWithIdOnly = await prisma.student.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends StudentFindManyArgs>(args?: SelectSubset<T, StudentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Student.
     * @param {StudentCreateArgs} args - Arguments to create a Student.
     * @example
     * // Create one Student
     * const Student = await prisma.student.create({
     *   data: {
     *     // ... data to create a Student
     *   }
     * })
     * 
     */
    create<T extends StudentCreateArgs>(args: SelectSubset<T, StudentCreateArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Students.
     * @param {StudentCreateManyArgs} args - Arguments to create many Students.
     * @example
     * // Create many Students
     * const student = await prisma.student.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends StudentCreateManyArgs>(args?: SelectSubset<T, StudentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Students and returns the data saved in the database.
     * @param {StudentCreateManyAndReturnArgs} args - Arguments to create many Students.
     * @example
     * // Create many Students
     * const student = await prisma.student.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Students and only return the `id`
     * const studentWithIdOnly = await prisma.student.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends StudentCreateManyAndReturnArgs>(args?: SelectSubset<T, StudentCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Student.
     * @param {StudentDeleteArgs} args - Arguments to delete one Student.
     * @example
     * // Delete one Student
     * const Student = await prisma.student.delete({
     *   where: {
     *     // ... filter to delete one Student
     *   }
     * })
     * 
     */
    delete<T extends StudentDeleteArgs>(args: SelectSubset<T, StudentDeleteArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Student.
     * @param {StudentUpdateArgs} args - Arguments to update one Student.
     * @example
     * // Update one Student
     * const student = await prisma.student.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends StudentUpdateArgs>(args: SelectSubset<T, StudentUpdateArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Students.
     * @param {StudentDeleteManyArgs} args - Arguments to filter Students to delete.
     * @example
     * // Delete a few Students
     * const { count } = await prisma.student.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends StudentDeleteManyArgs>(args?: SelectSubset<T, StudentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Students.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Students
     * const student = await prisma.student.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends StudentUpdateManyArgs>(args: SelectSubset<T, StudentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Students and returns the data updated in the database.
     * @param {StudentUpdateManyAndReturnArgs} args - Arguments to update many Students.
     * @example
     * // Update many Students
     * const student = await prisma.student.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Students and only return the `id`
     * const studentWithIdOnly = await prisma.student.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends StudentUpdateManyAndReturnArgs>(args: SelectSubset<T, StudentUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Student.
     * @param {StudentUpsertArgs} args - Arguments to update or create a Student.
     * @example
     * // Update or create a Student
     * const student = await prisma.student.upsert({
     *   create: {
     *     // ... data to create a Student
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Student we want to update
     *   }
     * })
     */
    upsert<T extends StudentUpsertArgs>(args: SelectSubset<T, StudentUpsertArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Students.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentCountArgs} args - Arguments to filter Students to count.
     * @example
     * // Count the number of Students
     * const count = await prisma.student.count({
     *   where: {
     *     // ... the filter for the Students we want to count
     *   }
     * })
    **/
    count<T extends StudentCountArgs>(
      args?: Subset<T, StudentCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], StudentCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Student.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends StudentAggregateArgs>(args: Subset<T, StudentAggregateArgs>): Prisma.PrismaPromise<GetStudentAggregateType<T>>

    /**
     * Group by Student.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends StudentGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: StudentGroupByArgs['orderBy'] }
        : { orderBy?: StudentGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, StudentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetStudentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Student model
   */
  readonly fields: StudentFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Student.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__StudentClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    studentUser<T extends StudentUserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, StudentUserDefaultArgs<ExtArgs>>): Prisma__StudentUserClient<$Result.GetResult<Prisma.$StudentUserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    progress<T extends Student$progressArgs<ExtArgs> = {}>(args?: Subset<T, Student$progressArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentProgressPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    submissions<T extends Student$submissionsArgs<ExtArgs> = {}>(args?: Subset<T, Student$submissionsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AssignmentSubmissionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    resourceAccess<T extends Student$resourceAccessArgs<ExtArgs> = {}>(args?: Subset<T, Student$resourceAccessArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentResourceAccessPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    schedules<T extends Student$schedulesArgs<ExtArgs> = {}>(args?: Subset<T, Student$schedulesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentSchedulePayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Student model
   */
  interface StudentFieldRefs {
    readonly id: FieldRef<"Student", 'String'>
    readonly studentUserId: FieldRef<"Student", 'String'>
    readonly studentId: FieldRef<"Student", 'String'>
    readonly dateOfBirth: FieldRef<"Student", 'DateTime'>
    readonly emergencyContact: FieldRef<"Student", 'Json'>
    readonly enrollmentDate: FieldRef<"Student", 'DateTime'>
    readonly currentLevel: FieldRef<"Student", 'String'>
    readonly status: FieldRef<"Student", 'StudentStatus'>
    readonly createdAt: FieldRef<"Student", 'DateTime'>
    readonly updatedAt: FieldRef<"Student", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Student findUnique
   */
  export type StudentFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentInclude<ExtArgs> | null
    /**
     * Filter, which Student to fetch.
     */
    where: StudentWhereUniqueInput
  }

  /**
   * Student findUniqueOrThrow
   */
  export type StudentFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentInclude<ExtArgs> | null
    /**
     * Filter, which Student to fetch.
     */
    where: StudentWhereUniqueInput
  }

  /**
   * Student findFirst
   */
  export type StudentFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentInclude<ExtArgs> | null
    /**
     * Filter, which Student to fetch.
     */
    where?: StudentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Students to fetch.
     */
    orderBy?: StudentOrderByWithRelationInput | StudentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Students.
     */
    cursor?: StudentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Students from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Students.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Students.
     */
    distinct?: StudentScalarFieldEnum | StudentScalarFieldEnum[]
  }

  /**
   * Student findFirstOrThrow
   */
  export type StudentFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentInclude<ExtArgs> | null
    /**
     * Filter, which Student to fetch.
     */
    where?: StudentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Students to fetch.
     */
    orderBy?: StudentOrderByWithRelationInput | StudentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Students.
     */
    cursor?: StudentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Students from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Students.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Students.
     */
    distinct?: StudentScalarFieldEnum | StudentScalarFieldEnum[]
  }

  /**
   * Student findMany
   */
  export type StudentFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentInclude<ExtArgs> | null
    /**
     * Filter, which Students to fetch.
     */
    where?: StudentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Students to fetch.
     */
    orderBy?: StudentOrderByWithRelationInput | StudentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Students.
     */
    cursor?: StudentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Students from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Students.
     */
    skip?: number
    distinct?: StudentScalarFieldEnum | StudentScalarFieldEnum[]
  }

  /**
   * Student create
   */
  export type StudentCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentInclude<ExtArgs> | null
    /**
     * The data needed to create a Student.
     */
    data: XOR<StudentCreateInput, StudentUncheckedCreateInput>
  }

  /**
   * Student createMany
   */
  export type StudentCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Students.
     */
    data: StudentCreateManyInput | StudentCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Student createManyAndReturn
   */
  export type StudentCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * The data used to create many Students.
     */
    data: StudentCreateManyInput | StudentCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Student update
   */
  export type StudentUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentInclude<ExtArgs> | null
    /**
     * The data needed to update a Student.
     */
    data: XOR<StudentUpdateInput, StudentUncheckedUpdateInput>
    /**
     * Choose, which Student to update.
     */
    where: StudentWhereUniqueInput
  }

  /**
   * Student updateMany
   */
  export type StudentUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Students.
     */
    data: XOR<StudentUpdateManyMutationInput, StudentUncheckedUpdateManyInput>
    /**
     * Filter which Students to update
     */
    where?: StudentWhereInput
    /**
     * Limit how many Students to update.
     */
    limit?: number
  }

  /**
   * Student updateManyAndReturn
   */
  export type StudentUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * The data used to update Students.
     */
    data: XOR<StudentUpdateManyMutationInput, StudentUncheckedUpdateManyInput>
    /**
     * Filter which Students to update
     */
    where?: StudentWhereInput
    /**
     * Limit how many Students to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * Student upsert
   */
  export type StudentUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentInclude<ExtArgs> | null
    /**
     * The filter to search for the Student to update in case it exists.
     */
    where: StudentWhereUniqueInput
    /**
     * In case the Student found by the `where` argument doesn't exist, create a new Student with this data.
     */
    create: XOR<StudentCreateInput, StudentUncheckedCreateInput>
    /**
     * In case the Student was found with the provided `where` argument, update it with this data.
     */
    update: XOR<StudentUpdateInput, StudentUncheckedUpdateInput>
  }

  /**
   * Student delete
   */
  export type StudentDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentInclude<ExtArgs> | null
    /**
     * Filter which Student to delete.
     */
    where: StudentWhereUniqueInput
  }

  /**
   * Student deleteMany
   */
  export type StudentDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Students to delete
     */
    where?: StudentWhereInput
    /**
     * Limit how many Students to delete.
     */
    limit?: number
  }

  /**
   * Student.progress
   */
  export type Student$progressArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressInclude<ExtArgs> | null
    where?: StudentProgressWhereInput
    orderBy?: StudentProgressOrderByWithRelationInput | StudentProgressOrderByWithRelationInput[]
    cursor?: StudentProgressWhereUniqueInput
    take?: number
    skip?: number
    distinct?: StudentProgressScalarFieldEnum | StudentProgressScalarFieldEnum[]
  }

  /**
   * Student.submissions
   */
  export type Student$submissionsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionInclude<ExtArgs> | null
    where?: AssignmentSubmissionWhereInput
    orderBy?: AssignmentSubmissionOrderByWithRelationInput | AssignmentSubmissionOrderByWithRelationInput[]
    cursor?: AssignmentSubmissionWhereUniqueInput
    take?: number
    skip?: number
    distinct?: AssignmentSubmissionScalarFieldEnum | AssignmentSubmissionScalarFieldEnum[]
  }

  /**
   * Student.resourceAccess
   */
  export type Student$resourceAccessArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessInclude<ExtArgs> | null
    where?: StudentResourceAccessWhereInput
    orderBy?: StudentResourceAccessOrderByWithRelationInput | StudentResourceAccessOrderByWithRelationInput[]
    cursor?: StudentResourceAccessWhereUniqueInput
    take?: number
    skip?: number
    distinct?: StudentResourceAccessScalarFieldEnum | StudentResourceAccessScalarFieldEnum[]
  }

  /**
   * Student.schedules
   */
  export type Student$schedulesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleInclude<ExtArgs> | null
    where?: StudentScheduleWhereInput
    orderBy?: StudentScheduleOrderByWithRelationInput | StudentScheduleOrderByWithRelationInput[]
    cursor?: StudentScheduleWhereUniqueInput
    take?: number
    skip?: number
    distinct?: StudentScheduleScalarFieldEnum | StudentScheduleScalarFieldEnum[]
  }

  /**
   * Student without action
   */
  export type StudentDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Student
     */
    select?: StudentSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Student
     */
    omit?: StudentOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentInclude<ExtArgs> | null
  }


  /**
   * Model StudentProgress
   */

  export type AggregateStudentProgress = {
    _count: StudentProgressCountAggregateOutputType | null
    _avg: StudentProgressAvgAggregateOutputType | null
    _sum: StudentProgressSumAggregateOutputType | null
    _min: StudentProgressMinAggregateOutputType | null
    _max: StudentProgressMaxAggregateOutputType | null
  }

  export type StudentProgressAvgAggregateOutputType = {
    progressPercentage: number | null
  }

  export type StudentProgressSumAggregateOutputType = {
    progressPercentage: number | null
  }

  export type StudentProgressMinAggregateOutputType = {
    id: string | null
    studentId: string | null
    assignmentId: string | null
    progressPercentage: number | null
    grade: string | null
    feedback: string | null
    submittedAt: Date | null
    gradedAt: Date | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type StudentProgressMaxAggregateOutputType = {
    id: string | null
    studentId: string | null
    assignmentId: string | null
    progressPercentage: number | null
    grade: string | null
    feedback: string | null
    submittedAt: Date | null
    gradedAt: Date | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type StudentProgressCountAggregateOutputType = {
    id: number
    studentId: number
    assignmentId: number
    progressPercentage: number
    grade: number
    feedback: number
    submittedAt: number
    gradedAt: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type StudentProgressAvgAggregateInputType = {
    progressPercentage?: true
  }

  export type StudentProgressSumAggregateInputType = {
    progressPercentage?: true
  }

  export type StudentProgressMinAggregateInputType = {
    id?: true
    studentId?: true
    assignmentId?: true
    progressPercentage?: true
    grade?: true
    feedback?: true
    submittedAt?: true
    gradedAt?: true
    createdAt?: true
    updatedAt?: true
  }

  export type StudentProgressMaxAggregateInputType = {
    id?: true
    studentId?: true
    assignmentId?: true
    progressPercentage?: true
    grade?: true
    feedback?: true
    submittedAt?: true
    gradedAt?: true
    createdAt?: true
    updatedAt?: true
  }

  export type StudentProgressCountAggregateInputType = {
    id?: true
    studentId?: true
    assignmentId?: true
    progressPercentage?: true
    grade?: true
    feedback?: true
    submittedAt?: true
    gradedAt?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type StudentProgressAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which StudentProgress to aggregate.
     */
    where?: StudentProgressWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentProgresses to fetch.
     */
    orderBy?: StudentProgressOrderByWithRelationInput | StudentProgressOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: StudentProgressWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentProgresses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentProgresses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned StudentProgresses
    **/
    _count?: true | StudentProgressCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: StudentProgressAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: StudentProgressSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: StudentProgressMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: StudentProgressMaxAggregateInputType
  }

  export type GetStudentProgressAggregateType<T extends StudentProgressAggregateArgs> = {
        [P in keyof T & keyof AggregateStudentProgress]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateStudentProgress[P]>
      : GetScalarType<T[P], AggregateStudentProgress[P]>
  }




  export type StudentProgressGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: StudentProgressWhereInput
    orderBy?: StudentProgressOrderByWithAggregationInput | StudentProgressOrderByWithAggregationInput[]
    by: StudentProgressScalarFieldEnum[] | StudentProgressScalarFieldEnum
    having?: StudentProgressScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: StudentProgressCountAggregateInputType | true
    _avg?: StudentProgressAvgAggregateInputType
    _sum?: StudentProgressSumAggregateInputType
    _min?: StudentProgressMinAggregateInputType
    _max?: StudentProgressMaxAggregateInputType
  }

  export type StudentProgressGroupByOutputType = {
    id: string
    studentId: string
    assignmentId: string
    progressPercentage: number
    grade: string | null
    feedback: string | null
    submittedAt: Date | null
    gradedAt: Date | null
    createdAt: Date
    updatedAt: Date
    _count: StudentProgressCountAggregateOutputType | null
    _avg: StudentProgressAvgAggregateOutputType | null
    _sum: StudentProgressSumAggregateOutputType | null
    _min: StudentProgressMinAggregateOutputType | null
    _max: StudentProgressMaxAggregateOutputType | null
  }

  type GetStudentProgressGroupByPayload<T extends StudentProgressGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<StudentProgressGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof StudentProgressGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], StudentProgressGroupByOutputType[P]>
            : GetScalarType<T[P], StudentProgressGroupByOutputType[P]>
        }
      >
    >


  export type StudentProgressSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentId?: boolean
    assignmentId?: boolean
    progressPercentage?: boolean
    grade?: boolean
    feedback?: boolean
    submittedAt?: boolean
    gradedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["studentProgress"]>

  export type StudentProgressSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentId?: boolean
    assignmentId?: boolean
    progressPercentage?: boolean
    grade?: boolean
    feedback?: boolean
    submittedAt?: boolean
    gradedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["studentProgress"]>

  export type StudentProgressSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentId?: boolean
    assignmentId?: boolean
    progressPercentage?: boolean
    grade?: boolean
    feedback?: boolean
    submittedAt?: boolean
    gradedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["studentProgress"]>

  export type StudentProgressSelectScalar = {
    id?: boolean
    studentId?: boolean
    assignmentId?: boolean
    progressPercentage?: boolean
    grade?: boolean
    feedback?: boolean
    submittedAt?: boolean
    gradedAt?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type StudentProgressOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "studentId" | "assignmentId" | "progressPercentage" | "grade" | "feedback" | "submittedAt" | "gradedAt" | "createdAt" | "updatedAt", ExtArgs["result"]["studentProgress"]>
  export type StudentProgressInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }
  export type StudentProgressIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }
  export type StudentProgressIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }

  export type $StudentProgressPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "StudentProgress"
    objects: {
      student: Prisma.$StudentPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      studentId: string
      assignmentId: string
      progressPercentage: number
      grade: string | null
      feedback: string | null
      submittedAt: Date | null
      gradedAt: Date | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["studentProgress"]>
    composites: {}
  }

  type StudentProgressGetPayload<S extends boolean | null | undefined | StudentProgressDefaultArgs> = $Result.GetResult<Prisma.$StudentProgressPayload, S>

  type StudentProgressCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<StudentProgressFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: StudentProgressCountAggregateInputType | true
    }

  export interface StudentProgressDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['StudentProgress'], meta: { name: 'StudentProgress' } }
    /**
     * Find zero or one StudentProgress that matches the filter.
     * @param {StudentProgressFindUniqueArgs} args - Arguments to find a StudentProgress
     * @example
     * // Get one StudentProgress
     * const studentProgress = await prisma.studentProgress.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends StudentProgressFindUniqueArgs>(args: SelectSubset<T, StudentProgressFindUniqueArgs<ExtArgs>>): Prisma__StudentProgressClient<$Result.GetResult<Prisma.$StudentProgressPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one StudentProgress that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {StudentProgressFindUniqueOrThrowArgs} args - Arguments to find a StudentProgress
     * @example
     * // Get one StudentProgress
     * const studentProgress = await prisma.studentProgress.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends StudentProgressFindUniqueOrThrowArgs>(args: SelectSubset<T, StudentProgressFindUniqueOrThrowArgs<ExtArgs>>): Prisma__StudentProgressClient<$Result.GetResult<Prisma.$StudentProgressPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first StudentProgress that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentProgressFindFirstArgs} args - Arguments to find a StudentProgress
     * @example
     * // Get one StudentProgress
     * const studentProgress = await prisma.studentProgress.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends StudentProgressFindFirstArgs>(args?: SelectSubset<T, StudentProgressFindFirstArgs<ExtArgs>>): Prisma__StudentProgressClient<$Result.GetResult<Prisma.$StudentProgressPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first StudentProgress that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentProgressFindFirstOrThrowArgs} args - Arguments to find a StudentProgress
     * @example
     * // Get one StudentProgress
     * const studentProgress = await prisma.studentProgress.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends StudentProgressFindFirstOrThrowArgs>(args?: SelectSubset<T, StudentProgressFindFirstOrThrowArgs<ExtArgs>>): Prisma__StudentProgressClient<$Result.GetResult<Prisma.$StudentProgressPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more StudentProgresses that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentProgressFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all StudentProgresses
     * const studentProgresses = await prisma.studentProgress.findMany()
     * 
     * // Get first 10 StudentProgresses
     * const studentProgresses = await prisma.studentProgress.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const studentProgressWithIdOnly = await prisma.studentProgress.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends StudentProgressFindManyArgs>(args?: SelectSubset<T, StudentProgressFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentProgressPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a StudentProgress.
     * @param {StudentProgressCreateArgs} args - Arguments to create a StudentProgress.
     * @example
     * // Create one StudentProgress
     * const StudentProgress = await prisma.studentProgress.create({
     *   data: {
     *     // ... data to create a StudentProgress
     *   }
     * })
     * 
     */
    create<T extends StudentProgressCreateArgs>(args: SelectSubset<T, StudentProgressCreateArgs<ExtArgs>>): Prisma__StudentProgressClient<$Result.GetResult<Prisma.$StudentProgressPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many StudentProgresses.
     * @param {StudentProgressCreateManyArgs} args - Arguments to create many StudentProgresses.
     * @example
     * // Create many StudentProgresses
     * const studentProgress = await prisma.studentProgress.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends StudentProgressCreateManyArgs>(args?: SelectSubset<T, StudentProgressCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many StudentProgresses and returns the data saved in the database.
     * @param {StudentProgressCreateManyAndReturnArgs} args - Arguments to create many StudentProgresses.
     * @example
     * // Create many StudentProgresses
     * const studentProgress = await prisma.studentProgress.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many StudentProgresses and only return the `id`
     * const studentProgressWithIdOnly = await prisma.studentProgress.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends StudentProgressCreateManyAndReturnArgs>(args?: SelectSubset<T, StudentProgressCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentProgressPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a StudentProgress.
     * @param {StudentProgressDeleteArgs} args - Arguments to delete one StudentProgress.
     * @example
     * // Delete one StudentProgress
     * const StudentProgress = await prisma.studentProgress.delete({
     *   where: {
     *     // ... filter to delete one StudentProgress
     *   }
     * })
     * 
     */
    delete<T extends StudentProgressDeleteArgs>(args: SelectSubset<T, StudentProgressDeleteArgs<ExtArgs>>): Prisma__StudentProgressClient<$Result.GetResult<Prisma.$StudentProgressPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one StudentProgress.
     * @param {StudentProgressUpdateArgs} args - Arguments to update one StudentProgress.
     * @example
     * // Update one StudentProgress
     * const studentProgress = await prisma.studentProgress.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends StudentProgressUpdateArgs>(args: SelectSubset<T, StudentProgressUpdateArgs<ExtArgs>>): Prisma__StudentProgressClient<$Result.GetResult<Prisma.$StudentProgressPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more StudentProgresses.
     * @param {StudentProgressDeleteManyArgs} args - Arguments to filter StudentProgresses to delete.
     * @example
     * // Delete a few StudentProgresses
     * const { count } = await prisma.studentProgress.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends StudentProgressDeleteManyArgs>(args?: SelectSubset<T, StudentProgressDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more StudentProgresses.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentProgressUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many StudentProgresses
     * const studentProgress = await prisma.studentProgress.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends StudentProgressUpdateManyArgs>(args: SelectSubset<T, StudentProgressUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more StudentProgresses and returns the data updated in the database.
     * @param {StudentProgressUpdateManyAndReturnArgs} args - Arguments to update many StudentProgresses.
     * @example
     * // Update many StudentProgresses
     * const studentProgress = await prisma.studentProgress.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more StudentProgresses and only return the `id`
     * const studentProgressWithIdOnly = await prisma.studentProgress.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends StudentProgressUpdateManyAndReturnArgs>(args: SelectSubset<T, StudentProgressUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentProgressPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one StudentProgress.
     * @param {StudentProgressUpsertArgs} args - Arguments to update or create a StudentProgress.
     * @example
     * // Update or create a StudentProgress
     * const studentProgress = await prisma.studentProgress.upsert({
     *   create: {
     *     // ... data to create a StudentProgress
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the StudentProgress we want to update
     *   }
     * })
     */
    upsert<T extends StudentProgressUpsertArgs>(args: SelectSubset<T, StudentProgressUpsertArgs<ExtArgs>>): Prisma__StudentProgressClient<$Result.GetResult<Prisma.$StudentProgressPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of StudentProgresses.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentProgressCountArgs} args - Arguments to filter StudentProgresses to count.
     * @example
     * // Count the number of StudentProgresses
     * const count = await prisma.studentProgress.count({
     *   where: {
     *     // ... the filter for the StudentProgresses we want to count
     *   }
     * })
    **/
    count<T extends StudentProgressCountArgs>(
      args?: Subset<T, StudentProgressCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], StudentProgressCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a StudentProgress.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentProgressAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends StudentProgressAggregateArgs>(args: Subset<T, StudentProgressAggregateArgs>): Prisma.PrismaPromise<GetStudentProgressAggregateType<T>>

    /**
     * Group by StudentProgress.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentProgressGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends StudentProgressGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: StudentProgressGroupByArgs['orderBy'] }
        : { orderBy?: StudentProgressGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, StudentProgressGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetStudentProgressGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the StudentProgress model
   */
  readonly fields: StudentProgressFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for StudentProgress.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__StudentProgressClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    student<T extends StudentDefaultArgs<ExtArgs> = {}>(args?: Subset<T, StudentDefaultArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the StudentProgress model
   */
  interface StudentProgressFieldRefs {
    readonly id: FieldRef<"StudentProgress", 'String'>
    readonly studentId: FieldRef<"StudentProgress", 'String'>
    readonly assignmentId: FieldRef<"StudentProgress", 'String'>
    readonly progressPercentage: FieldRef<"StudentProgress", 'Int'>
    readonly grade: FieldRef<"StudentProgress", 'String'>
    readonly feedback: FieldRef<"StudentProgress", 'String'>
    readonly submittedAt: FieldRef<"StudentProgress", 'DateTime'>
    readonly gradedAt: FieldRef<"StudentProgress", 'DateTime'>
    readonly createdAt: FieldRef<"StudentProgress", 'DateTime'>
    readonly updatedAt: FieldRef<"StudentProgress", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * StudentProgress findUnique
   */
  export type StudentProgressFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressInclude<ExtArgs> | null
    /**
     * Filter, which StudentProgress to fetch.
     */
    where: StudentProgressWhereUniqueInput
  }

  /**
   * StudentProgress findUniqueOrThrow
   */
  export type StudentProgressFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressInclude<ExtArgs> | null
    /**
     * Filter, which StudentProgress to fetch.
     */
    where: StudentProgressWhereUniqueInput
  }

  /**
   * StudentProgress findFirst
   */
  export type StudentProgressFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressInclude<ExtArgs> | null
    /**
     * Filter, which StudentProgress to fetch.
     */
    where?: StudentProgressWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentProgresses to fetch.
     */
    orderBy?: StudentProgressOrderByWithRelationInput | StudentProgressOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for StudentProgresses.
     */
    cursor?: StudentProgressWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentProgresses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentProgresses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of StudentProgresses.
     */
    distinct?: StudentProgressScalarFieldEnum | StudentProgressScalarFieldEnum[]
  }

  /**
   * StudentProgress findFirstOrThrow
   */
  export type StudentProgressFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressInclude<ExtArgs> | null
    /**
     * Filter, which StudentProgress to fetch.
     */
    where?: StudentProgressWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentProgresses to fetch.
     */
    orderBy?: StudentProgressOrderByWithRelationInput | StudentProgressOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for StudentProgresses.
     */
    cursor?: StudentProgressWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentProgresses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentProgresses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of StudentProgresses.
     */
    distinct?: StudentProgressScalarFieldEnum | StudentProgressScalarFieldEnum[]
  }

  /**
   * StudentProgress findMany
   */
  export type StudentProgressFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressInclude<ExtArgs> | null
    /**
     * Filter, which StudentProgresses to fetch.
     */
    where?: StudentProgressWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentProgresses to fetch.
     */
    orderBy?: StudentProgressOrderByWithRelationInput | StudentProgressOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing StudentProgresses.
     */
    cursor?: StudentProgressWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentProgresses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentProgresses.
     */
    skip?: number
    distinct?: StudentProgressScalarFieldEnum | StudentProgressScalarFieldEnum[]
  }

  /**
   * StudentProgress create
   */
  export type StudentProgressCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressInclude<ExtArgs> | null
    /**
     * The data needed to create a StudentProgress.
     */
    data: XOR<StudentProgressCreateInput, StudentProgressUncheckedCreateInput>
  }

  /**
   * StudentProgress createMany
   */
  export type StudentProgressCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many StudentProgresses.
     */
    data: StudentProgressCreateManyInput | StudentProgressCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * StudentProgress createManyAndReturn
   */
  export type StudentProgressCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * The data used to create many StudentProgresses.
     */
    data: StudentProgressCreateManyInput | StudentProgressCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * StudentProgress update
   */
  export type StudentProgressUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressInclude<ExtArgs> | null
    /**
     * The data needed to update a StudentProgress.
     */
    data: XOR<StudentProgressUpdateInput, StudentProgressUncheckedUpdateInput>
    /**
     * Choose, which StudentProgress to update.
     */
    where: StudentProgressWhereUniqueInput
  }

  /**
   * StudentProgress updateMany
   */
  export type StudentProgressUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update StudentProgresses.
     */
    data: XOR<StudentProgressUpdateManyMutationInput, StudentProgressUncheckedUpdateManyInput>
    /**
     * Filter which StudentProgresses to update
     */
    where?: StudentProgressWhereInput
    /**
     * Limit how many StudentProgresses to update.
     */
    limit?: number
  }

  /**
   * StudentProgress updateManyAndReturn
   */
  export type StudentProgressUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * The data used to update StudentProgresses.
     */
    data: XOR<StudentProgressUpdateManyMutationInput, StudentProgressUncheckedUpdateManyInput>
    /**
     * Filter which StudentProgresses to update
     */
    where?: StudentProgressWhereInput
    /**
     * Limit how many StudentProgresses to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * StudentProgress upsert
   */
  export type StudentProgressUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressInclude<ExtArgs> | null
    /**
     * The filter to search for the StudentProgress to update in case it exists.
     */
    where: StudentProgressWhereUniqueInput
    /**
     * In case the StudentProgress found by the `where` argument doesn't exist, create a new StudentProgress with this data.
     */
    create: XOR<StudentProgressCreateInput, StudentProgressUncheckedCreateInput>
    /**
     * In case the StudentProgress was found with the provided `where` argument, update it with this data.
     */
    update: XOR<StudentProgressUpdateInput, StudentProgressUncheckedUpdateInput>
  }

  /**
   * StudentProgress delete
   */
  export type StudentProgressDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressInclude<ExtArgs> | null
    /**
     * Filter which StudentProgress to delete.
     */
    where: StudentProgressWhereUniqueInput
  }

  /**
   * StudentProgress deleteMany
   */
  export type StudentProgressDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which StudentProgresses to delete
     */
    where?: StudentProgressWhereInput
    /**
     * Limit how many StudentProgresses to delete.
     */
    limit?: number
  }

  /**
   * StudentProgress without action
   */
  export type StudentProgressDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentProgress
     */
    select?: StudentProgressSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentProgress
     */
    omit?: StudentProgressOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentProgressInclude<ExtArgs> | null
  }


  /**
   * Model AssignmentSubmission
   */

  export type AggregateAssignmentSubmission = {
    _count: AssignmentSubmissionCountAggregateOutputType | null
    _min: AssignmentSubmissionMinAggregateOutputType | null
    _max: AssignmentSubmissionMaxAggregateOutputType | null
  }

  export type AssignmentSubmissionMinAggregateOutputType = {
    id: string | null
    studentId: string | null
    assignmentId: string | null
    submissionContent: string | null
    fileUrl: string | null
    submittedAt: Date | null
    status: $Enums.SubmissionStatus | null
    teacherFeedback: string | null
    grade: string | null
    gradedAt: Date | null
  }

  export type AssignmentSubmissionMaxAggregateOutputType = {
    id: string | null
    studentId: string | null
    assignmentId: string | null
    submissionContent: string | null
    fileUrl: string | null
    submittedAt: Date | null
    status: $Enums.SubmissionStatus | null
    teacherFeedback: string | null
    grade: string | null
    gradedAt: Date | null
  }

  export type AssignmentSubmissionCountAggregateOutputType = {
    id: number
    studentId: number
    assignmentId: number
    submissionContent: number
    fileUrl: number
    submittedAt: number
    status: number
    teacherFeedback: number
    grade: number
    gradedAt: number
    _all: number
  }


  export type AssignmentSubmissionMinAggregateInputType = {
    id?: true
    studentId?: true
    assignmentId?: true
    submissionContent?: true
    fileUrl?: true
    submittedAt?: true
    status?: true
    teacherFeedback?: true
    grade?: true
    gradedAt?: true
  }

  export type AssignmentSubmissionMaxAggregateInputType = {
    id?: true
    studentId?: true
    assignmentId?: true
    submissionContent?: true
    fileUrl?: true
    submittedAt?: true
    status?: true
    teacherFeedback?: true
    grade?: true
    gradedAt?: true
  }

  export type AssignmentSubmissionCountAggregateInputType = {
    id?: true
    studentId?: true
    assignmentId?: true
    submissionContent?: true
    fileUrl?: true
    submittedAt?: true
    status?: true
    teacherFeedback?: true
    grade?: true
    gradedAt?: true
    _all?: true
  }

  export type AssignmentSubmissionAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AssignmentSubmission to aggregate.
     */
    where?: AssignmentSubmissionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AssignmentSubmissions to fetch.
     */
    orderBy?: AssignmentSubmissionOrderByWithRelationInput | AssignmentSubmissionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: AssignmentSubmissionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AssignmentSubmissions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AssignmentSubmissions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned AssignmentSubmissions
    **/
    _count?: true | AssignmentSubmissionCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: AssignmentSubmissionMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: AssignmentSubmissionMaxAggregateInputType
  }

  export type GetAssignmentSubmissionAggregateType<T extends AssignmentSubmissionAggregateArgs> = {
        [P in keyof T & keyof AggregateAssignmentSubmission]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateAssignmentSubmission[P]>
      : GetScalarType<T[P], AggregateAssignmentSubmission[P]>
  }




  export type AssignmentSubmissionGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AssignmentSubmissionWhereInput
    orderBy?: AssignmentSubmissionOrderByWithAggregationInput | AssignmentSubmissionOrderByWithAggregationInput[]
    by: AssignmentSubmissionScalarFieldEnum[] | AssignmentSubmissionScalarFieldEnum
    having?: AssignmentSubmissionScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: AssignmentSubmissionCountAggregateInputType | true
    _min?: AssignmentSubmissionMinAggregateInputType
    _max?: AssignmentSubmissionMaxAggregateInputType
  }

  export type AssignmentSubmissionGroupByOutputType = {
    id: string
    studentId: string
    assignmentId: string
    submissionContent: string | null
    fileUrl: string | null
    submittedAt: Date
    status: $Enums.SubmissionStatus
    teacherFeedback: string | null
    grade: string | null
    gradedAt: Date | null
    _count: AssignmentSubmissionCountAggregateOutputType | null
    _min: AssignmentSubmissionMinAggregateOutputType | null
    _max: AssignmentSubmissionMaxAggregateOutputType | null
  }

  type GetAssignmentSubmissionGroupByPayload<T extends AssignmentSubmissionGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<AssignmentSubmissionGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof AssignmentSubmissionGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], AssignmentSubmissionGroupByOutputType[P]>
            : GetScalarType<T[P], AssignmentSubmissionGroupByOutputType[P]>
        }
      >
    >


  export type AssignmentSubmissionSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentId?: boolean
    assignmentId?: boolean
    submissionContent?: boolean
    fileUrl?: boolean
    submittedAt?: boolean
    status?: boolean
    teacherFeedback?: boolean
    grade?: boolean
    gradedAt?: boolean
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["assignmentSubmission"]>

  export type AssignmentSubmissionSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentId?: boolean
    assignmentId?: boolean
    submissionContent?: boolean
    fileUrl?: boolean
    submittedAt?: boolean
    status?: boolean
    teacherFeedback?: boolean
    grade?: boolean
    gradedAt?: boolean
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["assignmentSubmission"]>

  export type AssignmentSubmissionSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentId?: boolean
    assignmentId?: boolean
    submissionContent?: boolean
    fileUrl?: boolean
    submittedAt?: boolean
    status?: boolean
    teacherFeedback?: boolean
    grade?: boolean
    gradedAt?: boolean
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["assignmentSubmission"]>

  export type AssignmentSubmissionSelectScalar = {
    id?: boolean
    studentId?: boolean
    assignmentId?: boolean
    submissionContent?: boolean
    fileUrl?: boolean
    submittedAt?: boolean
    status?: boolean
    teacherFeedback?: boolean
    grade?: boolean
    gradedAt?: boolean
  }

  export type AssignmentSubmissionOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "studentId" | "assignmentId" | "submissionContent" | "fileUrl" | "submittedAt" | "status" | "teacherFeedback" | "grade" | "gradedAt", ExtArgs["result"]["assignmentSubmission"]>
  export type AssignmentSubmissionInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }
  export type AssignmentSubmissionIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }
  export type AssignmentSubmissionIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }

  export type $AssignmentSubmissionPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "AssignmentSubmission"
    objects: {
      student: Prisma.$StudentPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      studentId: string
      assignmentId: string
      submissionContent: string | null
      fileUrl: string | null
      submittedAt: Date
      status: $Enums.SubmissionStatus
      teacherFeedback: string | null
      grade: string | null
      gradedAt: Date | null
    }, ExtArgs["result"]["assignmentSubmission"]>
    composites: {}
  }

  type AssignmentSubmissionGetPayload<S extends boolean | null | undefined | AssignmentSubmissionDefaultArgs> = $Result.GetResult<Prisma.$AssignmentSubmissionPayload, S>

  type AssignmentSubmissionCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<AssignmentSubmissionFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: AssignmentSubmissionCountAggregateInputType | true
    }

  export interface AssignmentSubmissionDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['AssignmentSubmission'], meta: { name: 'AssignmentSubmission' } }
    /**
     * Find zero or one AssignmentSubmission that matches the filter.
     * @param {AssignmentSubmissionFindUniqueArgs} args - Arguments to find a AssignmentSubmission
     * @example
     * // Get one AssignmentSubmission
     * const assignmentSubmission = await prisma.assignmentSubmission.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends AssignmentSubmissionFindUniqueArgs>(args: SelectSubset<T, AssignmentSubmissionFindUniqueArgs<ExtArgs>>): Prisma__AssignmentSubmissionClient<$Result.GetResult<Prisma.$AssignmentSubmissionPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one AssignmentSubmission that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {AssignmentSubmissionFindUniqueOrThrowArgs} args - Arguments to find a AssignmentSubmission
     * @example
     * // Get one AssignmentSubmission
     * const assignmentSubmission = await prisma.assignmentSubmission.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends AssignmentSubmissionFindUniqueOrThrowArgs>(args: SelectSubset<T, AssignmentSubmissionFindUniqueOrThrowArgs<ExtArgs>>): Prisma__AssignmentSubmissionClient<$Result.GetResult<Prisma.$AssignmentSubmissionPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first AssignmentSubmission that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignmentSubmissionFindFirstArgs} args - Arguments to find a AssignmentSubmission
     * @example
     * // Get one AssignmentSubmission
     * const assignmentSubmission = await prisma.assignmentSubmission.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends AssignmentSubmissionFindFirstArgs>(args?: SelectSubset<T, AssignmentSubmissionFindFirstArgs<ExtArgs>>): Prisma__AssignmentSubmissionClient<$Result.GetResult<Prisma.$AssignmentSubmissionPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first AssignmentSubmission that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignmentSubmissionFindFirstOrThrowArgs} args - Arguments to find a AssignmentSubmission
     * @example
     * // Get one AssignmentSubmission
     * const assignmentSubmission = await prisma.assignmentSubmission.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends AssignmentSubmissionFindFirstOrThrowArgs>(args?: SelectSubset<T, AssignmentSubmissionFindFirstOrThrowArgs<ExtArgs>>): Prisma__AssignmentSubmissionClient<$Result.GetResult<Prisma.$AssignmentSubmissionPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more AssignmentSubmissions that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignmentSubmissionFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all AssignmentSubmissions
     * const assignmentSubmissions = await prisma.assignmentSubmission.findMany()
     * 
     * // Get first 10 AssignmentSubmissions
     * const assignmentSubmissions = await prisma.assignmentSubmission.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const assignmentSubmissionWithIdOnly = await prisma.assignmentSubmission.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends AssignmentSubmissionFindManyArgs>(args?: SelectSubset<T, AssignmentSubmissionFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AssignmentSubmissionPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a AssignmentSubmission.
     * @param {AssignmentSubmissionCreateArgs} args - Arguments to create a AssignmentSubmission.
     * @example
     * // Create one AssignmentSubmission
     * const AssignmentSubmission = await prisma.assignmentSubmission.create({
     *   data: {
     *     // ... data to create a AssignmentSubmission
     *   }
     * })
     * 
     */
    create<T extends AssignmentSubmissionCreateArgs>(args: SelectSubset<T, AssignmentSubmissionCreateArgs<ExtArgs>>): Prisma__AssignmentSubmissionClient<$Result.GetResult<Prisma.$AssignmentSubmissionPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many AssignmentSubmissions.
     * @param {AssignmentSubmissionCreateManyArgs} args - Arguments to create many AssignmentSubmissions.
     * @example
     * // Create many AssignmentSubmissions
     * const assignmentSubmission = await prisma.assignmentSubmission.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends AssignmentSubmissionCreateManyArgs>(args?: SelectSubset<T, AssignmentSubmissionCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many AssignmentSubmissions and returns the data saved in the database.
     * @param {AssignmentSubmissionCreateManyAndReturnArgs} args - Arguments to create many AssignmentSubmissions.
     * @example
     * // Create many AssignmentSubmissions
     * const assignmentSubmission = await prisma.assignmentSubmission.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many AssignmentSubmissions and only return the `id`
     * const assignmentSubmissionWithIdOnly = await prisma.assignmentSubmission.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends AssignmentSubmissionCreateManyAndReturnArgs>(args?: SelectSubset<T, AssignmentSubmissionCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AssignmentSubmissionPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a AssignmentSubmission.
     * @param {AssignmentSubmissionDeleteArgs} args - Arguments to delete one AssignmentSubmission.
     * @example
     * // Delete one AssignmentSubmission
     * const AssignmentSubmission = await prisma.assignmentSubmission.delete({
     *   where: {
     *     // ... filter to delete one AssignmentSubmission
     *   }
     * })
     * 
     */
    delete<T extends AssignmentSubmissionDeleteArgs>(args: SelectSubset<T, AssignmentSubmissionDeleteArgs<ExtArgs>>): Prisma__AssignmentSubmissionClient<$Result.GetResult<Prisma.$AssignmentSubmissionPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one AssignmentSubmission.
     * @param {AssignmentSubmissionUpdateArgs} args - Arguments to update one AssignmentSubmission.
     * @example
     * // Update one AssignmentSubmission
     * const assignmentSubmission = await prisma.assignmentSubmission.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends AssignmentSubmissionUpdateArgs>(args: SelectSubset<T, AssignmentSubmissionUpdateArgs<ExtArgs>>): Prisma__AssignmentSubmissionClient<$Result.GetResult<Prisma.$AssignmentSubmissionPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more AssignmentSubmissions.
     * @param {AssignmentSubmissionDeleteManyArgs} args - Arguments to filter AssignmentSubmissions to delete.
     * @example
     * // Delete a few AssignmentSubmissions
     * const { count } = await prisma.assignmentSubmission.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends AssignmentSubmissionDeleteManyArgs>(args?: SelectSubset<T, AssignmentSubmissionDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AssignmentSubmissions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignmentSubmissionUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many AssignmentSubmissions
     * const assignmentSubmission = await prisma.assignmentSubmission.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends AssignmentSubmissionUpdateManyArgs>(args: SelectSubset<T, AssignmentSubmissionUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AssignmentSubmissions and returns the data updated in the database.
     * @param {AssignmentSubmissionUpdateManyAndReturnArgs} args - Arguments to update many AssignmentSubmissions.
     * @example
     * // Update many AssignmentSubmissions
     * const assignmentSubmission = await prisma.assignmentSubmission.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more AssignmentSubmissions and only return the `id`
     * const assignmentSubmissionWithIdOnly = await prisma.assignmentSubmission.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends AssignmentSubmissionUpdateManyAndReturnArgs>(args: SelectSubset<T, AssignmentSubmissionUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AssignmentSubmissionPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one AssignmentSubmission.
     * @param {AssignmentSubmissionUpsertArgs} args - Arguments to update or create a AssignmentSubmission.
     * @example
     * // Update or create a AssignmentSubmission
     * const assignmentSubmission = await prisma.assignmentSubmission.upsert({
     *   create: {
     *     // ... data to create a AssignmentSubmission
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the AssignmentSubmission we want to update
     *   }
     * })
     */
    upsert<T extends AssignmentSubmissionUpsertArgs>(args: SelectSubset<T, AssignmentSubmissionUpsertArgs<ExtArgs>>): Prisma__AssignmentSubmissionClient<$Result.GetResult<Prisma.$AssignmentSubmissionPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of AssignmentSubmissions.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignmentSubmissionCountArgs} args - Arguments to filter AssignmentSubmissions to count.
     * @example
     * // Count the number of AssignmentSubmissions
     * const count = await prisma.assignmentSubmission.count({
     *   where: {
     *     // ... the filter for the AssignmentSubmissions we want to count
     *   }
     * })
    **/
    count<T extends AssignmentSubmissionCountArgs>(
      args?: Subset<T, AssignmentSubmissionCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], AssignmentSubmissionCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a AssignmentSubmission.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignmentSubmissionAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends AssignmentSubmissionAggregateArgs>(args: Subset<T, AssignmentSubmissionAggregateArgs>): Prisma.PrismaPromise<GetAssignmentSubmissionAggregateType<T>>

    /**
     * Group by AssignmentSubmission.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignmentSubmissionGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends AssignmentSubmissionGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: AssignmentSubmissionGroupByArgs['orderBy'] }
        : { orderBy?: AssignmentSubmissionGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, AssignmentSubmissionGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAssignmentSubmissionGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the AssignmentSubmission model
   */
  readonly fields: AssignmentSubmissionFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for AssignmentSubmission.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__AssignmentSubmissionClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    student<T extends StudentDefaultArgs<ExtArgs> = {}>(args?: Subset<T, StudentDefaultArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the AssignmentSubmission model
   */
  interface AssignmentSubmissionFieldRefs {
    readonly id: FieldRef<"AssignmentSubmission", 'String'>
    readonly studentId: FieldRef<"AssignmentSubmission", 'String'>
    readonly assignmentId: FieldRef<"AssignmentSubmission", 'String'>
    readonly submissionContent: FieldRef<"AssignmentSubmission", 'String'>
    readonly fileUrl: FieldRef<"AssignmentSubmission", 'String'>
    readonly submittedAt: FieldRef<"AssignmentSubmission", 'DateTime'>
    readonly status: FieldRef<"AssignmentSubmission", 'SubmissionStatus'>
    readonly teacherFeedback: FieldRef<"AssignmentSubmission", 'String'>
    readonly grade: FieldRef<"AssignmentSubmission", 'String'>
    readonly gradedAt: FieldRef<"AssignmentSubmission", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * AssignmentSubmission findUnique
   */
  export type AssignmentSubmissionFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionInclude<ExtArgs> | null
    /**
     * Filter, which AssignmentSubmission to fetch.
     */
    where: AssignmentSubmissionWhereUniqueInput
  }

  /**
   * AssignmentSubmission findUniqueOrThrow
   */
  export type AssignmentSubmissionFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionInclude<ExtArgs> | null
    /**
     * Filter, which AssignmentSubmission to fetch.
     */
    where: AssignmentSubmissionWhereUniqueInput
  }

  /**
   * AssignmentSubmission findFirst
   */
  export type AssignmentSubmissionFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionInclude<ExtArgs> | null
    /**
     * Filter, which AssignmentSubmission to fetch.
     */
    where?: AssignmentSubmissionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AssignmentSubmissions to fetch.
     */
    orderBy?: AssignmentSubmissionOrderByWithRelationInput | AssignmentSubmissionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AssignmentSubmissions.
     */
    cursor?: AssignmentSubmissionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AssignmentSubmissions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AssignmentSubmissions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AssignmentSubmissions.
     */
    distinct?: AssignmentSubmissionScalarFieldEnum | AssignmentSubmissionScalarFieldEnum[]
  }

  /**
   * AssignmentSubmission findFirstOrThrow
   */
  export type AssignmentSubmissionFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionInclude<ExtArgs> | null
    /**
     * Filter, which AssignmentSubmission to fetch.
     */
    where?: AssignmentSubmissionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AssignmentSubmissions to fetch.
     */
    orderBy?: AssignmentSubmissionOrderByWithRelationInput | AssignmentSubmissionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AssignmentSubmissions.
     */
    cursor?: AssignmentSubmissionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AssignmentSubmissions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AssignmentSubmissions.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AssignmentSubmissions.
     */
    distinct?: AssignmentSubmissionScalarFieldEnum | AssignmentSubmissionScalarFieldEnum[]
  }

  /**
   * AssignmentSubmission findMany
   */
  export type AssignmentSubmissionFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionInclude<ExtArgs> | null
    /**
     * Filter, which AssignmentSubmissions to fetch.
     */
    where?: AssignmentSubmissionWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AssignmentSubmissions to fetch.
     */
    orderBy?: AssignmentSubmissionOrderByWithRelationInput | AssignmentSubmissionOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing AssignmentSubmissions.
     */
    cursor?: AssignmentSubmissionWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AssignmentSubmissions from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AssignmentSubmissions.
     */
    skip?: number
    distinct?: AssignmentSubmissionScalarFieldEnum | AssignmentSubmissionScalarFieldEnum[]
  }

  /**
   * AssignmentSubmission create
   */
  export type AssignmentSubmissionCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionInclude<ExtArgs> | null
    /**
     * The data needed to create a AssignmentSubmission.
     */
    data: XOR<AssignmentSubmissionCreateInput, AssignmentSubmissionUncheckedCreateInput>
  }

  /**
   * AssignmentSubmission createMany
   */
  export type AssignmentSubmissionCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many AssignmentSubmissions.
     */
    data: AssignmentSubmissionCreateManyInput | AssignmentSubmissionCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * AssignmentSubmission createManyAndReturn
   */
  export type AssignmentSubmissionCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * The data used to create many AssignmentSubmissions.
     */
    data: AssignmentSubmissionCreateManyInput | AssignmentSubmissionCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * AssignmentSubmission update
   */
  export type AssignmentSubmissionUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionInclude<ExtArgs> | null
    /**
     * The data needed to update a AssignmentSubmission.
     */
    data: XOR<AssignmentSubmissionUpdateInput, AssignmentSubmissionUncheckedUpdateInput>
    /**
     * Choose, which AssignmentSubmission to update.
     */
    where: AssignmentSubmissionWhereUniqueInput
  }

  /**
   * AssignmentSubmission updateMany
   */
  export type AssignmentSubmissionUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update AssignmentSubmissions.
     */
    data: XOR<AssignmentSubmissionUpdateManyMutationInput, AssignmentSubmissionUncheckedUpdateManyInput>
    /**
     * Filter which AssignmentSubmissions to update
     */
    where?: AssignmentSubmissionWhereInput
    /**
     * Limit how many AssignmentSubmissions to update.
     */
    limit?: number
  }

  /**
   * AssignmentSubmission updateManyAndReturn
   */
  export type AssignmentSubmissionUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * The data used to update AssignmentSubmissions.
     */
    data: XOR<AssignmentSubmissionUpdateManyMutationInput, AssignmentSubmissionUncheckedUpdateManyInput>
    /**
     * Filter which AssignmentSubmissions to update
     */
    where?: AssignmentSubmissionWhereInput
    /**
     * Limit how many AssignmentSubmissions to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * AssignmentSubmission upsert
   */
  export type AssignmentSubmissionUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionInclude<ExtArgs> | null
    /**
     * The filter to search for the AssignmentSubmission to update in case it exists.
     */
    where: AssignmentSubmissionWhereUniqueInput
    /**
     * In case the AssignmentSubmission found by the `where` argument doesn't exist, create a new AssignmentSubmission with this data.
     */
    create: XOR<AssignmentSubmissionCreateInput, AssignmentSubmissionUncheckedCreateInput>
    /**
     * In case the AssignmentSubmission was found with the provided `where` argument, update it with this data.
     */
    update: XOR<AssignmentSubmissionUpdateInput, AssignmentSubmissionUncheckedUpdateInput>
  }

  /**
   * AssignmentSubmission delete
   */
  export type AssignmentSubmissionDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionInclude<ExtArgs> | null
    /**
     * Filter which AssignmentSubmission to delete.
     */
    where: AssignmentSubmissionWhereUniqueInput
  }

  /**
   * AssignmentSubmission deleteMany
   */
  export type AssignmentSubmissionDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AssignmentSubmissions to delete
     */
    where?: AssignmentSubmissionWhereInput
    /**
     * Limit how many AssignmentSubmissions to delete.
     */
    limit?: number
  }

  /**
   * AssignmentSubmission without action
   */
  export type AssignmentSubmissionDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignmentSubmission
     */
    select?: AssignmentSubmissionSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignmentSubmission
     */
    omit?: AssignmentSubmissionOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignmentSubmissionInclude<ExtArgs> | null
  }


  /**
   * Model StudentResourceAccess
   */

  export type AggregateStudentResourceAccess = {
    _count: StudentResourceAccessCountAggregateOutputType | null
    _avg: StudentResourceAccessAvgAggregateOutputType | null
    _sum: StudentResourceAccessSumAggregateOutputType | null
    _min: StudentResourceAccessMinAggregateOutputType | null
    _max: StudentResourceAccessMaxAggregateOutputType | null
  }

  export type StudentResourceAccessAvgAggregateOutputType = {
    accessDuration: number | null
  }

  export type StudentResourceAccessSumAggregateOutputType = {
    accessDuration: number | null
  }

  export type StudentResourceAccessMinAggregateOutputType = {
    id: string | null
    studentId: string | null
    resourceId: string | null
    accessedAt: Date | null
    accessDuration: number | null
  }

  export type StudentResourceAccessMaxAggregateOutputType = {
    id: string | null
    studentId: string | null
    resourceId: string | null
    accessedAt: Date | null
    accessDuration: number | null
  }

  export type StudentResourceAccessCountAggregateOutputType = {
    id: number
    studentId: number
    resourceId: number
    accessedAt: number
    accessDuration: number
    _all: number
  }


  export type StudentResourceAccessAvgAggregateInputType = {
    accessDuration?: true
  }

  export type StudentResourceAccessSumAggregateInputType = {
    accessDuration?: true
  }

  export type StudentResourceAccessMinAggregateInputType = {
    id?: true
    studentId?: true
    resourceId?: true
    accessedAt?: true
    accessDuration?: true
  }

  export type StudentResourceAccessMaxAggregateInputType = {
    id?: true
    studentId?: true
    resourceId?: true
    accessedAt?: true
    accessDuration?: true
  }

  export type StudentResourceAccessCountAggregateInputType = {
    id?: true
    studentId?: true
    resourceId?: true
    accessedAt?: true
    accessDuration?: true
    _all?: true
  }

  export type StudentResourceAccessAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which StudentResourceAccess to aggregate.
     */
    where?: StudentResourceAccessWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentResourceAccesses to fetch.
     */
    orderBy?: StudentResourceAccessOrderByWithRelationInput | StudentResourceAccessOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: StudentResourceAccessWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentResourceAccesses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentResourceAccesses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned StudentResourceAccesses
    **/
    _count?: true | StudentResourceAccessCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: StudentResourceAccessAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: StudentResourceAccessSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: StudentResourceAccessMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: StudentResourceAccessMaxAggregateInputType
  }

  export type GetStudentResourceAccessAggregateType<T extends StudentResourceAccessAggregateArgs> = {
        [P in keyof T & keyof AggregateStudentResourceAccess]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateStudentResourceAccess[P]>
      : GetScalarType<T[P], AggregateStudentResourceAccess[P]>
  }




  export type StudentResourceAccessGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: StudentResourceAccessWhereInput
    orderBy?: StudentResourceAccessOrderByWithAggregationInput | StudentResourceAccessOrderByWithAggregationInput[]
    by: StudentResourceAccessScalarFieldEnum[] | StudentResourceAccessScalarFieldEnum
    having?: StudentResourceAccessScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: StudentResourceAccessCountAggregateInputType | true
    _avg?: StudentResourceAccessAvgAggregateInputType
    _sum?: StudentResourceAccessSumAggregateInputType
    _min?: StudentResourceAccessMinAggregateInputType
    _max?: StudentResourceAccessMaxAggregateInputType
  }

  export type StudentResourceAccessGroupByOutputType = {
    id: string
    studentId: string
    resourceId: string
    accessedAt: Date
    accessDuration: number | null
    _count: StudentResourceAccessCountAggregateOutputType | null
    _avg: StudentResourceAccessAvgAggregateOutputType | null
    _sum: StudentResourceAccessSumAggregateOutputType | null
    _min: StudentResourceAccessMinAggregateOutputType | null
    _max: StudentResourceAccessMaxAggregateOutputType | null
  }

  type GetStudentResourceAccessGroupByPayload<T extends StudentResourceAccessGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<StudentResourceAccessGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof StudentResourceAccessGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], StudentResourceAccessGroupByOutputType[P]>
            : GetScalarType<T[P], StudentResourceAccessGroupByOutputType[P]>
        }
      >
    >


  export type StudentResourceAccessSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentId?: boolean
    resourceId?: boolean
    accessedAt?: boolean
    accessDuration?: boolean
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["studentResourceAccess"]>

  export type StudentResourceAccessSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentId?: boolean
    resourceId?: boolean
    accessedAt?: boolean
    accessDuration?: boolean
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["studentResourceAccess"]>

  export type StudentResourceAccessSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentId?: boolean
    resourceId?: boolean
    accessedAt?: boolean
    accessDuration?: boolean
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["studentResourceAccess"]>

  export type StudentResourceAccessSelectScalar = {
    id?: boolean
    studentId?: boolean
    resourceId?: boolean
    accessedAt?: boolean
    accessDuration?: boolean
  }

  export type StudentResourceAccessOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "studentId" | "resourceId" | "accessedAt" | "accessDuration", ExtArgs["result"]["studentResourceAccess"]>
  export type StudentResourceAccessInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }
  export type StudentResourceAccessIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }
  export type StudentResourceAccessIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }

  export type $StudentResourceAccessPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "StudentResourceAccess"
    objects: {
      student: Prisma.$StudentPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      studentId: string
      resourceId: string
      accessedAt: Date
      accessDuration: number | null
    }, ExtArgs["result"]["studentResourceAccess"]>
    composites: {}
  }

  type StudentResourceAccessGetPayload<S extends boolean | null | undefined | StudentResourceAccessDefaultArgs> = $Result.GetResult<Prisma.$StudentResourceAccessPayload, S>

  type StudentResourceAccessCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<StudentResourceAccessFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: StudentResourceAccessCountAggregateInputType | true
    }

  export interface StudentResourceAccessDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['StudentResourceAccess'], meta: { name: 'StudentResourceAccess' } }
    /**
     * Find zero or one StudentResourceAccess that matches the filter.
     * @param {StudentResourceAccessFindUniqueArgs} args - Arguments to find a StudentResourceAccess
     * @example
     * // Get one StudentResourceAccess
     * const studentResourceAccess = await prisma.studentResourceAccess.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends StudentResourceAccessFindUniqueArgs>(args: SelectSubset<T, StudentResourceAccessFindUniqueArgs<ExtArgs>>): Prisma__StudentResourceAccessClient<$Result.GetResult<Prisma.$StudentResourceAccessPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one StudentResourceAccess that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {StudentResourceAccessFindUniqueOrThrowArgs} args - Arguments to find a StudentResourceAccess
     * @example
     * // Get one StudentResourceAccess
     * const studentResourceAccess = await prisma.studentResourceAccess.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends StudentResourceAccessFindUniqueOrThrowArgs>(args: SelectSubset<T, StudentResourceAccessFindUniqueOrThrowArgs<ExtArgs>>): Prisma__StudentResourceAccessClient<$Result.GetResult<Prisma.$StudentResourceAccessPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first StudentResourceAccess that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentResourceAccessFindFirstArgs} args - Arguments to find a StudentResourceAccess
     * @example
     * // Get one StudentResourceAccess
     * const studentResourceAccess = await prisma.studentResourceAccess.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends StudentResourceAccessFindFirstArgs>(args?: SelectSubset<T, StudentResourceAccessFindFirstArgs<ExtArgs>>): Prisma__StudentResourceAccessClient<$Result.GetResult<Prisma.$StudentResourceAccessPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first StudentResourceAccess that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentResourceAccessFindFirstOrThrowArgs} args - Arguments to find a StudentResourceAccess
     * @example
     * // Get one StudentResourceAccess
     * const studentResourceAccess = await prisma.studentResourceAccess.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends StudentResourceAccessFindFirstOrThrowArgs>(args?: SelectSubset<T, StudentResourceAccessFindFirstOrThrowArgs<ExtArgs>>): Prisma__StudentResourceAccessClient<$Result.GetResult<Prisma.$StudentResourceAccessPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more StudentResourceAccesses that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentResourceAccessFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all StudentResourceAccesses
     * const studentResourceAccesses = await prisma.studentResourceAccess.findMany()
     * 
     * // Get first 10 StudentResourceAccesses
     * const studentResourceAccesses = await prisma.studentResourceAccess.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const studentResourceAccessWithIdOnly = await prisma.studentResourceAccess.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends StudentResourceAccessFindManyArgs>(args?: SelectSubset<T, StudentResourceAccessFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentResourceAccessPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a StudentResourceAccess.
     * @param {StudentResourceAccessCreateArgs} args - Arguments to create a StudentResourceAccess.
     * @example
     * // Create one StudentResourceAccess
     * const StudentResourceAccess = await prisma.studentResourceAccess.create({
     *   data: {
     *     // ... data to create a StudentResourceAccess
     *   }
     * })
     * 
     */
    create<T extends StudentResourceAccessCreateArgs>(args: SelectSubset<T, StudentResourceAccessCreateArgs<ExtArgs>>): Prisma__StudentResourceAccessClient<$Result.GetResult<Prisma.$StudentResourceAccessPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many StudentResourceAccesses.
     * @param {StudentResourceAccessCreateManyArgs} args - Arguments to create many StudentResourceAccesses.
     * @example
     * // Create many StudentResourceAccesses
     * const studentResourceAccess = await prisma.studentResourceAccess.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends StudentResourceAccessCreateManyArgs>(args?: SelectSubset<T, StudentResourceAccessCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many StudentResourceAccesses and returns the data saved in the database.
     * @param {StudentResourceAccessCreateManyAndReturnArgs} args - Arguments to create many StudentResourceAccesses.
     * @example
     * // Create many StudentResourceAccesses
     * const studentResourceAccess = await prisma.studentResourceAccess.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many StudentResourceAccesses and only return the `id`
     * const studentResourceAccessWithIdOnly = await prisma.studentResourceAccess.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends StudentResourceAccessCreateManyAndReturnArgs>(args?: SelectSubset<T, StudentResourceAccessCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentResourceAccessPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a StudentResourceAccess.
     * @param {StudentResourceAccessDeleteArgs} args - Arguments to delete one StudentResourceAccess.
     * @example
     * // Delete one StudentResourceAccess
     * const StudentResourceAccess = await prisma.studentResourceAccess.delete({
     *   where: {
     *     // ... filter to delete one StudentResourceAccess
     *   }
     * })
     * 
     */
    delete<T extends StudentResourceAccessDeleteArgs>(args: SelectSubset<T, StudentResourceAccessDeleteArgs<ExtArgs>>): Prisma__StudentResourceAccessClient<$Result.GetResult<Prisma.$StudentResourceAccessPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one StudentResourceAccess.
     * @param {StudentResourceAccessUpdateArgs} args - Arguments to update one StudentResourceAccess.
     * @example
     * // Update one StudentResourceAccess
     * const studentResourceAccess = await prisma.studentResourceAccess.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends StudentResourceAccessUpdateArgs>(args: SelectSubset<T, StudentResourceAccessUpdateArgs<ExtArgs>>): Prisma__StudentResourceAccessClient<$Result.GetResult<Prisma.$StudentResourceAccessPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more StudentResourceAccesses.
     * @param {StudentResourceAccessDeleteManyArgs} args - Arguments to filter StudentResourceAccesses to delete.
     * @example
     * // Delete a few StudentResourceAccesses
     * const { count } = await prisma.studentResourceAccess.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends StudentResourceAccessDeleteManyArgs>(args?: SelectSubset<T, StudentResourceAccessDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more StudentResourceAccesses.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentResourceAccessUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many StudentResourceAccesses
     * const studentResourceAccess = await prisma.studentResourceAccess.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends StudentResourceAccessUpdateManyArgs>(args: SelectSubset<T, StudentResourceAccessUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more StudentResourceAccesses and returns the data updated in the database.
     * @param {StudentResourceAccessUpdateManyAndReturnArgs} args - Arguments to update many StudentResourceAccesses.
     * @example
     * // Update many StudentResourceAccesses
     * const studentResourceAccess = await prisma.studentResourceAccess.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more StudentResourceAccesses and only return the `id`
     * const studentResourceAccessWithIdOnly = await prisma.studentResourceAccess.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends StudentResourceAccessUpdateManyAndReturnArgs>(args: SelectSubset<T, StudentResourceAccessUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentResourceAccessPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one StudentResourceAccess.
     * @param {StudentResourceAccessUpsertArgs} args - Arguments to update or create a StudentResourceAccess.
     * @example
     * // Update or create a StudentResourceAccess
     * const studentResourceAccess = await prisma.studentResourceAccess.upsert({
     *   create: {
     *     // ... data to create a StudentResourceAccess
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the StudentResourceAccess we want to update
     *   }
     * })
     */
    upsert<T extends StudentResourceAccessUpsertArgs>(args: SelectSubset<T, StudentResourceAccessUpsertArgs<ExtArgs>>): Prisma__StudentResourceAccessClient<$Result.GetResult<Prisma.$StudentResourceAccessPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of StudentResourceAccesses.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentResourceAccessCountArgs} args - Arguments to filter StudentResourceAccesses to count.
     * @example
     * // Count the number of StudentResourceAccesses
     * const count = await prisma.studentResourceAccess.count({
     *   where: {
     *     // ... the filter for the StudentResourceAccesses we want to count
     *   }
     * })
    **/
    count<T extends StudentResourceAccessCountArgs>(
      args?: Subset<T, StudentResourceAccessCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], StudentResourceAccessCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a StudentResourceAccess.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentResourceAccessAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends StudentResourceAccessAggregateArgs>(args: Subset<T, StudentResourceAccessAggregateArgs>): Prisma.PrismaPromise<GetStudentResourceAccessAggregateType<T>>

    /**
     * Group by StudentResourceAccess.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentResourceAccessGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends StudentResourceAccessGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: StudentResourceAccessGroupByArgs['orderBy'] }
        : { orderBy?: StudentResourceAccessGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, StudentResourceAccessGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetStudentResourceAccessGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the StudentResourceAccess model
   */
  readonly fields: StudentResourceAccessFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for StudentResourceAccess.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__StudentResourceAccessClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    student<T extends StudentDefaultArgs<ExtArgs> = {}>(args?: Subset<T, StudentDefaultArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the StudentResourceAccess model
   */
  interface StudentResourceAccessFieldRefs {
    readonly id: FieldRef<"StudentResourceAccess", 'String'>
    readonly studentId: FieldRef<"StudentResourceAccess", 'String'>
    readonly resourceId: FieldRef<"StudentResourceAccess", 'String'>
    readonly accessedAt: FieldRef<"StudentResourceAccess", 'DateTime'>
    readonly accessDuration: FieldRef<"StudentResourceAccess", 'Int'>
  }
    

  // Custom InputTypes
  /**
   * StudentResourceAccess findUnique
   */
  export type StudentResourceAccessFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessInclude<ExtArgs> | null
    /**
     * Filter, which StudentResourceAccess to fetch.
     */
    where: StudentResourceAccessWhereUniqueInput
  }

  /**
   * StudentResourceAccess findUniqueOrThrow
   */
  export type StudentResourceAccessFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessInclude<ExtArgs> | null
    /**
     * Filter, which StudentResourceAccess to fetch.
     */
    where: StudentResourceAccessWhereUniqueInput
  }

  /**
   * StudentResourceAccess findFirst
   */
  export type StudentResourceAccessFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessInclude<ExtArgs> | null
    /**
     * Filter, which StudentResourceAccess to fetch.
     */
    where?: StudentResourceAccessWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentResourceAccesses to fetch.
     */
    orderBy?: StudentResourceAccessOrderByWithRelationInput | StudentResourceAccessOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for StudentResourceAccesses.
     */
    cursor?: StudentResourceAccessWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentResourceAccesses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentResourceAccesses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of StudentResourceAccesses.
     */
    distinct?: StudentResourceAccessScalarFieldEnum | StudentResourceAccessScalarFieldEnum[]
  }

  /**
   * StudentResourceAccess findFirstOrThrow
   */
  export type StudentResourceAccessFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessInclude<ExtArgs> | null
    /**
     * Filter, which StudentResourceAccess to fetch.
     */
    where?: StudentResourceAccessWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentResourceAccesses to fetch.
     */
    orderBy?: StudentResourceAccessOrderByWithRelationInput | StudentResourceAccessOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for StudentResourceAccesses.
     */
    cursor?: StudentResourceAccessWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentResourceAccesses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentResourceAccesses.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of StudentResourceAccesses.
     */
    distinct?: StudentResourceAccessScalarFieldEnum | StudentResourceAccessScalarFieldEnum[]
  }

  /**
   * StudentResourceAccess findMany
   */
  export type StudentResourceAccessFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessInclude<ExtArgs> | null
    /**
     * Filter, which StudentResourceAccesses to fetch.
     */
    where?: StudentResourceAccessWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentResourceAccesses to fetch.
     */
    orderBy?: StudentResourceAccessOrderByWithRelationInput | StudentResourceAccessOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing StudentResourceAccesses.
     */
    cursor?: StudentResourceAccessWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentResourceAccesses from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentResourceAccesses.
     */
    skip?: number
    distinct?: StudentResourceAccessScalarFieldEnum | StudentResourceAccessScalarFieldEnum[]
  }

  /**
   * StudentResourceAccess create
   */
  export type StudentResourceAccessCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessInclude<ExtArgs> | null
    /**
     * The data needed to create a StudentResourceAccess.
     */
    data: XOR<StudentResourceAccessCreateInput, StudentResourceAccessUncheckedCreateInput>
  }

  /**
   * StudentResourceAccess createMany
   */
  export type StudentResourceAccessCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many StudentResourceAccesses.
     */
    data: StudentResourceAccessCreateManyInput | StudentResourceAccessCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * StudentResourceAccess createManyAndReturn
   */
  export type StudentResourceAccessCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * The data used to create many StudentResourceAccesses.
     */
    data: StudentResourceAccessCreateManyInput | StudentResourceAccessCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * StudentResourceAccess update
   */
  export type StudentResourceAccessUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessInclude<ExtArgs> | null
    /**
     * The data needed to update a StudentResourceAccess.
     */
    data: XOR<StudentResourceAccessUpdateInput, StudentResourceAccessUncheckedUpdateInput>
    /**
     * Choose, which StudentResourceAccess to update.
     */
    where: StudentResourceAccessWhereUniqueInput
  }

  /**
   * StudentResourceAccess updateMany
   */
  export type StudentResourceAccessUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update StudentResourceAccesses.
     */
    data: XOR<StudentResourceAccessUpdateManyMutationInput, StudentResourceAccessUncheckedUpdateManyInput>
    /**
     * Filter which StudentResourceAccesses to update
     */
    where?: StudentResourceAccessWhereInput
    /**
     * Limit how many StudentResourceAccesses to update.
     */
    limit?: number
  }

  /**
   * StudentResourceAccess updateManyAndReturn
   */
  export type StudentResourceAccessUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * The data used to update StudentResourceAccesses.
     */
    data: XOR<StudentResourceAccessUpdateManyMutationInput, StudentResourceAccessUncheckedUpdateManyInput>
    /**
     * Filter which StudentResourceAccesses to update
     */
    where?: StudentResourceAccessWhereInput
    /**
     * Limit how many StudentResourceAccesses to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * StudentResourceAccess upsert
   */
  export type StudentResourceAccessUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessInclude<ExtArgs> | null
    /**
     * The filter to search for the StudentResourceAccess to update in case it exists.
     */
    where: StudentResourceAccessWhereUniqueInput
    /**
     * In case the StudentResourceAccess found by the `where` argument doesn't exist, create a new StudentResourceAccess with this data.
     */
    create: XOR<StudentResourceAccessCreateInput, StudentResourceAccessUncheckedCreateInput>
    /**
     * In case the StudentResourceAccess was found with the provided `where` argument, update it with this data.
     */
    update: XOR<StudentResourceAccessUpdateInput, StudentResourceAccessUncheckedUpdateInput>
  }

  /**
   * StudentResourceAccess delete
   */
  export type StudentResourceAccessDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessInclude<ExtArgs> | null
    /**
     * Filter which StudentResourceAccess to delete.
     */
    where: StudentResourceAccessWhereUniqueInput
  }

  /**
   * StudentResourceAccess deleteMany
   */
  export type StudentResourceAccessDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which StudentResourceAccesses to delete
     */
    where?: StudentResourceAccessWhereInput
    /**
     * Limit how many StudentResourceAccesses to delete.
     */
    limit?: number
  }

  /**
   * StudentResourceAccess without action
   */
  export type StudentResourceAccessDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentResourceAccess
     */
    select?: StudentResourceAccessSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentResourceAccess
     */
    omit?: StudentResourceAccessOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentResourceAccessInclude<ExtArgs> | null
  }


  /**
   * Model StudentSchedule
   */

  export type AggregateStudentSchedule = {
    _count: StudentScheduleCountAggregateOutputType | null
    _min: StudentScheduleMinAggregateOutputType | null
    _max: StudentScheduleMaxAggregateOutputType | null
  }

  export type StudentScheduleMinAggregateOutputType = {
    id: string | null
    studentId: string | null
    groupId: string | null
    courseTitle: string | null
    teacherName: string | null
    cabinetNumber: string | null
    startDate: Date | null
    endDate: Date | null
    isActive: boolean | null
    lastUpdated: Date | null
  }

  export type StudentScheduleMaxAggregateOutputType = {
    id: string | null
    studentId: string | null
    groupId: string | null
    courseTitle: string | null
    teacherName: string | null
    cabinetNumber: string | null
    startDate: Date | null
    endDate: Date | null
    isActive: boolean | null
    lastUpdated: Date | null
  }

  export type StudentScheduleCountAggregateOutputType = {
    id: number
    studentId: number
    groupId: number
    courseTitle: number
    teacherName: number
    cabinetNumber: number
    schedule: number
    startDate: number
    endDate: number
    isActive: number
    lastUpdated: number
    _all: number
  }


  export type StudentScheduleMinAggregateInputType = {
    id?: true
    studentId?: true
    groupId?: true
    courseTitle?: true
    teacherName?: true
    cabinetNumber?: true
    startDate?: true
    endDate?: true
    isActive?: true
    lastUpdated?: true
  }

  export type StudentScheduleMaxAggregateInputType = {
    id?: true
    studentId?: true
    groupId?: true
    courseTitle?: true
    teacherName?: true
    cabinetNumber?: true
    startDate?: true
    endDate?: true
    isActive?: true
    lastUpdated?: true
  }

  export type StudentScheduleCountAggregateInputType = {
    id?: true
    studentId?: true
    groupId?: true
    courseTitle?: true
    teacherName?: true
    cabinetNumber?: true
    schedule?: true
    startDate?: true
    endDate?: true
    isActive?: true
    lastUpdated?: true
    _all?: true
  }

  export type StudentScheduleAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which StudentSchedule to aggregate.
     */
    where?: StudentScheduleWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentSchedules to fetch.
     */
    orderBy?: StudentScheduleOrderByWithRelationInput | StudentScheduleOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: StudentScheduleWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentSchedules from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentSchedules.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned StudentSchedules
    **/
    _count?: true | StudentScheduleCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: StudentScheduleMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: StudentScheduleMaxAggregateInputType
  }

  export type GetStudentScheduleAggregateType<T extends StudentScheduleAggregateArgs> = {
        [P in keyof T & keyof AggregateStudentSchedule]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateStudentSchedule[P]>
      : GetScalarType<T[P], AggregateStudentSchedule[P]>
  }




  export type StudentScheduleGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: StudentScheduleWhereInput
    orderBy?: StudentScheduleOrderByWithAggregationInput | StudentScheduleOrderByWithAggregationInput[]
    by: StudentScheduleScalarFieldEnum[] | StudentScheduleScalarFieldEnum
    having?: StudentScheduleScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: StudentScheduleCountAggregateInputType | true
    _min?: StudentScheduleMinAggregateInputType
    _max?: StudentScheduleMaxAggregateInputType
  }

  export type StudentScheduleGroupByOutputType = {
    id: string
    studentId: string
    groupId: string
    courseTitle: string
    teacherName: string
    cabinetNumber: string
    schedule: JsonValue
    startDate: Date
    endDate: Date | null
    isActive: boolean
    lastUpdated: Date
    _count: StudentScheduleCountAggregateOutputType | null
    _min: StudentScheduleMinAggregateOutputType | null
    _max: StudentScheduleMaxAggregateOutputType | null
  }

  type GetStudentScheduleGroupByPayload<T extends StudentScheduleGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<StudentScheduleGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof StudentScheduleGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], StudentScheduleGroupByOutputType[P]>
            : GetScalarType<T[P], StudentScheduleGroupByOutputType[P]>
        }
      >
    >


  export type StudentScheduleSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentId?: boolean
    groupId?: boolean
    courseTitle?: boolean
    teacherName?: boolean
    cabinetNumber?: boolean
    schedule?: boolean
    startDate?: boolean
    endDate?: boolean
    isActive?: boolean
    lastUpdated?: boolean
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["studentSchedule"]>

  export type StudentScheduleSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentId?: boolean
    groupId?: boolean
    courseTitle?: boolean
    teacherName?: boolean
    cabinetNumber?: boolean
    schedule?: boolean
    startDate?: boolean
    endDate?: boolean
    isActive?: boolean
    lastUpdated?: boolean
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["studentSchedule"]>

  export type StudentScheduleSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    studentId?: boolean
    groupId?: boolean
    courseTitle?: boolean
    teacherName?: boolean
    cabinetNumber?: boolean
    schedule?: boolean
    startDate?: boolean
    endDate?: boolean
    isActive?: boolean
    lastUpdated?: boolean
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["studentSchedule"]>

  export type StudentScheduleSelectScalar = {
    id?: boolean
    studentId?: boolean
    groupId?: boolean
    courseTitle?: boolean
    teacherName?: boolean
    cabinetNumber?: boolean
    schedule?: boolean
    startDate?: boolean
    endDate?: boolean
    isActive?: boolean
    lastUpdated?: boolean
  }

  export type StudentScheduleOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "studentId" | "groupId" | "courseTitle" | "teacherName" | "cabinetNumber" | "schedule" | "startDate" | "endDate" | "isActive" | "lastUpdated", ExtArgs["result"]["studentSchedule"]>
  export type StudentScheduleInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }
  export type StudentScheduleIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }
  export type StudentScheduleIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    student?: boolean | StudentDefaultArgs<ExtArgs>
  }

  export type $StudentSchedulePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "StudentSchedule"
    objects: {
      student: Prisma.$StudentPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      studentId: string
      groupId: string
      courseTitle: string
      teacherName: string
      cabinetNumber: string
      schedule: Prisma.JsonValue
      startDate: Date
      endDate: Date | null
      isActive: boolean
      lastUpdated: Date
    }, ExtArgs["result"]["studentSchedule"]>
    composites: {}
  }

  type StudentScheduleGetPayload<S extends boolean | null | undefined | StudentScheduleDefaultArgs> = $Result.GetResult<Prisma.$StudentSchedulePayload, S>

  type StudentScheduleCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<StudentScheduleFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: StudentScheduleCountAggregateInputType | true
    }

  export interface StudentScheduleDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['StudentSchedule'], meta: { name: 'StudentSchedule' } }
    /**
     * Find zero or one StudentSchedule that matches the filter.
     * @param {StudentScheduleFindUniqueArgs} args - Arguments to find a StudentSchedule
     * @example
     * // Get one StudentSchedule
     * const studentSchedule = await prisma.studentSchedule.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends StudentScheduleFindUniqueArgs>(args: SelectSubset<T, StudentScheduleFindUniqueArgs<ExtArgs>>): Prisma__StudentScheduleClient<$Result.GetResult<Prisma.$StudentSchedulePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one StudentSchedule that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {StudentScheduleFindUniqueOrThrowArgs} args - Arguments to find a StudentSchedule
     * @example
     * // Get one StudentSchedule
     * const studentSchedule = await prisma.studentSchedule.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends StudentScheduleFindUniqueOrThrowArgs>(args: SelectSubset<T, StudentScheduleFindUniqueOrThrowArgs<ExtArgs>>): Prisma__StudentScheduleClient<$Result.GetResult<Prisma.$StudentSchedulePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first StudentSchedule that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentScheduleFindFirstArgs} args - Arguments to find a StudentSchedule
     * @example
     * // Get one StudentSchedule
     * const studentSchedule = await prisma.studentSchedule.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends StudentScheduleFindFirstArgs>(args?: SelectSubset<T, StudentScheduleFindFirstArgs<ExtArgs>>): Prisma__StudentScheduleClient<$Result.GetResult<Prisma.$StudentSchedulePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first StudentSchedule that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentScheduleFindFirstOrThrowArgs} args - Arguments to find a StudentSchedule
     * @example
     * // Get one StudentSchedule
     * const studentSchedule = await prisma.studentSchedule.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends StudentScheduleFindFirstOrThrowArgs>(args?: SelectSubset<T, StudentScheduleFindFirstOrThrowArgs<ExtArgs>>): Prisma__StudentScheduleClient<$Result.GetResult<Prisma.$StudentSchedulePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more StudentSchedules that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentScheduleFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all StudentSchedules
     * const studentSchedules = await prisma.studentSchedule.findMany()
     * 
     * // Get first 10 StudentSchedules
     * const studentSchedules = await prisma.studentSchedule.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const studentScheduleWithIdOnly = await prisma.studentSchedule.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends StudentScheduleFindManyArgs>(args?: SelectSubset<T, StudentScheduleFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentSchedulePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a StudentSchedule.
     * @param {StudentScheduleCreateArgs} args - Arguments to create a StudentSchedule.
     * @example
     * // Create one StudentSchedule
     * const StudentSchedule = await prisma.studentSchedule.create({
     *   data: {
     *     // ... data to create a StudentSchedule
     *   }
     * })
     * 
     */
    create<T extends StudentScheduleCreateArgs>(args: SelectSubset<T, StudentScheduleCreateArgs<ExtArgs>>): Prisma__StudentScheduleClient<$Result.GetResult<Prisma.$StudentSchedulePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many StudentSchedules.
     * @param {StudentScheduleCreateManyArgs} args - Arguments to create many StudentSchedules.
     * @example
     * // Create many StudentSchedules
     * const studentSchedule = await prisma.studentSchedule.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends StudentScheduleCreateManyArgs>(args?: SelectSubset<T, StudentScheduleCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many StudentSchedules and returns the data saved in the database.
     * @param {StudentScheduleCreateManyAndReturnArgs} args - Arguments to create many StudentSchedules.
     * @example
     * // Create many StudentSchedules
     * const studentSchedule = await prisma.studentSchedule.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many StudentSchedules and only return the `id`
     * const studentScheduleWithIdOnly = await prisma.studentSchedule.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends StudentScheduleCreateManyAndReturnArgs>(args?: SelectSubset<T, StudentScheduleCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentSchedulePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a StudentSchedule.
     * @param {StudentScheduleDeleteArgs} args - Arguments to delete one StudentSchedule.
     * @example
     * // Delete one StudentSchedule
     * const StudentSchedule = await prisma.studentSchedule.delete({
     *   where: {
     *     // ... filter to delete one StudentSchedule
     *   }
     * })
     * 
     */
    delete<T extends StudentScheduleDeleteArgs>(args: SelectSubset<T, StudentScheduleDeleteArgs<ExtArgs>>): Prisma__StudentScheduleClient<$Result.GetResult<Prisma.$StudentSchedulePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one StudentSchedule.
     * @param {StudentScheduleUpdateArgs} args - Arguments to update one StudentSchedule.
     * @example
     * // Update one StudentSchedule
     * const studentSchedule = await prisma.studentSchedule.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends StudentScheduleUpdateArgs>(args: SelectSubset<T, StudentScheduleUpdateArgs<ExtArgs>>): Prisma__StudentScheduleClient<$Result.GetResult<Prisma.$StudentSchedulePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more StudentSchedules.
     * @param {StudentScheduleDeleteManyArgs} args - Arguments to filter StudentSchedules to delete.
     * @example
     * // Delete a few StudentSchedules
     * const { count } = await prisma.studentSchedule.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends StudentScheduleDeleteManyArgs>(args?: SelectSubset<T, StudentScheduleDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more StudentSchedules.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentScheduleUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many StudentSchedules
     * const studentSchedule = await prisma.studentSchedule.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends StudentScheduleUpdateManyArgs>(args: SelectSubset<T, StudentScheduleUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more StudentSchedules and returns the data updated in the database.
     * @param {StudentScheduleUpdateManyAndReturnArgs} args - Arguments to update many StudentSchedules.
     * @example
     * // Update many StudentSchedules
     * const studentSchedule = await prisma.studentSchedule.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more StudentSchedules and only return the `id`
     * const studentScheduleWithIdOnly = await prisma.studentSchedule.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends StudentScheduleUpdateManyAndReturnArgs>(args: SelectSubset<T, StudentScheduleUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StudentSchedulePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one StudentSchedule.
     * @param {StudentScheduleUpsertArgs} args - Arguments to update or create a StudentSchedule.
     * @example
     * // Update or create a StudentSchedule
     * const studentSchedule = await prisma.studentSchedule.upsert({
     *   create: {
     *     // ... data to create a StudentSchedule
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the StudentSchedule we want to update
     *   }
     * })
     */
    upsert<T extends StudentScheduleUpsertArgs>(args: SelectSubset<T, StudentScheduleUpsertArgs<ExtArgs>>): Prisma__StudentScheduleClient<$Result.GetResult<Prisma.$StudentSchedulePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of StudentSchedules.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentScheduleCountArgs} args - Arguments to filter StudentSchedules to count.
     * @example
     * // Count the number of StudentSchedules
     * const count = await prisma.studentSchedule.count({
     *   where: {
     *     // ... the filter for the StudentSchedules we want to count
     *   }
     * })
    **/
    count<T extends StudentScheduleCountArgs>(
      args?: Subset<T, StudentScheduleCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], StudentScheduleCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a StudentSchedule.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentScheduleAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends StudentScheduleAggregateArgs>(args: Subset<T, StudentScheduleAggregateArgs>): Prisma.PrismaPromise<GetStudentScheduleAggregateType<T>>

    /**
     * Group by StudentSchedule.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StudentScheduleGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends StudentScheduleGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: StudentScheduleGroupByArgs['orderBy'] }
        : { orderBy?: StudentScheduleGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, StudentScheduleGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetStudentScheduleGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the StudentSchedule model
   */
  readonly fields: StudentScheduleFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for StudentSchedule.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__StudentScheduleClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    student<T extends StudentDefaultArgs<ExtArgs> = {}>(args?: Subset<T, StudentDefaultArgs<ExtArgs>>): Prisma__StudentClient<$Result.GetResult<Prisma.$StudentPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the StudentSchedule model
   */
  interface StudentScheduleFieldRefs {
    readonly id: FieldRef<"StudentSchedule", 'String'>
    readonly studentId: FieldRef<"StudentSchedule", 'String'>
    readonly groupId: FieldRef<"StudentSchedule", 'String'>
    readonly courseTitle: FieldRef<"StudentSchedule", 'String'>
    readonly teacherName: FieldRef<"StudentSchedule", 'String'>
    readonly cabinetNumber: FieldRef<"StudentSchedule", 'String'>
    readonly schedule: FieldRef<"StudentSchedule", 'Json'>
    readonly startDate: FieldRef<"StudentSchedule", 'DateTime'>
    readonly endDate: FieldRef<"StudentSchedule", 'DateTime'>
    readonly isActive: FieldRef<"StudentSchedule", 'Boolean'>
    readonly lastUpdated: FieldRef<"StudentSchedule", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * StudentSchedule findUnique
   */
  export type StudentScheduleFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleInclude<ExtArgs> | null
    /**
     * Filter, which StudentSchedule to fetch.
     */
    where: StudentScheduleWhereUniqueInput
  }

  /**
   * StudentSchedule findUniqueOrThrow
   */
  export type StudentScheduleFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleInclude<ExtArgs> | null
    /**
     * Filter, which StudentSchedule to fetch.
     */
    where: StudentScheduleWhereUniqueInput
  }

  /**
   * StudentSchedule findFirst
   */
  export type StudentScheduleFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleInclude<ExtArgs> | null
    /**
     * Filter, which StudentSchedule to fetch.
     */
    where?: StudentScheduleWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentSchedules to fetch.
     */
    orderBy?: StudentScheduleOrderByWithRelationInput | StudentScheduleOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for StudentSchedules.
     */
    cursor?: StudentScheduleWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentSchedules from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentSchedules.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of StudentSchedules.
     */
    distinct?: StudentScheduleScalarFieldEnum | StudentScheduleScalarFieldEnum[]
  }

  /**
   * StudentSchedule findFirstOrThrow
   */
  export type StudentScheduleFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleInclude<ExtArgs> | null
    /**
     * Filter, which StudentSchedule to fetch.
     */
    where?: StudentScheduleWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentSchedules to fetch.
     */
    orderBy?: StudentScheduleOrderByWithRelationInput | StudentScheduleOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for StudentSchedules.
     */
    cursor?: StudentScheduleWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentSchedules from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentSchedules.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of StudentSchedules.
     */
    distinct?: StudentScheduleScalarFieldEnum | StudentScheduleScalarFieldEnum[]
  }

  /**
   * StudentSchedule findMany
   */
  export type StudentScheduleFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleInclude<ExtArgs> | null
    /**
     * Filter, which StudentSchedules to fetch.
     */
    where?: StudentScheduleWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of StudentSchedules to fetch.
     */
    orderBy?: StudentScheduleOrderByWithRelationInput | StudentScheduleOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing StudentSchedules.
     */
    cursor?: StudentScheduleWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` StudentSchedules from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` StudentSchedules.
     */
    skip?: number
    distinct?: StudentScheduleScalarFieldEnum | StudentScheduleScalarFieldEnum[]
  }

  /**
   * StudentSchedule create
   */
  export type StudentScheduleCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleInclude<ExtArgs> | null
    /**
     * The data needed to create a StudentSchedule.
     */
    data: XOR<StudentScheduleCreateInput, StudentScheduleUncheckedCreateInput>
  }

  /**
   * StudentSchedule createMany
   */
  export type StudentScheduleCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many StudentSchedules.
     */
    data: StudentScheduleCreateManyInput | StudentScheduleCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * StudentSchedule createManyAndReturn
   */
  export type StudentScheduleCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * The data used to create many StudentSchedules.
     */
    data: StudentScheduleCreateManyInput | StudentScheduleCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * StudentSchedule update
   */
  export type StudentScheduleUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleInclude<ExtArgs> | null
    /**
     * The data needed to update a StudentSchedule.
     */
    data: XOR<StudentScheduleUpdateInput, StudentScheduleUncheckedUpdateInput>
    /**
     * Choose, which StudentSchedule to update.
     */
    where: StudentScheduleWhereUniqueInput
  }

  /**
   * StudentSchedule updateMany
   */
  export type StudentScheduleUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update StudentSchedules.
     */
    data: XOR<StudentScheduleUpdateManyMutationInput, StudentScheduleUncheckedUpdateManyInput>
    /**
     * Filter which StudentSchedules to update
     */
    where?: StudentScheduleWhereInput
    /**
     * Limit how many StudentSchedules to update.
     */
    limit?: number
  }

  /**
   * StudentSchedule updateManyAndReturn
   */
  export type StudentScheduleUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * The data used to update StudentSchedules.
     */
    data: XOR<StudentScheduleUpdateManyMutationInput, StudentScheduleUncheckedUpdateManyInput>
    /**
     * Filter which StudentSchedules to update
     */
    where?: StudentScheduleWhereInput
    /**
     * Limit how many StudentSchedules to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * StudentSchedule upsert
   */
  export type StudentScheduleUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleInclude<ExtArgs> | null
    /**
     * The filter to search for the StudentSchedule to update in case it exists.
     */
    where: StudentScheduleWhereUniqueInput
    /**
     * In case the StudentSchedule found by the `where` argument doesn't exist, create a new StudentSchedule with this data.
     */
    create: XOR<StudentScheduleCreateInput, StudentScheduleUncheckedCreateInput>
    /**
     * In case the StudentSchedule was found with the provided `where` argument, update it with this data.
     */
    update: XOR<StudentScheduleUpdateInput, StudentScheduleUncheckedUpdateInput>
  }

  /**
   * StudentSchedule delete
   */
  export type StudentScheduleDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleInclude<ExtArgs> | null
    /**
     * Filter which StudentSchedule to delete.
     */
    where: StudentScheduleWhereUniqueInput
  }

  /**
   * StudentSchedule deleteMany
   */
  export type StudentScheduleDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which StudentSchedules to delete
     */
    where?: StudentScheduleWhereInput
    /**
     * Limit how many StudentSchedules to delete.
     */
    limit?: number
  }

  /**
   * StudentSchedule without action
   */
  export type StudentScheduleDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StudentSchedule
     */
    select?: StudentScheduleSelect<ExtArgs> | null
    /**
     * Omit specific fields from the StudentSchedule
     */
    omit?: StudentScheduleOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StudentScheduleInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const StudentUserScalarFieldEnum: {
    id: 'id',
    email: 'email',
    passwordHash: 'passwordHash',
    firstName: 'firstName',
    lastName: 'lastName',
    phone: 'phone',
    isActive: 'isActive',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type StudentUserScalarFieldEnum = (typeof StudentUserScalarFieldEnum)[keyof typeof StudentUserScalarFieldEnum]


  export const StudentScalarFieldEnum: {
    id: 'id',
    studentUserId: 'studentUserId',
    studentId: 'studentId',
    dateOfBirth: 'dateOfBirth',
    emergencyContact: 'emergencyContact',
    enrollmentDate: 'enrollmentDate',
    currentLevel: 'currentLevel',
    status: 'status',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type StudentScalarFieldEnum = (typeof StudentScalarFieldEnum)[keyof typeof StudentScalarFieldEnum]


  export const StudentProgressScalarFieldEnum: {
    id: 'id',
    studentId: 'studentId',
    assignmentId: 'assignmentId',
    progressPercentage: 'progressPercentage',
    grade: 'grade',
    feedback: 'feedback',
    submittedAt: 'submittedAt',
    gradedAt: 'gradedAt',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type StudentProgressScalarFieldEnum = (typeof StudentProgressScalarFieldEnum)[keyof typeof StudentProgressScalarFieldEnum]


  export const AssignmentSubmissionScalarFieldEnum: {
    id: 'id',
    studentId: 'studentId',
    assignmentId: 'assignmentId',
    submissionContent: 'submissionContent',
    fileUrl: 'fileUrl',
    submittedAt: 'submittedAt',
    status: 'status',
    teacherFeedback: 'teacherFeedback',
    grade: 'grade',
    gradedAt: 'gradedAt'
  };

  export type AssignmentSubmissionScalarFieldEnum = (typeof AssignmentSubmissionScalarFieldEnum)[keyof typeof AssignmentSubmissionScalarFieldEnum]


  export const StudentResourceAccessScalarFieldEnum: {
    id: 'id',
    studentId: 'studentId',
    resourceId: 'resourceId',
    accessedAt: 'accessedAt',
    accessDuration: 'accessDuration'
  };

  export type StudentResourceAccessScalarFieldEnum = (typeof StudentResourceAccessScalarFieldEnum)[keyof typeof StudentResourceAccessScalarFieldEnum]


  export const StudentScheduleScalarFieldEnum: {
    id: 'id',
    studentId: 'studentId',
    groupId: 'groupId',
    courseTitle: 'courseTitle',
    teacherName: 'teacherName',
    cabinetNumber: 'cabinetNumber',
    schedule: 'schedule',
    startDate: 'startDate',
    endDate: 'endDate',
    isActive: 'isActive',
    lastUpdated: 'lastUpdated'
  };

  export type StudentScheduleScalarFieldEnum = (typeof StudentScheduleScalarFieldEnum)[keyof typeof StudentScheduleScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const NullableJsonNullValueInput: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull
  };

  export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


  export const JsonNullValueInput: {
    JsonNull: typeof JsonNull
  };

  export type JsonNullValueInput = (typeof JsonNullValueInput)[keyof typeof JsonNullValueInput]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  export const JsonNullValueFilter: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull,
    AnyNull: typeof AnyNull
  };

  export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'Json'
   */
  export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


  /**
   * Reference to a field of type 'QueryMode'
   */
  export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>
    


  /**
   * Reference to a field of type 'StudentStatus'
   */
  export type EnumStudentStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'StudentStatus'>
    


  /**
   * Reference to a field of type 'StudentStatus[]'
   */
  export type ListEnumStudentStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'StudentStatus[]'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    


  /**
   * Reference to a field of type 'SubmissionStatus'
   */
  export type EnumSubmissionStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'SubmissionStatus'>
    


  /**
   * Reference to a field of type 'SubmissionStatus[]'
   */
  export type ListEnumSubmissionStatusFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'SubmissionStatus[]'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'Float[]'
   */
  export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>
    
  /**
   * Deep Input Types
   */


  export type StudentUserWhereInput = {
    AND?: StudentUserWhereInput | StudentUserWhereInput[]
    OR?: StudentUserWhereInput[]
    NOT?: StudentUserWhereInput | StudentUserWhereInput[]
    id?: StringFilter<"StudentUser"> | string
    email?: StringFilter<"StudentUser"> | string
    passwordHash?: StringFilter<"StudentUser"> | string
    firstName?: StringFilter<"StudentUser"> | string
    lastName?: StringFilter<"StudentUser"> | string
    phone?: StringNullableFilter<"StudentUser"> | string | null
    isActive?: BoolFilter<"StudentUser"> | boolean
    createdAt?: DateTimeFilter<"StudentUser"> | Date | string
    updatedAt?: DateTimeFilter<"StudentUser"> | Date | string
    student?: XOR<StudentNullableScalarRelationFilter, StudentWhereInput> | null
  }

  export type StudentUserOrderByWithRelationInput = {
    id?: SortOrder
    email?: SortOrder
    passwordHash?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    phone?: SortOrderInput | SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    student?: StudentOrderByWithRelationInput
  }

  export type StudentUserWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    email?: string
    AND?: StudentUserWhereInput | StudentUserWhereInput[]
    OR?: StudentUserWhereInput[]
    NOT?: StudentUserWhereInput | StudentUserWhereInput[]
    passwordHash?: StringFilter<"StudentUser"> | string
    firstName?: StringFilter<"StudentUser"> | string
    lastName?: StringFilter<"StudentUser"> | string
    phone?: StringNullableFilter<"StudentUser"> | string | null
    isActive?: BoolFilter<"StudentUser"> | boolean
    createdAt?: DateTimeFilter<"StudentUser"> | Date | string
    updatedAt?: DateTimeFilter<"StudentUser"> | Date | string
    student?: XOR<StudentNullableScalarRelationFilter, StudentWhereInput> | null
  }, "id" | "email">

  export type StudentUserOrderByWithAggregationInput = {
    id?: SortOrder
    email?: SortOrder
    passwordHash?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    phone?: SortOrderInput | SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: StudentUserCountOrderByAggregateInput
    _max?: StudentUserMaxOrderByAggregateInput
    _min?: StudentUserMinOrderByAggregateInput
  }

  export type StudentUserScalarWhereWithAggregatesInput = {
    AND?: StudentUserScalarWhereWithAggregatesInput | StudentUserScalarWhereWithAggregatesInput[]
    OR?: StudentUserScalarWhereWithAggregatesInput[]
    NOT?: StudentUserScalarWhereWithAggregatesInput | StudentUserScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"StudentUser"> | string
    email?: StringWithAggregatesFilter<"StudentUser"> | string
    passwordHash?: StringWithAggregatesFilter<"StudentUser"> | string
    firstName?: StringWithAggregatesFilter<"StudentUser"> | string
    lastName?: StringWithAggregatesFilter<"StudentUser"> | string
    phone?: StringNullableWithAggregatesFilter<"StudentUser"> | string | null
    isActive?: BoolWithAggregatesFilter<"StudentUser"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"StudentUser"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"StudentUser"> | Date | string
  }

  export type StudentWhereInput = {
    AND?: StudentWhereInput | StudentWhereInput[]
    OR?: StudentWhereInput[]
    NOT?: StudentWhereInput | StudentWhereInput[]
    id?: StringFilter<"Student"> | string
    studentUserId?: StringFilter<"Student"> | string
    studentId?: StringFilter<"Student"> | string
    dateOfBirth?: DateTimeNullableFilter<"Student"> | Date | string | null
    emergencyContact?: JsonNullableFilter<"Student">
    enrollmentDate?: DateTimeFilter<"Student"> | Date | string
    currentLevel?: StringNullableFilter<"Student"> | string | null
    status?: EnumStudentStatusFilter<"Student"> | $Enums.StudentStatus
    createdAt?: DateTimeFilter<"Student"> | Date | string
    updatedAt?: DateTimeFilter<"Student"> | Date | string
    studentUser?: XOR<StudentUserScalarRelationFilter, StudentUserWhereInput>
    progress?: StudentProgressListRelationFilter
    submissions?: AssignmentSubmissionListRelationFilter
    resourceAccess?: StudentResourceAccessListRelationFilter
    schedules?: StudentScheduleListRelationFilter
  }

  export type StudentOrderByWithRelationInput = {
    id?: SortOrder
    studentUserId?: SortOrder
    studentId?: SortOrder
    dateOfBirth?: SortOrderInput | SortOrder
    emergencyContact?: SortOrderInput | SortOrder
    enrollmentDate?: SortOrder
    currentLevel?: SortOrderInput | SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    studentUser?: StudentUserOrderByWithRelationInput
    progress?: StudentProgressOrderByRelationAggregateInput
    submissions?: AssignmentSubmissionOrderByRelationAggregateInput
    resourceAccess?: StudentResourceAccessOrderByRelationAggregateInput
    schedules?: StudentScheduleOrderByRelationAggregateInput
  }

  export type StudentWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    studentUserId?: string
    studentId?: string
    AND?: StudentWhereInput | StudentWhereInput[]
    OR?: StudentWhereInput[]
    NOT?: StudentWhereInput | StudentWhereInput[]
    dateOfBirth?: DateTimeNullableFilter<"Student"> | Date | string | null
    emergencyContact?: JsonNullableFilter<"Student">
    enrollmentDate?: DateTimeFilter<"Student"> | Date | string
    currentLevel?: StringNullableFilter<"Student"> | string | null
    status?: EnumStudentStatusFilter<"Student"> | $Enums.StudentStatus
    createdAt?: DateTimeFilter<"Student"> | Date | string
    updatedAt?: DateTimeFilter<"Student"> | Date | string
    studentUser?: XOR<StudentUserScalarRelationFilter, StudentUserWhereInput>
    progress?: StudentProgressListRelationFilter
    submissions?: AssignmentSubmissionListRelationFilter
    resourceAccess?: StudentResourceAccessListRelationFilter
    schedules?: StudentScheduleListRelationFilter
  }, "id" | "studentUserId" | "studentId">

  export type StudentOrderByWithAggregationInput = {
    id?: SortOrder
    studentUserId?: SortOrder
    studentId?: SortOrder
    dateOfBirth?: SortOrderInput | SortOrder
    emergencyContact?: SortOrderInput | SortOrder
    enrollmentDate?: SortOrder
    currentLevel?: SortOrderInput | SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: StudentCountOrderByAggregateInput
    _max?: StudentMaxOrderByAggregateInput
    _min?: StudentMinOrderByAggregateInput
  }

  export type StudentScalarWhereWithAggregatesInput = {
    AND?: StudentScalarWhereWithAggregatesInput | StudentScalarWhereWithAggregatesInput[]
    OR?: StudentScalarWhereWithAggregatesInput[]
    NOT?: StudentScalarWhereWithAggregatesInput | StudentScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Student"> | string
    studentUserId?: StringWithAggregatesFilter<"Student"> | string
    studentId?: StringWithAggregatesFilter<"Student"> | string
    dateOfBirth?: DateTimeNullableWithAggregatesFilter<"Student"> | Date | string | null
    emergencyContact?: JsonNullableWithAggregatesFilter<"Student">
    enrollmentDate?: DateTimeWithAggregatesFilter<"Student"> | Date | string
    currentLevel?: StringNullableWithAggregatesFilter<"Student"> | string | null
    status?: EnumStudentStatusWithAggregatesFilter<"Student"> | $Enums.StudentStatus
    createdAt?: DateTimeWithAggregatesFilter<"Student"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Student"> | Date | string
  }

  export type StudentProgressWhereInput = {
    AND?: StudentProgressWhereInput | StudentProgressWhereInput[]
    OR?: StudentProgressWhereInput[]
    NOT?: StudentProgressWhereInput | StudentProgressWhereInput[]
    id?: StringFilter<"StudentProgress"> | string
    studentId?: StringFilter<"StudentProgress"> | string
    assignmentId?: StringFilter<"StudentProgress"> | string
    progressPercentage?: IntFilter<"StudentProgress"> | number
    grade?: StringNullableFilter<"StudentProgress"> | string | null
    feedback?: StringNullableFilter<"StudentProgress"> | string | null
    submittedAt?: DateTimeNullableFilter<"StudentProgress"> | Date | string | null
    gradedAt?: DateTimeNullableFilter<"StudentProgress"> | Date | string | null
    createdAt?: DateTimeFilter<"StudentProgress"> | Date | string
    updatedAt?: DateTimeFilter<"StudentProgress"> | Date | string
    student?: XOR<StudentScalarRelationFilter, StudentWhereInput>
  }

  export type StudentProgressOrderByWithRelationInput = {
    id?: SortOrder
    studentId?: SortOrder
    assignmentId?: SortOrder
    progressPercentage?: SortOrder
    grade?: SortOrderInput | SortOrder
    feedback?: SortOrderInput | SortOrder
    submittedAt?: SortOrderInput | SortOrder
    gradedAt?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    student?: StudentOrderByWithRelationInput
  }

  export type StudentProgressWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    studentId_assignmentId?: StudentProgressStudentIdAssignmentIdCompoundUniqueInput
    AND?: StudentProgressWhereInput | StudentProgressWhereInput[]
    OR?: StudentProgressWhereInput[]
    NOT?: StudentProgressWhereInput | StudentProgressWhereInput[]
    studentId?: StringFilter<"StudentProgress"> | string
    assignmentId?: StringFilter<"StudentProgress"> | string
    progressPercentage?: IntFilter<"StudentProgress"> | number
    grade?: StringNullableFilter<"StudentProgress"> | string | null
    feedback?: StringNullableFilter<"StudentProgress"> | string | null
    submittedAt?: DateTimeNullableFilter<"StudentProgress"> | Date | string | null
    gradedAt?: DateTimeNullableFilter<"StudentProgress"> | Date | string | null
    createdAt?: DateTimeFilter<"StudentProgress"> | Date | string
    updatedAt?: DateTimeFilter<"StudentProgress"> | Date | string
    student?: XOR<StudentScalarRelationFilter, StudentWhereInput>
  }, "id" | "studentId_assignmentId">

  export type StudentProgressOrderByWithAggregationInput = {
    id?: SortOrder
    studentId?: SortOrder
    assignmentId?: SortOrder
    progressPercentage?: SortOrder
    grade?: SortOrderInput | SortOrder
    feedback?: SortOrderInput | SortOrder
    submittedAt?: SortOrderInput | SortOrder
    gradedAt?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: StudentProgressCountOrderByAggregateInput
    _avg?: StudentProgressAvgOrderByAggregateInput
    _max?: StudentProgressMaxOrderByAggregateInput
    _min?: StudentProgressMinOrderByAggregateInput
    _sum?: StudentProgressSumOrderByAggregateInput
  }

  export type StudentProgressScalarWhereWithAggregatesInput = {
    AND?: StudentProgressScalarWhereWithAggregatesInput | StudentProgressScalarWhereWithAggregatesInput[]
    OR?: StudentProgressScalarWhereWithAggregatesInput[]
    NOT?: StudentProgressScalarWhereWithAggregatesInput | StudentProgressScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"StudentProgress"> | string
    studentId?: StringWithAggregatesFilter<"StudentProgress"> | string
    assignmentId?: StringWithAggregatesFilter<"StudentProgress"> | string
    progressPercentage?: IntWithAggregatesFilter<"StudentProgress"> | number
    grade?: StringNullableWithAggregatesFilter<"StudentProgress"> | string | null
    feedback?: StringNullableWithAggregatesFilter<"StudentProgress"> | string | null
    submittedAt?: DateTimeNullableWithAggregatesFilter<"StudentProgress"> | Date | string | null
    gradedAt?: DateTimeNullableWithAggregatesFilter<"StudentProgress"> | Date | string | null
    createdAt?: DateTimeWithAggregatesFilter<"StudentProgress"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"StudentProgress"> | Date | string
  }

  export type AssignmentSubmissionWhereInput = {
    AND?: AssignmentSubmissionWhereInput | AssignmentSubmissionWhereInput[]
    OR?: AssignmentSubmissionWhereInput[]
    NOT?: AssignmentSubmissionWhereInput | AssignmentSubmissionWhereInput[]
    id?: StringFilter<"AssignmentSubmission"> | string
    studentId?: StringFilter<"AssignmentSubmission"> | string
    assignmentId?: StringFilter<"AssignmentSubmission"> | string
    submissionContent?: StringNullableFilter<"AssignmentSubmission"> | string | null
    fileUrl?: StringNullableFilter<"AssignmentSubmission"> | string | null
    submittedAt?: DateTimeFilter<"AssignmentSubmission"> | Date | string
    status?: EnumSubmissionStatusFilter<"AssignmentSubmission"> | $Enums.SubmissionStatus
    teacherFeedback?: StringNullableFilter<"AssignmentSubmission"> | string | null
    grade?: StringNullableFilter<"AssignmentSubmission"> | string | null
    gradedAt?: DateTimeNullableFilter<"AssignmentSubmission"> | Date | string | null
    student?: XOR<StudentScalarRelationFilter, StudentWhereInput>
  }

  export type AssignmentSubmissionOrderByWithRelationInput = {
    id?: SortOrder
    studentId?: SortOrder
    assignmentId?: SortOrder
    submissionContent?: SortOrderInput | SortOrder
    fileUrl?: SortOrderInput | SortOrder
    submittedAt?: SortOrder
    status?: SortOrder
    teacherFeedback?: SortOrderInput | SortOrder
    grade?: SortOrderInput | SortOrder
    gradedAt?: SortOrderInput | SortOrder
    student?: StudentOrderByWithRelationInput
  }

  export type AssignmentSubmissionWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: AssignmentSubmissionWhereInput | AssignmentSubmissionWhereInput[]
    OR?: AssignmentSubmissionWhereInput[]
    NOT?: AssignmentSubmissionWhereInput | AssignmentSubmissionWhereInput[]
    studentId?: StringFilter<"AssignmentSubmission"> | string
    assignmentId?: StringFilter<"AssignmentSubmission"> | string
    submissionContent?: StringNullableFilter<"AssignmentSubmission"> | string | null
    fileUrl?: StringNullableFilter<"AssignmentSubmission"> | string | null
    submittedAt?: DateTimeFilter<"AssignmentSubmission"> | Date | string
    status?: EnumSubmissionStatusFilter<"AssignmentSubmission"> | $Enums.SubmissionStatus
    teacherFeedback?: StringNullableFilter<"AssignmentSubmission"> | string | null
    grade?: StringNullableFilter<"AssignmentSubmission"> | string | null
    gradedAt?: DateTimeNullableFilter<"AssignmentSubmission"> | Date | string | null
    student?: XOR<StudentScalarRelationFilter, StudentWhereInput>
  }, "id">

  export type AssignmentSubmissionOrderByWithAggregationInput = {
    id?: SortOrder
    studentId?: SortOrder
    assignmentId?: SortOrder
    submissionContent?: SortOrderInput | SortOrder
    fileUrl?: SortOrderInput | SortOrder
    submittedAt?: SortOrder
    status?: SortOrder
    teacherFeedback?: SortOrderInput | SortOrder
    grade?: SortOrderInput | SortOrder
    gradedAt?: SortOrderInput | SortOrder
    _count?: AssignmentSubmissionCountOrderByAggregateInput
    _max?: AssignmentSubmissionMaxOrderByAggregateInput
    _min?: AssignmentSubmissionMinOrderByAggregateInput
  }

  export type AssignmentSubmissionScalarWhereWithAggregatesInput = {
    AND?: AssignmentSubmissionScalarWhereWithAggregatesInput | AssignmentSubmissionScalarWhereWithAggregatesInput[]
    OR?: AssignmentSubmissionScalarWhereWithAggregatesInput[]
    NOT?: AssignmentSubmissionScalarWhereWithAggregatesInput | AssignmentSubmissionScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"AssignmentSubmission"> | string
    studentId?: StringWithAggregatesFilter<"AssignmentSubmission"> | string
    assignmentId?: StringWithAggregatesFilter<"AssignmentSubmission"> | string
    submissionContent?: StringNullableWithAggregatesFilter<"AssignmentSubmission"> | string | null
    fileUrl?: StringNullableWithAggregatesFilter<"AssignmentSubmission"> | string | null
    submittedAt?: DateTimeWithAggregatesFilter<"AssignmentSubmission"> | Date | string
    status?: EnumSubmissionStatusWithAggregatesFilter<"AssignmentSubmission"> | $Enums.SubmissionStatus
    teacherFeedback?: StringNullableWithAggregatesFilter<"AssignmentSubmission"> | string | null
    grade?: StringNullableWithAggregatesFilter<"AssignmentSubmission"> | string | null
    gradedAt?: DateTimeNullableWithAggregatesFilter<"AssignmentSubmission"> | Date | string | null
  }

  export type StudentResourceAccessWhereInput = {
    AND?: StudentResourceAccessWhereInput | StudentResourceAccessWhereInput[]
    OR?: StudentResourceAccessWhereInput[]
    NOT?: StudentResourceAccessWhereInput | StudentResourceAccessWhereInput[]
    id?: StringFilter<"StudentResourceAccess"> | string
    studentId?: StringFilter<"StudentResourceAccess"> | string
    resourceId?: StringFilter<"StudentResourceAccess"> | string
    accessedAt?: DateTimeFilter<"StudentResourceAccess"> | Date | string
    accessDuration?: IntNullableFilter<"StudentResourceAccess"> | number | null
    student?: XOR<StudentScalarRelationFilter, StudentWhereInput>
  }

  export type StudentResourceAccessOrderByWithRelationInput = {
    id?: SortOrder
    studentId?: SortOrder
    resourceId?: SortOrder
    accessedAt?: SortOrder
    accessDuration?: SortOrderInput | SortOrder
    student?: StudentOrderByWithRelationInput
  }

  export type StudentResourceAccessWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: StudentResourceAccessWhereInput | StudentResourceAccessWhereInput[]
    OR?: StudentResourceAccessWhereInput[]
    NOT?: StudentResourceAccessWhereInput | StudentResourceAccessWhereInput[]
    studentId?: StringFilter<"StudentResourceAccess"> | string
    resourceId?: StringFilter<"StudentResourceAccess"> | string
    accessedAt?: DateTimeFilter<"StudentResourceAccess"> | Date | string
    accessDuration?: IntNullableFilter<"StudentResourceAccess"> | number | null
    student?: XOR<StudentScalarRelationFilter, StudentWhereInput>
  }, "id">

  export type StudentResourceAccessOrderByWithAggregationInput = {
    id?: SortOrder
    studentId?: SortOrder
    resourceId?: SortOrder
    accessedAt?: SortOrder
    accessDuration?: SortOrderInput | SortOrder
    _count?: StudentResourceAccessCountOrderByAggregateInput
    _avg?: StudentResourceAccessAvgOrderByAggregateInput
    _max?: StudentResourceAccessMaxOrderByAggregateInput
    _min?: StudentResourceAccessMinOrderByAggregateInput
    _sum?: StudentResourceAccessSumOrderByAggregateInput
  }

  export type StudentResourceAccessScalarWhereWithAggregatesInput = {
    AND?: StudentResourceAccessScalarWhereWithAggregatesInput | StudentResourceAccessScalarWhereWithAggregatesInput[]
    OR?: StudentResourceAccessScalarWhereWithAggregatesInput[]
    NOT?: StudentResourceAccessScalarWhereWithAggregatesInput | StudentResourceAccessScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"StudentResourceAccess"> | string
    studentId?: StringWithAggregatesFilter<"StudentResourceAccess"> | string
    resourceId?: StringWithAggregatesFilter<"StudentResourceAccess"> | string
    accessedAt?: DateTimeWithAggregatesFilter<"StudentResourceAccess"> | Date | string
    accessDuration?: IntNullableWithAggregatesFilter<"StudentResourceAccess"> | number | null
  }

  export type StudentScheduleWhereInput = {
    AND?: StudentScheduleWhereInput | StudentScheduleWhereInput[]
    OR?: StudentScheduleWhereInput[]
    NOT?: StudentScheduleWhereInput | StudentScheduleWhereInput[]
    id?: StringFilter<"StudentSchedule"> | string
    studentId?: StringFilter<"StudentSchedule"> | string
    groupId?: StringFilter<"StudentSchedule"> | string
    courseTitle?: StringFilter<"StudentSchedule"> | string
    teacherName?: StringFilter<"StudentSchedule"> | string
    cabinetNumber?: StringFilter<"StudentSchedule"> | string
    schedule?: JsonFilter<"StudentSchedule">
    startDate?: DateTimeFilter<"StudentSchedule"> | Date | string
    endDate?: DateTimeNullableFilter<"StudentSchedule"> | Date | string | null
    isActive?: BoolFilter<"StudentSchedule"> | boolean
    lastUpdated?: DateTimeFilter<"StudentSchedule"> | Date | string
    student?: XOR<StudentScalarRelationFilter, StudentWhereInput>
  }

  export type StudentScheduleOrderByWithRelationInput = {
    id?: SortOrder
    studentId?: SortOrder
    groupId?: SortOrder
    courseTitle?: SortOrder
    teacherName?: SortOrder
    cabinetNumber?: SortOrder
    schedule?: SortOrder
    startDate?: SortOrder
    endDate?: SortOrderInput | SortOrder
    isActive?: SortOrder
    lastUpdated?: SortOrder
    student?: StudentOrderByWithRelationInput
  }

  export type StudentScheduleWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: StudentScheduleWhereInput | StudentScheduleWhereInput[]
    OR?: StudentScheduleWhereInput[]
    NOT?: StudentScheduleWhereInput | StudentScheduleWhereInput[]
    studentId?: StringFilter<"StudentSchedule"> | string
    groupId?: StringFilter<"StudentSchedule"> | string
    courseTitle?: StringFilter<"StudentSchedule"> | string
    teacherName?: StringFilter<"StudentSchedule"> | string
    cabinetNumber?: StringFilter<"StudentSchedule"> | string
    schedule?: JsonFilter<"StudentSchedule">
    startDate?: DateTimeFilter<"StudentSchedule"> | Date | string
    endDate?: DateTimeNullableFilter<"StudentSchedule"> | Date | string | null
    isActive?: BoolFilter<"StudentSchedule"> | boolean
    lastUpdated?: DateTimeFilter<"StudentSchedule"> | Date | string
    student?: XOR<StudentScalarRelationFilter, StudentWhereInput>
  }, "id">

  export type StudentScheduleOrderByWithAggregationInput = {
    id?: SortOrder
    studentId?: SortOrder
    groupId?: SortOrder
    courseTitle?: SortOrder
    teacherName?: SortOrder
    cabinetNumber?: SortOrder
    schedule?: SortOrder
    startDate?: SortOrder
    endDate?: SortOrderInput | SortOrder
    isActive?: SortOrder
    lastUpdated?: SortOrder
    _count?: StudentScheduleCountOrderByAggregateInput
    _max?: StudentScheduleMaxOrderByAggregateInput
    _min?: StudentScheduleMinOrderByAggregateInput
  }

  export type StudentScheduleScalarWhereWithAggregatesInput = {
    AND?: StudentScheduleScalarWhereWithAggregatesInput | StudentScheduleScalarWhereWithAggregatesInput[]
    OR?: StudentScheduleScalarWhereWithAggregatesInput[]
    NOT?: StudentScheduleScalarWhereWithAggregatesInput | StudentScheduleScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"StudentSchedule"> | string
    studentId?: StringWithAggregatesFilter<"StudentSchedule"> | string
    groupId?: StringWithAggregatesFilter<"StudentSchedule"> | string
    courseTitle?: StringWithAggregatesFilter<"StudentSchedule"> | string
    teacherName?: StringWithAggregatesFilter<"StudentSchedule"> | string
    cabinetNumber?: StringWithAggregatesFilter<"StudentSchedule"> | string
    schedule?: JsonWithAggregatesFilter<"StudentSchedule">
    startDate?: DateTimeWithAggregatesFilter<"StudentSchedule"> | Date | string
    endDate?: DateTimeNullableWithAggregatesFilter<"StudentSchedule"> | Date | string | null
    isActive?: BoolWithAggregatesFilter<"StudentSchedule"> | boolean
    lastUpdated?: DateTimeWithAggregatesFilter<"StudentSchedule"> | Date | string
  }

  export type StudentUserCreateInput = {
    id?: string
    email: string
    passwordHash: string
    firstName: string
    lastName: string
    phone?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    student?: StudentCreateNestedOneWithoutStudentUserInput
  }

  export type StudentUserUncheckedCreateInput = {
    id?: string
    email: string
    passwordHash: string
    firstName: string
    lastName: string
    phone?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    student?: StudentUncheckedCreateNestedOneWithoutStudentUserInput
  }

  export type StudentUserUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    student?: StudentUpdateOneWithoutStudentUserNestedInput
  }

  export type StudentUserUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    student?: StudentUncheckedUpdateOneWithoutStudentUserNestedInput
  }

  export type StudentUserCreateManyInput = {
    id?: string
    email: string
    passwordHash: string
    firstName: string
    lastName: string
    phone?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type StudentUserUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentUserUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentCreateInput = {
    id?: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    studentUser: StudentUserCreateNestedOneWithoutStudentInput
    progress?: StudentProgressCreateNestedManyWithoutStudentInput
    submissions?: AssignmentSubmissionCreateNestedManyWithoutStudentInput
    resourceAccess?: StudentResourceAccessCreateNestedManyWithoutStudentInput
    schedules?: StudentScheduleCreateNestedManyWithoutStudentInput
  }

  export type StudentUncheckedCreateInput = {
    id?: string
    studentUserId: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    progress?: StudentProgressUncheckedCreateNestedManyWithoutStudentInput
    submissions?: AssignmentSubmissionUncheckedCreateNestedManyWithoutStudentInput
    resourceAccess?: StudentResourceAccessUncheckedCreateNestedManyWithoutStudentInput
    schedules?: StudentScheduleUncheckedCreateNestedManyWithoutStudentInput
  }

  export type StudentUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    studentUser?: StudentUserUpdateOneRequiredWithoutStudentNestedInput
    progress?: StudentProgressUpdateManyWithoutStudentNestedInput
    submissions?: AssignmentSubmissionUpdateManyWithoutStudentNestedInput
    resourceAccess?: StudentResourceAccessUpdateManyWithoutStudentNestedInput
    schedules?: StudentScheduleUpdateManyWithoutStudentNestedInput
  }

  export type StudentUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentUserId?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    progress?: StudentProgressUncheckedUpdateManyWithoutStudentNestedInput
    submissions?: AssignmentSubmissionUncheckedUpdateManyWithoutStudentNestedInput
    resourceAccess?: StudentResourceAccessUncheckedUpdateManyWithoutStudentNestedInput
    schedules?: StudentScheduleUncheckedUpdateManyWithoutStudentNestedInput
  }

  export type StudentCreateManyInput = {
    id?: string
    studentUserId: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type StudentUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentUserId?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentProgressCreateInput = {
    id?: string
    assignmentId: string
    progressPercentage: number
    grade?: string | null
    feedback?: string | null
    submittedAt?: Date | string | null
    gradedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    student: StudentCreateNestedOneWithoutProgressInput
  }

  export type StudentProgressUncheckedCreateInput = {
    id?: string
    studentId: string
    assignmentId: string
    progressPercentage: number
    grade?: string | null
    feedback?: string | null
    submittedAt?: Date | string | null
    gradedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type StudentProgressUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    progressPercentage?: IntFieldUpdateOperationsInput | number
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    feedback?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    student?: StudentUpdateOneRequiredWithoutProgressNestedInput
  }

  export type StudentProgressUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    progressPercentage?: IntFieldUpdateOperationsInput | number
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    feedback?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentProgressCreateManyInput = {
    id?: string
    studentId: string
    assignmentId: string
    progressPercentage: number
    grade?: string | null
    feedback?: string | null
    submittedAt?: Date | string | null
    gradedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type StudentProgressUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    progressPercentage?: IntFieldUpdateOperationsInput | number
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    feedback?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentProgressUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    progressPercentage?: IntFieldUpdateOperationsInput | number
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    feedback?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AssignmentSubmissionCreateInput = {
    id?: string
    assignmentId: string
    submissionContent?: string | null
    fileUrl?: string | null
    submittedAt?: Date | string
    status?: $Enums.SubmissionStatus
    teacherFeedback?: string | null
    grade?: string | null
    gradedAt?: Date | string | null
    student: StudentCreateNestedOneWithoutSubmissionsInput
  }

  export type AssignmentSubmissionUncheckedCreateInput = {
    id?: string
    studentId: string
    assignmentId: string
    submissionContent?: string | null
    fileUrl?: string | null
    submittedAt?: Date | string
    status?: $Enums.SubmissionStatus
    teacherFeedback?: string | null
    grade?: string | null
    gradedAt?: Date | string | null
  }

  export type AssignmentSubmissionUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    submissionContent?: NullableStringFieldUpdateOperationsInput | string | null
    fileUrl?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: EnumSubmissionStatusFieldUpdateOperationsInput | $Enums.SubmissionStatus
    teacherFeedback?: NullableStringFieldUpdateOperationsInput | string | null
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    student?: StudentUpdateOneRequiredWithoutSubmissionsNestedInput
  }

  export type AssignmentSubmissionUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    submissionContent?: NullableStringFieldUpdateOperationsInput | string | null
    fileUrl?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: EnumSubmissionStatusFieldUpdateOperationsInput | $Enums.SubmissionStatus
    teacherFeedback?: NullableStringFieldUpdateOperationsInput | string | null
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type AssignmentSubmissionCreateManyInput = {
    id?: string
    studentId: string
    assignmentId: string
    submissionContent?: string | null
    fileUrl?: string | null
    submittedAt?: Date | string
    status?: $Enums.SubmissionStatus
    teacherFeedback?: string | null
    grade?: string | null
    gradedAt?: Date | string | null
  }

  export type AssignmentSubmissionUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    submissionContent?: NullableStringFieldUpdateOperationsInput | string | null
    fileUrl?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: EnumSubmissionStatusFieldUpdateOperationsInput | $Enums.SubmissionStatus
    teacherFeedback?: NullableStringFieldUpdateOperationsInput | string | null
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type AssignmentSubmissionUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    submissionContent?: NullableStringFieldUpdateOperationsInput | string | null
    fileUrl?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: EnumSubmissionStatusFieldUpdateOperationsInput | $Enums.SubmissionStatus
    teacherFeedback?: NullableStringFieldUpdateOperationsInput | string | null
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type StudentResourceAccessCreateInput = {
    id?: string
    resourceId: string
    accessedAt?: Date | string
    accessDuration?: number | null
    student: StudentCreateNestedOneWithoutResourceAccessInput
  }

  export type StudentResourceAccessUncheckedCreateInput = {
    id?: string
    studentId: string
    resourceId: string
    accessedAt?: Date | string
    accessDuration?: number | null
  }

  export type StudentResourceAccessUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    accessedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    accessDuration?: NullableIntFieldUpdateOperationsInput | number | null
    student?: StudentUpdateOneRequiredWithoutResourceAccessNestedInput
  }

  export type StudentResourceAccessUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    accessedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    accessDuration?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type StudentResourceAccessCreateManyInput = {
    id?: string
    studentId: string
    resourceId: string
    accessedAt?: Date | string
    accessDuration?: number | null
  }

  export type StudentResourceAccessUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    accessedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    accessDuration?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type StudentResourceAccessUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    accessedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    accessDuration?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type StudentScheduleCreateInput = {
    id?: string
    groupId: string
    courseTitle: string
    teacherName: string
    cabinetNumber: string
    schedule: JsonNullValueInput | InputJsonValue
    startDate: Date | string
    endDate?: Date | string | null
    isActive?: boolean
    lastUpdated?: Date | string
    student: StudentCreateNestedOneWithoutSchedulesInput
  }

  export type StudentScheduleUncheckedCreateInput = {
    id?: string
    studentId: string
    groupId: string
    courseTitle: string
    teacherName: string
    cabinetNumber: string
    schedule: JsonNullValueInput | InputJsonValue
    startDate: Date | string
    endDate?: Date | string | null
    isActive?: boolean
    lastUpdated?: Date | string
  }

  export type StudentScheduleUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    groupId?: StringFieldUpdateOperationsInput | string
    courseTitle?: StringFieldUpdateOperationsInput | string
    teacherName?: StringFieldUpdateOperationsInput | string
    cabinetNumber?: StringFieldUpdateOperationsInput | string
    schedule?: JsonNullValueInput | InputJsonValue
    startDate?: DateTimeFieldUpdateOperationsInput | Date | string
    endDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    lastUpdated?: DateTimeFieldUpdateOperationsInput | Date | string
    student?: StudentUpdateOneRequiredWithoutSchedulesNestedInput
  }

  export type StudentScheduleUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    groupId?: StringFieldUpdateOperationsInput | string
    courseTitle?: StringFieldUpdateOperationsInput | string
    teacherName?: StringFieldUpdateOperationsInput | string
    cabinetNumber?: StringFieldUpdateOperationsInput | string
    schedule?: JsonNullValueInput | InputJsonValue
    startDate?: DateTimeFieldUpdateOperationsInput | Date | string
    endDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    lastUpdated?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentScheduleCreateManyInput = {
    id?: string
    studentId: string
    groupId: string
    courseTitle: string
    teacherName: string
    cabinetNumber: string
    schedule: JsonNullValueInput | InputJsonValue
    startDate: Date | string
    endDate?: Date | string | null
    isActive?: boolean
    lastUpdated?: Date | string
  }

  export type StudentScheduleUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    groupId?: StringFieldUpdateOperationsInput | string
    courseTitle?: StringFieldUpdateOperationsInput | string
    teacherName?: StringFieldUpdateOperationsInput | string
    cabinetNumber?: StringFieldUpdateOperationsInput | string
    schedule?: JsonNullValueInput | InputJsonValue
    startDate?: DateTimeFieldUpdateOperationsInput | Date | string
    endDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    lastUpdated?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentScheduleUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    groupId?: StringFieldUpdateOperationsInput | string
    courseTitle?: StringFieldUpdateOperationsInput | string
    teacherName?: StringFieldUpdateOperationsInput | string
    cabinetNumber?: StringFieldUpdateOperationsInput | string
    schedule?: JsonNullValueInput | InputJsonValue
    startDate?: DateTimeFieldUpdateOperationsInput | Date | string
    endDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    lastUpdated?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type StudentNullableScalarRelationFilter = {
    is?: StudentWhereInput | null
    isNot?: StudentWhereInput | null
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type StudentUserCountOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    passwordHash?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    phone?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StudentUserMaxOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    passwordHash?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    phone?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StudentUserMinOrderByAggregateInput = {
    id?: SortOrder
    email?: SortOrder
    passwordHash?: SortOrder
    firstName?: SortOrder
    lastName?: SortOrder
    phone?: SortOrder
    isActive?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }
  export type JsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type EnumStudentStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.StudentStatus | EnumStudentStatusFieldRefInput<$PrismaModel>
    in?: $Enums.StudentStatus[] | ListEnumStudentStatusFieldRefInput<$PrismaModel>
    notIn?: $Enums.StudentStatus[] | ListEnumStudentStatusFieldRefInput<$PrismaModel>
    not?: NestedEnumStudentStatusFilter<$PrismaModel> | $Enums.StudentStatus
  }

  export type StudentUserScalarRelationFilter = {
    is?: StudentUserWhereInput
    isNot?: StudentUserWhereInput
  }

  export type StudentProgressListRelationFilter = {
    every?: StudentProgressWhereInput
    some?: StudentProgressWhereInput
    none?: StudentProgressWhereInput
  }

  export type AssignmentSubmissionListRelationFilter = {
    every?: AssignmentSubmissionWhereInput
    some?: AssignmentSubmissionWhereInput
    none?: AssignmentSubmissionWhereInput
  }

  export type StudentResourceAccessListRelationFilter = {
    every?: StudentResourceAccessWhereInput
    some?: StudentResourceAccessWhereInput
    none?: StudentResourceAccessWhereInput
  }

  export type StudentScheduleListRelationFilter = {
    every?: StudentScheduleWhereInput
    some?: StudentScheduleWhereInput
    none?: StudentScheduleWhereInput
  }

  export type StudentProgressOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type AssignmentSubmissionOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type StudentResourceAccessOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type StudentScheduleOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type StudentCountOrderByAggregateInput = {
    id?: SortOrder
    studentUserId?: SortOrder
    studentId?: SortOrder
    dateOfBirth?: SortOrder
    emergencyContact?: SortOrder
    enrollmentDate?: SortOrder
    currentLevel?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StudentMaxOrderByAggregateInput = {
    id?: SortOrder
    studentUserId?: SortOrder
    studentId?: SortOrder
    dateOfBirth?: SortOrder
    enrollmentDate?: SortOrder
    currentLevel?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StudentMinOrderByAggregateInput = {
    id?: SortOrder
    studentUserId?: SortOrder
    studentId?: SortOrder
    dateOfBirth?: SortOrder
    enrollmentDate?: SortOrder
    currentLevel?: SortOrder
    status?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }
  export type JsonNullableWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedJsonNullableFilter<$PrismaModel>
    _max?: NestedJsonNullableFilter<$PrismaModel>
  }

  export type EnumStudentStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.StudentStatus | EnumStudentStatusFieldRefInput<$PrismaModel>
    in?: $Enums.StudentStatus[] | ListEnumStudentStatusFieldRefInput<$PrismaModel>
    notIn?: $Enums.StudentStatus[] | ListEnumStudentStatusFieldRefInput<$PrismaModel>
    not?: NestedEnumStudentStatusWithAggregatesFilter<$PrismaModel> | $Enums.StudentStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumStudentStatusFilter<$PrismaModel>
    _max?: NestedEnumStudentStatusFilter<$PrismaModel>
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type StudentScalarRelationFilter = {
    is?: StudentWhereInput
    isNot?: StudentWhereInput
  }

  export type StudentProgressStudentIdAssignmentIdCompoundUniqueInput = {
    studentId: string
    assignmentId: string
  }

  export type StudentProgressCountOrderByAggregateInput = {
    id?: SortOrder
    studentId?: SortOrder
    assignmentId?: SortOrder
    progressPercentage?: SortOrder
    grade?: SortOrder
    feedback?: SortOrder
    submittedAt?: SortOrder
    gradedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StudentProgressAvgOrderByAggregateInput = {
    progressPercentage?: SortOrder
  }

  export type StudentProgressMaxOrderByAggregateInput = {
    id?: SortOrder
    studentId?: SortOrder
    assignmentId?: SortOrder
    progressPercentage?: SortOrder
    grade?: SortOrder
    feedback?: SortOrder
    submittedAt?: SortOrder
    gradedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StudentProgressMinOrderByAggregateInput = {
    id?: SortOrder
    studentId?: SortOrder
    assignmentId?: SortOrder
    progressPercentage?: SortOrder
    grade?: SortOrder
    feedback?: SortOrder
    submittedAt?: SortOrder
    gradedAt?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StudentProgressSumOrderByAggregateInput = {
    progressPercentage?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type EnumSubmissionStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.SubmissionStatus | EnumSubmissionStatusFieldRefInput<$PrismaModel>
    in?: $Enums.SubmissionStatus[] | ListEnumSubmissionStatusFieldRefInput<$PrismaModel>
    notIn?: $Enums.SubmissionStatus[] | ListEnumSubmissionStatusFieldRefInput<$PrismaModel>
    not?: NestedEnumSubmissionStatusFilter<$PrismaModel> | $Enums.SubmissionStatus
  }

  export type AssignmentSubmissionCountOrderByAggregateInput = {
    id?: SortOrder
    studentId?: SortOrder
    assignmentId?: SortOrder
    submissionContent?: SortOrder
    fileUrl?: SortOrder
    submittedAt?: SortOrder
    status?: SortOrder
    teacherFeedback?: SortOrder
    grade?: SortOrder
    gradedAt?: SortOrder
  }

  export type AssignmentSubmissionMaxOrderByAggregateInput = {
    id?: SortOrder
    studentId?: SortOrder
    assignmentId?: SortOrder
    submissionContent?: SortOrder
    fileUrl?: SortOrder
    submittedAt?: SortOrder
    status?: SortOrder
    teacherFeedback?: SortOrder
    grade?: SortOrder
    gradedAt?: SortOrder
  }

  export type AssignmentSubmissionMinOrderByAggregateInput = {
    id?: SortOrder
    studentId?: SortOrder
    assignmentId?: SortOrder
    submissionContent?: SortOrder
    fileUrl?: SortOrder
    submittedAt?: SortOrder
    status?: SortOrder
    teacherFeedback?: SortOrder
    grade?: SortOrder
    gradedAt?: SortOrder
  }

  export type EnumSubmissionStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.SubmissionStatus | EnumSubmissionStatusFieldRefInput<$PrismaModel>
    in?: $Enums.SubmissionStatus[] | ListEnumSubmissionStatusFieldRefInput<$PrismaModel>
    notIn?: $Enums.SubmissionStatus[] | ListEnumSubmissionStatusFieldRefInput<$PrismaModel>
    not?: NestedEnumSubmissionStatusWithAggregatesFilter<$PrismaModel> | $Enums.SubmissionStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumSubmissionStatusFilter<$PrismaModel>
    _max?: NestedEnumSubmissionStatusFilter<$PrismaModel>
  }

  export type IntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type StudentResourceAccessCountOrderByAggregateInput = {
    id?: SortOrder
    studentId?: SortOrder
    resourceId?: SortOrder
    accessedAt?: SortOrder
    accessDuration?: SortOrder
  }

  export type StudentResourceAccessAvgOrderByAggregateInput = {
    accessDuration?: SortOrder
  }

  export type StudentResourceAccessMaxOrderByAggregateInput = {
    id?: SortOrder
    studentId?: SortOrder
    resourceId?: SortOrder
    accessedAt?: SortOrder
    accessDuration?: SortOrder
  }

  export type StudentResourceAccessMinOrderByAggregateInput = {
    id?: SortOrder
    studentId?: SortOrder
    resourceId?: SortOrder
    accessedAt?: SortOrder
    accessDuration?: SortOrder
  }

  export type StudentResourceAccessSumOrderByAggregateInput = {
    accessDuration?: SortOrder
  }

  export type IntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }
  export type JsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonFilterBase<$PrismaModel>>, 'path'>>

  export type JsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type StudentScheduleCountOrderByAggregateInput = {
    id?: SortOrder
    studentId?: SortOrder
    groupId?: SortOrder
    courseTitle?: SortOrder
    teacherName?: SortOrder
    cabinetNumber?: SortOrder
    schedule?: SortOrder
    startDate?: SortOrder
    endDate?: SortOrder
    isActive?: SortOrder
    lastUpdated?: SortOrder
  }

  export type StudentScheduleMaxOrderByAggregateInput = {
    id?: SortOrder
    studentId?: SortOrder
    groupId?: SortOrder
    courseTitle?: SortOrder
    teacherName?: SortOrder
    cabinetNumber?: SortOrder
    startDate?: SortOrder
    endDate?: SortOrder
    isActive?: SortOrder
    lastUpdated?: SortOrder
  }

  export type StudentScheduleMinOrderByAggregateInput = {
    id?: SortOrder
    studentId?: SortOrder
    groupId?: SortOrder
    courseTitle?: SortOrder
    teacherName?: SortOrder
    cabinetNumber?: SortOrder
    startDate?: SortOrder
    endDate?: SortOrder
    isActive?: SortOrder
    lastUpdated?: SortOrder
  }
  export type JsonWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedJsonFilter<$PrismaModel>
    _max?: NestedJsonFilter<$PrismaModel>
  }

  export type StudentCreateNestedOneWithoutStudentUserInput = {
    create?: XOR<StudentCreateWithoutStudentUserInput, StudentUncheckedCreateWithoutStudentUserInput>
    connectOrCreate?: StudentCreateOrConnectWithoutStudentUserInput
    connect?: StudentWhereUniqueInput
  }

  export type StudentUncheckedCreateNestedOneWithoutStudentUserInput = {
    create?: XOR<StudentCreateWithoutStudentUserInput, StudentUncheckedCreateWithoutStudentUserInput>
    connectOrCreate?: StudentCreateOrConnectWithoutStudentUserInput
    connect?: StudentWhereUniqueInput
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type StudentUpdateOneWithoutStudentUserNestedInput = {
    create?: XOR<StudentCreateWithoutStudentUserInput, StudentUncheckedCreateWithoutStudentUserInput>
    connectOrCreate?: StudentCreateOrConnectWithoutStudentUserInput
    upsert?: StudentUpsertWithoutStudentUserInput
    disconnect?: StudentWhereInput | boolean
    delete?: StudentWhereInput | boolean
    connect?: StudentWhereUniqueInput
    update?: XOR<XOR<StudentUpdateToOneWithWhereWithoutStudentUserInput, StudentUpdateWithoutStudentUserInput>, StudentUncheckedUpdateWithoutStudentUserInput>
  }

  export type StudentUncheckedUpdateOneWithoutStudentUserNestedInput = {
    create?: XOR<StudentCreateWithoutStudentUserInput, StudentUncheckedCreateWithoutStudentUserInput>
    connectOrCreate?: StudentCreateOrConnectWithoutStudentUserInput
    upsert?: StudentUpsertWithoutStudentUserInput
    disconnect?: StudentWhereInput | boolean
    delete?: StudentWhereInput | boolean
    connect?: StudentWhereUniqueInput
    update?: XOR<XOR<StudentUpdateToOneWithWhereWithoutStudentUserInput, StudentUpdateWithoutStudentUserInput>, StudentUncheckedUpdateWithoutStudentUserInput>
  }

  export type StudentUserCreateNestedOneWithoutStudentInput = {
    create?: XOR<StudentUserCreateWithoutStudentInput, StudentUserUncheckedCreateWithoutStudentInput>
    connectOrCreate?: StudentUserCreateOrConnectWithoutStudentInput
    connect?: StudentUserWhereUniqueInput
  }

  export type StudentProgressCreateNestedManyWithoutStudentInput = {
    create?: XOR<StudentProgressCreateWithoutStudentInput, StudentProgressUncheckedCreateWithoutStudentInput> | StudentProgressCreateWithoutStudentInput[] | StudentProgressUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: StudentProgressCreateOrConnectWithoutStudentInput | StudentProgressCreateOrConnectWithoutStudentInput[]
    createMany?: StudentProgressCreateManyStudentInputEnvelope
    connect?: StudentProgressWhereUniqueInput | StudentProgressWhereUniqueInput[]
  }

  export type AssignmentSubmissionCreateNestedManyWithoutStudentInput = {
    create?: XOR<AssignmentSubmissionCreateWithoutStudentInput, AssignmentSubmissionUncheckedCreateWithoutStudentInput> | AssignmentSubmissionCreateWithoutStudentInput[] | AssignmentSubmissionUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: AssignmentSubmissionCreateOrConnectWithoutStudentInput | AssignmentSubmissionCreateOrConnectWithoutStudentInput[]
    createMany?: AssignmentSubmissionCreateManyStudentInputEnvelope
    connect?: AssignmentSubmissionWhereUniqueInput | AssignmentSubmissionWhereUniqueInput[]
  }

  export type StudentResourceAccessCreateNestedManyWithoutStudentInput = {
    create?: XOR<StudentResourceAccessCreateWithoutStudentInput, StudentResourceAccessUncheckedCreateWithoutStudentInput> | StudentResourceAccessCreateWithoutStudentInput[] | StudentResourceAccessUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: StudentResourceAccessCreateOrConnectWithoutStudentInput | StudentResourceAccessCreateOrConnectWithoutStudentInput[]
    createMany?: StudentResourceAccessCreateManyStudentInputEnvelope
    connect?: StudentResourceAccessWhereUniqueInput | StudentResourceAccessWhereUniqueInput[]
  }

  export type StudentScheduleCreateNestedManyWithoutStudentInput = {
    create?: XOR<StudentScheduleCreateWithoutStudentInput, StudentScheduleUncheckedCreateWithoutStudentInput> | StudentScheduleCreateWithoutStudentInput[] | StudentScheduleUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: StudentScheduleCreateOrConnectWithoutStudentInput | StudentScheduleCreateOrConnectWithoutStudentInput[]
    createMany?: StudentScheduleCreateManyStudentInputEnvelope
    connect?: StudentScheduleWhereUniqueInput | StudentScheduleWhereUniqueInput[]
  }

  export type StudentProgressUncheckedCreateNestedManyWithoutStudentInput = {
    create?: XOR<StudentProgressCreateWithoutStudentInput, StudentProgressUncheckedCreateWithoutStudentInput> | StudentProgressCreateWithoutStudentInput[] | StudentProgressUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: StudentProgressCreateOrConnectWithoutStudentInput | StudentProgressCreateOrConnectWithoutStudentInput[]
    createMany?: StudentProgressCreateManyStudentInputEnvelope
    connect?: StudentProgressWhereUniqueInput | StudentProgressWhereUniqueInput[]
  }

  export type AssignmentSubmissionUncheckedCreateNestedManyWithoutStudentInput = {
    create?: XOR<AssignmentSubmissionCreateWithoutStudentInput, AssignmentSubmissionUncheckedCreateWithoutStudentInput> | AssignmentSubmissionCreateWithoutStudentInput[] | AssignmentSubmissionUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: AssignmentSubmissionCreateOrConnectWithoutStudentInput | AssignmentSubmissionCreateOrConnectWithoutStudentInput[]
    createMany?: AssignmentSubmissionCreateManyStudentInputEnvelope
    connect?: AssignmentSubmissionWhereUniqueInput | AssignmentSubmissionWhereUniqueInput[]
  }

  export type StudentResourceAccessUncheckedCreateNestedManyWithoutStudentInput = {
    create?: XOR<StudentResourceAccessCreateWithoutStudentInput, StudentResourceAccessUncheckedCreateWithoutStudentInput> | StudentResourceAccessCreateWithoutStudentInput[] | StudentResourceAccessUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: StudentResourceAccessCreateOrConnectWithoutStudentInput | StudentResourceAccessCreateOrConnectWithoutStudentInput[]
    createMany?: StudentResourceAccessCreateManyStudentInputEnvelope
    connect?: StudentResourceAccessWhereUniqueInput | StudentResourceAccessWhereUniqueInput[]
  }

  export type StudentScheduleUncheckedCreateNestedManyWithoutStudentInput = {
    create?: XOR<StudentScheduleCreateWithoutStudentInput, StudentScheduleUncheckedCreateWithoutStudentInput> | StudentScheduleCreateWithoutStudentInput[] | StudentScheduleUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: StudentScheduleCreateOrConnectWithoutStudentInput | StudentScheduleCreateOrConnectWithoutStudentInput[]
    createMany?: StudentScheduleCreateManyStudentInputEnvelope
    connect?: StudentScheduleWhereUniqueInput | StudentScheduleWhereUniqueInput[]
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type EnumStudentStatusFieldUpdateOperationsInput = {
    set?: $Enums.StudentStatus
  }

  export type StudentUserUpdateOneRequiredWithoutStudentNestedInput = {
    create?: XOR<StudentUserCreateWithoutStudentInput, StudentUserUncheckedCreateWithoutStudentInput>
    connectOrCreate?: StudentUserCreateOrConnectWithoutStudentInput
    upsert?: StudentUserUpsertWithoutStudentInput
    connect?: StudentUserWhereUniqueInput
    update?: XOR<XOR<StudentUserUpdateToOneWithWhereWithoutStudentInput, StudentUserUpdateWithoutStudentInput>, StudentUserUncheckedUpdateWithoutStudentInput>
  }

  export type StudentProgressUpdateManyWithoutStudentNestedInput = {
    create?: XOR<StudentProgressCreateWithoutStudentInput, StudentProgressUncheckedCreateWithoutStudentInput> | StudentProgressCreateWithoutStudentInput[] | StudentProgressUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: StudentProgressCreateOrConnectWithoutStudentInput | StudentProgressCreateOrConnectWithoutStudentInput[]
    upsert?: StudentProgressUpsertWithWhereUniqueWithoutStudentInput | StudentProgressUpsertWithWhereUniqueWithoutStudentInput[]
    createMany?: StudentProgressCreateManyStudentInputEnvelope
    set?: StudentProgressWhereUniqueInput | StudentProgressWhereUniqueInput[]
    disconnect?: StudentProgressWhereUniqueInput | StudentProgressWhereUniqueInput[]
    delete?: StudentProgressWhereUniqueInput | StudentProgressWhereUniqueInput[]
    connect?: StudentProgressWhereUniqueInput | StudentProgressWhereUniqueInput[]
    update?: StudentProgressUpdateWithWhereUniqueWithoutStudentInput | StudentProgressUpdateWithWhereUniqueWithoutStudentInput[]
    updateMany?: StudentProgressUpdateManyWithWhereWithoutStudentInput | StudentProgressUpdateManyWithWhereWithoutStudentInput[]
    deleteMany?: StudentProgressScalarWhereInput | StudentProgressScalarWhereInput[]
  }

  export type AssignmentSubmissionUpdateManyWithoutStudentNestedInput = {
    create?: XOR<AssignmentSubmissionCreateWithoutStudentInput, AssignmentSubmissionUncheckedCreateWithoutStudentInput> | AssignmentSubmissionCreateWithoutStudentInput[] | AssignmentSubmissionUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: AssignmentSubmissionCreateOrConnectWithoutStudentInput | AssignmentSubmissionCreateOrConnectWithoutStudentInput[]
    upsert?: AssignmentSubmissionUpsertWithWhereUniqueWithoutStudentInput | AssignmentSubmissionUpsertWithWhereUniqueWithoutStudentInput[]
    createMany?: AssignmentSubmissionCreateManyStudentInputEnvelope
    set?: AssignmentSubmissionWhereUniqueInput | AssignmentSubmissionWhereUniqueInput[]
    disconnect?: AssignmentSubmissionWhereUniqueInput | AssignmentSubmissionWhereUniqueInput[]
    delete?: AssignmentSubmissionWhereUniqueInput | AssignmentSubmissionWhereUniqueInput[]
    connect?: AssignmentSubmissionWhereUniqueInput | AssignmentSubmissionWhereUniqueInput[]
    update?: AssignmentSubmissionUpdateWithWhereUniqueWithoutStudentInput | AssignmentSubmissionUpdateWithWhereUniqueWithoutStudentInput[]
    updateMany?: AssignmentSubmissionUpdateManyWithWhereWithoutStudentInput | AssignmentSubmissionUpdateManyWithWhereWithoutStudentInput[]
    deleteMany?: AssignmentSubmissionScalarWhereInput | AssignmentSubmissionScalarWhereInput[]
  }

  export type StudentResourceAccessUpdateManyWithoutStudentNestedInput = {
    create?: XOR<StudentResourceAccessCreateWithoutStudentInput, StudentResourceAccessUncheckedCreateWithoutStudentInput> | StudentResourceAccessCreateWithoutStudentInput[] | StudentResourceAccessUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: StudentResourceAccessCreateOrConnectWithoutStudentInput | StudentResourceAccessCreateOrConnectWithoutStudentInput[]
    upsert?: StudentResourceAccessUpsertWithWhereUniqueWithoutStudentInput | StudentResourceAccessUpsertWithWhereUniqueWithoutStudentInput[]
    createMany?: StudentResourceAccessCreateManyStudentInputEnvelope
    set?: StudentResourceAccessWhereUniqueInput | StudentResourceAccessWhereUniqueInput[]
    disconnect?: StudentResourceAccessWhereUniqueInput | StudentResourceAccessWhereUniqueInput[]
    delete?: StudentResourceAccessWhereUniqueInput | StudentResourceAccessWhereUniqueInput[]
    connect?: StudentResourceAccessWhereUniqueInput | StudentResourceAccessWhereUniqueInput[]
    update?: StudentResourceAccessUpdateWithWhereUniqueWithoutStudentInput | StudentResourceAccessUpdateWithWhereUniqueWithoutStudentInput[]
    updateMany?: StudentResourceAccessUpdateManyWithWhereWithoutStudentInput | StudentResourceAccessUpdateManyWithWhereWithoutStudentInput[]
    deleteMany?: StudentResourceAccessScalarWhereInput | StudentResourceAccessScalarWhereInput[]
  }

  export type StudentScheduleUpdateManyWithoutStudentNestedInput = {
    create?: XOR<StudentScheduleCreateWithoutStudentInput, StudentScheduleUncheckedCreateWithoutStudentInput> | StudentScheduleCreateWithoutStudentInput[] | StudentScheduleUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: StudentScheduleCreateOrConnectWithoutStudentInput | StudentScheduleCreateOrConnectWithoutStudentInput[]
    upsert?: StudentScheduleUpsertWithWhereUniqueWithoutStudentInput | StudentScheduleUpsertWithWhereUniqueWithoutStudentInput[]
    createMany?: StudentScheduleCreateManyStudentInputEnvelope
    set?: StudentScheduleWhereUniqueInput | StudentScheduleWhereUniqueInput[]
    disconnect?: StudentScheduleWhereUniqueInput | StudentScheduleWhereUniqueInput[]
    delete?: StudentScheduleWhereUniqueInput | StudentScheduleWhereUniqueInput[]
    connect?: StudentScheduleWhereUniqueInput | StudentScheduleWhereUniqueInput[]
    update?: StudentScheduleUpdateWithWhereUniqueWithoutStudentInput | StudentScheduleUpdateWithWhereUniqueWithoutStudentInput[]
    updateMany?: StudentScheduleUpdateManyWithWhereWithoutStudentInput | StudentScheduleUpdateManyWithWhereWithoutStudentInput[]
    deleteMany?: StudentScheduleScalarWhereInput | StudentScheduleScalarWhereInput[]
  }

  export type StudentProgressUncheckedUpdateManyWithoutStudentNestedInput = {
    create?: XOR<StudentProgressCreateWithoutStudentInput, StudentProgressUncheckedCreateWithoutStudentInput> | StudentProgressCreateWithoutStudentInput[] | StudentProgressUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: StudentProgressCreateOrConnectWithoutStudentInput | StudentProgressCreateOrConnectWithoutStudentInput[]
    upsert?: StudentProgressUpsertWithWhereUniqueWithoutStudentInput | StudentProgressUpsertWithWhereUniqueWithoutStudentInput[]
    createMany?: StudentProgressCreateManyStudentInputEnvelope
    set?: StudentProgressWhereUniqueInput | StudentProgressWhereUniqueInput[]
    disconnect?: StudentProgressWhereUniqueInput | StudentProgressWhereUniqueInput[]
    delete?: StudentProgressWhereUniqueInput | StudentProgressWhereUniqueInput[]
    connect?: StudentProgressWhereUniqueInput | StudentProgressWhereUniqueInput[]
    update?: StudentProgressUpdateWithWhereUniqueWithoutStudentInput | StudentProgressUpdateWithWhereUniqueWithoutStudentInput[]
    updateMany?: StudentProgressUpdateManyWithWhereWithoutStudentInput | StudentProgressUpdateManyWithWhereWithoutStudentInput[]
    deleteMany?: StudentProgressScalarWhereInput | StudentProgressScalarWhereInput[]
  }

  export type AssignmentSubmissionUncheckedUpdateManyWithoutStudentNestedInput = {
    create?: XOR<AssignmentSubmissionCreateWithoutStudentInput, AssignmentSubmissionUncheckedCreateWithoutStudentInput> | AssignmentSubmissionCreateWithoutStudentInput[] | AssignmentSubmissionUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: AssignmentSubmissionCreateOrConnectWithoutStudentInput | AssignmentSubmissionCreateOrConnectWithoutStudentInput[]
    upsert?: AssignmentSubmissionUpsertWithWhereUniqueWithoutStudentInput | AssignmentSubmissionUpsertWithWhereUniqueWithoutStudentInput[]
    createMany?: AssignmentSubmissionCreateManyStudentInputEnvelope
    set?: AssignmentSubmissionWhereUniqueInput | AssignmentSubmissionWhereUniqueInput[]
    disconnect?: AssignmentSubmissionWhereUniqueInput | AssignmentSubmissionWhereUniqueInput[]
    delete?: AssignmentSubmissionWhereUniqueInput | AssignmentSubmissionWhereUniqueInput[]
    connect?: AssignmentSubmissionWhereUniqueInput | AssignmentSubmissionWhereUniqueInput[]
    update?: AssignmentSubmissionUpdateWithWhereUniqueWithoutStudentInput | AssignmentSubmissionUpdateWithWhereUniqueWithoutStudentInput[]
    updateMany?: AssignmentSubmissionUpdateManyWithWhereWithoutStudentInput | AssignmentSubmissionUpdateManyWithWhereWithoutStudentInput[]
    deleteMany?: AssignmentSubmissionScalarWhereInput | AssignmentSubmissionScalarWhereInput[]
  }

  export type StudentResourceAccessUncheckedUpdateManyWithoutStudentNestedInput = {
    create?: XOR<StudentResourceAccessCreateWithoutStudentInput, StudentResourceAccessUncheckedCreateWithoutStudentInput> | StudentResourceAccessCreateWithoutStudentInput[] | StudentResourceAccessUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: StudentResourceAccessCreateOrConnectWithoutStudentInput | StudentResourceAccessCreateOrConnectWithoutStudentInput[]
    upsert?: StudentResourceAccessUpsertWithWhereUniqueWithoutStudentInput | StudentResourceAccessUpsertWithWhereUniqueWithoutStudentInput[]
    createMany?: StudentResourceAccessCreateManyStudentInputEnvelope
    set?: StudentResourceAccessWhereUniqueInput | StudentResourceAccessWhereUniqueInput[]
    disconnect?: StudentResourceAccessWhereUniqueInput | StudentResourceAccessWhereUniqueInput[]
    delete?: StudentResourceAccessWhereUniqueInput | StudentResourceAccessWhereUniqueInput[]
    connect?: StudentResourceAccessWhereUniqueInput | StudentResourceAccessWhereUniqueInput[]
    update?: StudentResourceAccessUpdateWithWhereUniqueWithoutStudentInput | StudentResourceAccessUpdateWithWhereUniqueWithoutStudentInput[]
    updateMany?: StudentResourceAccessUpdateManyWithWhereWithoutStudentInput | StudentResourceAccessUpdateManyWithWhereWithoutStudentInput[]
    deleteMany?: StudentResourceAccessScalarWhereInput | StudentResourceAccessScalarWhereInput[]
  }

  export type StudentScheduleUncheckedUpdateManyWithoutStudentNestedInput = {
    create?: XOR<StudentScheduleCreateWithoutStudentInput, StudentScheduleUncheckedCreateWithoutStudentInput> | StudentScheduleCreateWithoutStudentInput[] | StudentScheduleUncheckedCreateWithoutStudentInput[]
    connectOrCreate?: StudentScheduleCreateOrConnectWithoutStudentInput | StudentScheduleCreateOrConnectWithoutStudentInput[]
    upsert?: StudentScheduleUpsertWithWhereUniqueWithoutStudentInput | StudentScheduleUpsertWithWhereUniqueWithoutStudentInput[]
    createMany?: StudentScheduleCreateManyStudentInputEnvelope
    set?: StudentScheduleWhereUniqueInput | StudentScheduleWhereUniqueInput[]
    disconnect?: StudentScheduleWhereUniqueInput | StudentScheduleWhereUniqueInput[]
    delete?: StudentScheduleWhereUniqueInput | StudentScheduleWhereUniqueInput[]
    connect?: StudentScheduleWhereUniqueInput | StudentScheduleWhereUniqueInput[]
    update?: StudentScheduleUpdateWithWhereUniqueWithoutStudentInput | StudentScheduleUpdateWithWhereUniqueWithoutStudentInput[]
    updateMany?: StudentScheduleUpdateManyWithWhereWithoutStudentInput | StudentScheduleUpdateManyWithWhereWithoutStudentInput[]
    deleteMany?: StudentScheduleScalarWhereInput | StudentScheduleScalarWhereInput[]
  }

  export type StudentCreateNestedOneWithoutProgressInput = {
    create?: XOR<StudentCreateWithoutProgressInput, StudentUncheckedCreateWithoutProgressInput>
    connectOrCreate?: StudentCreateOrConnectWithoutProgressInput
    connect?: StudentWhereUniqueInput
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type StudentUpdateOneRequiredWithoutProgressNestedInput = {
    create?: XOR<StudentCreateWithoutProgressInput, StudentUncheckedCreateWithoutProgressInput>
    connectOrCreate?: StudentCreateOrConnectWithoutProgressInput
    upsert?: StudentUpsertWithoutProgressInput
    connect?: StudentWhereUniqueInput
    update?: XOR<XOR<StudentUpdateToOneWithWhereWithoutProgressInput, StudentUpdateWithoutProgressInput>, StudentUncheckedUpdateWithoutProgressInput>
  }

  export type StudentCreateNestedOneWithoutSubmissionsInput = {
    create?: XOR<StudentCreateWithoutSubmissionsInput, StudentUncheckedCreateWithoutSubmissionsInput>
    connectOrCreate?: StudentCreateOrConnectWithoutSubmissionsInput
    connect?: StudentWhereUniqueInput
  }

  export type EnumSubmissionStatusFieldUpdateOperationsInput = {
    set?: $Enums.SubmissionStatus
  }

  export type StudentUpdateOneRequiredWithoutSubmissionsNestedInput = {
    create?: XOR<StudentCreateWithoutSubmissionsInput, StudentUncheckedCreateWithoutSubmissionsInput>
    connectOrCreate?: StudentCreateOrConnectWithoutSubmissionsInput
    upsert?: StudentUpsertWithoutSubmissionsInput
    connect?: StudentWhereUniqueInput
    update?: XOR<XOR<StudentUpdateToOneWithWhereWithoutSubmissionsInput, StudentUpdateWithoutSubmissionsInput>, StudentUncheckedUpdateWithoutSubmissionsInput>
  }

  export type StudentCreateNestedOneWithoutResourceAccessInput = {
    create?: XOR<StudentCreateWithoutResourceAccessInput, StudentUncheckedCreateWithoutResourceAccessInput>
    connectOrCreate?: StudentCreateOrConnectWithoutResourceAccessInput
    connect?: StudentWhereUniqueInput
  }

  export type NullableIntFieldUpdateOperationsInput = {
    set?: number | null
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type StudentUpdateOneRequiredWithoutResourceAccessNestedInput = {
    create?: XOR<StudentCreateWithoutResourceAccessInput, StudentUncheckedCreateWithoutResourceAccessInput>
    connectOrCreate?: StudentCreateOrConnectWithoutResourceAccessInput
    upsert?: StudentUpsertWithoutResourceAccessInput
    connect?: StudentWhereUniqueInput
    update?: XOR<XOR<StudentUpdateToOneWithWhereWithoutResourceAccessInput, StudentUpdateWithoutResourceAccessInput>, StudentUncheckedUpdateWithoutResourceAccessInput>
  }

  export type StudentCreateNestedOneWithoutSchedulesInput = {
    create?: XOR<StudentCreateWithoutSchedulesInput, StudentUncheckedCreateWithoutSchedulesInput>
    connectOrCreate?: StudentCreateOrConnectWithoutSchedulesInput
    connect?: StudentWhereUniqueInput
  }

  export type StudentUpdateOneRequiredWithoutSchedulesNestedInput = {
    create?: XOR<StudentCreateWithoutSchedulesInput, StudentUncheckedCreateWithoutSchedulesInput>
    connectOrCreate?: StudentCreateOrConnectWithoutSchedulesInput
    upsert?: StudentUpsertWithoutSchedulesInput
    connect?: StudentWhereUniqueInput
    update?: XOR<XOR<StudentUpdateToOneWithWhereWithoutSchedulesInput, StudentUpdateWithoutSchedulesInput>, StudentUncheckedUpdateWithoutSchedulesInput>
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedEnumStudentStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.StudentStatus | EnumStudentStatusFieldRefInput<$PrismaModel>
    in?: $Enums.StudentStatus[] | ListEnumStudentStatusFieldRefInput<$PrismaModel>
    notIn?: $Enums.StudentStatus[] | ListEnumStudentStatusFieldRefInput<$PrismaModel>
    not?: NestedEnumStudentStatusFilter<$PrismaModel> | $Enums.StudentStatus
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }
  export type NestedJsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type NestedEnumStudentStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.StudentStatus | EnumStudentStatusFieldRefInput<$PrismaModel>
    in?: $Enums.StudentStatus[] | ListEnumStudentStatusFieldRefInput<$PrismaModel>
    notIn?: $Enums.StudentStatus[] | ListEnumStudentStatusFieldRefInput<$PrismaModel>
    not?: NestedEnumStudentStatusWithAggregatesFilter<$PrismaModel> | $Enums.StudentStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumStudentStatusFilter<$PrismaModel>
    _max?: NestedEnumStudentStatusFilter<$PrismaModel>
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedEnumSubmissionStatusFilter<$PrismaModel = never> = {
    equals?: $Enums.SubmissionStatus | EnumSubmissionStatusFieldRefInput<$PrismaModel>
    in?: $Enums.SubmissionStatus[] | ListEnumSubmissionStatusFieldRefInput<$PrismaModel>
    notIn?: $Enums.SubmissionStatus[] | ListEnumSubmissionStatusFieldRefInput<$PrismaModel>
    not?: NestedEnumSubmissionStatusFilter<$PrismaModel> | $Enums.SubmissionStatus
  }

  export type NestedEnumSubmissionStatusWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.SubmissionStatus | EnumSubmissionStatusFieldRefInput<$PrismaModel>
    in?: $Enums.SubmissionStatus[] | ListEnumSubmissionStatusFieldRefInput<$PrismaModel>
    notIn?: $Enums.SubmissionStatus[] | ListEnumSubmissionStatusFieldRefInput<$PrismaModel>
    not?: NestedEnumSubmissionStatusWithAggregatesFilter<$PrismaModel> | $Enums.SubmissionStatus
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumSubmissionStatusFilter<$PrismaModel>
    _max?: NestedEnumSubmissionStatusFilter<$PrismaModel>
  }

  export type NestedIntNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableWithAggregatesFilter<$PrismaModel> | number | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _avg?: NestedFloatNullableFilter<$PrismaModel>
    _sum?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedIntNullableFilter<$PrismaModel>
    _max?: NestedIntNullableFilter<$PrismaModel>
  }

  export type NestedFloatNullableFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel> | null
    in?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel> | null
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatNullableFilter<$PrismaModel> | number | null
  }
  export type NestedJsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type StudentCreateWithoutStudentUserInput = {
    id?: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    progress?: StudentProgressCreateNestedManyWithoutStudentInput
    submissions?: AssignmentSubmissionCreateNestedManyWithoutStudentInput
    resourceAccess?: StudentResourceAccessCreateNestedManyWithoutStudentInput
    schedules?: StudentScheduleCreateNestedManyWithoutStudentInput
  }

  export type StudentUncheckedCreateWithoutStudentUserInput = {
    id?: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    progress?: StudentProgressUncheckedCreateNestedManyWithoutStudentInput
    submissions?: AssignmentSubmissionUncheckedCreateNestedManyWithoutStudentInput
    resourceAccess?: StudentResourceAccessUncheckedCreateNestedManyWithoutStudentInput
    schedules?: StudentScheduleUncheckedCreateNestedManyWithoutStudentInput
  }

  export type StudentCreateOrConnectWithoutStudentUserInput = {
    where: StudentWhereUniqueInput
    create: XOR<StudentCreateWithoutStudentUserInput, StudentUncheckedCreateWithoutStudentUserInput>
  }

  export type StudentUpsertWithoutStudentUserInput = {
    update: XOR<StudentUpdateWithoutStudentUserInput, StudentUncheckedUpdateWithoutStudentUserInput>
    create: XOR<StudentCreateWithoutStudentUserInput, StudentUncheckedCreateWithoutStudentUserInput>
    where?: StudentWhereInput
  }

  export type StudentUpdateToOneWithWhereWithoutStudentUserInput = {
    where?: StudentWhereInput
    data: XOR<StudentUpdateWithoutStudentUserInput, StudentUncheckedUpdateWithoutStudentUserInput>
  }

  export type StudentUpdateWithoutStudentUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    progress?: StudentProgressUpdateManyWithoutStudentNestedInput
    submissions?: AssignmentSubmissionUpdateManyWithoutStudentNestedInput
    resourceAccess?: StudentResourceAccessUpdateManyWithoutStudentNestedInput
    schedules?: StudentScheduleUpdateManyWithoutStudentNestedInput
  }

  export type StudentUncheckedUpdateWithoutStudentUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    progress?: StudentProgressUncheckedUpdateManyWithoutStudentNestedInput
    submissions?: AssignmentSubmissionUncheckedUpdateManyWithoutStudentNestedInput
    resourceAccess?: StudentResourceAccessUncheckedUpdateManyWithoutStudentNestedInput
    schedules?: StudentScheduleUncheckedUpdateManyWithoutStudentNestedInput
  }

  export type StudentUserCreateWithoutStudentInput = {
    id?: string
    email: string
    passwordHash: string
    firstName: string
    lastName: string
    phone?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type StudentUserUncheckedCreateWithoutStudentInput = {
    id?: string
    email: string
    passwordHash: string
    firstName: string
    lastName: string
    phone?: string | null
    isActive?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type StudentUserCreateOrConnectWithoutStudentInput = {
    where: StudentUserWhereUniqueInput
    create: XOR<StudentUserCreateWithoutStudentInput, StudentUserUncheckedCreateWithoutStudentInput>
  }

  export type StudentProgressCreateWithoutStudentInput = {
    id?: string
    assignmentId: string
    progressPercentage: number
    grade?: string | null
    feedback?: string | null
    submittedAt?: Date | string | null
    gradedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type StudentProgressUncheckedCreateWithoutStudentInput = {
    id?: string
    assignmentId: string
    progressPercentage: number
    grade?: string | null
    feedback?: string | null
    submittedAt?: Date | string | null
    gradedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type StudentProgressCreateOrConnectWithoutStudentInput = {
    where: StudentProgressWhereUniqueInput
    create: XOR<StudentProgressCreateWithoutStudentInput, StudentProgressUncheckedCreateWithoutStudentInput>
  }

  export type StudentProgressCreateManyStudentInputEnvelope = {
    data: StudentProgressCreateManyStudentInput | StudentProgressCreateManyStudentInput[]
    skipDuplicates?: boolean
  }

  export type AssignmentSubmissionCreateWithoutStudentInput = {
    id?: string
    assignmentId: string
    submissionContent?: string | null
    fileUrl?: string | null
    submittedAt?: Date | string
    status?: $Enums.SubmissionStatus
    teacherFeedback?: string | null
    grade?: string | null
    gradedAt?: Date | string | null
  }

  export type AssignmentSubmissionUncheckedCreateWithoutStudentInput = {
    id?: string
    assignmentId: string
    submissionContent?: string | null
    fileUrl?: string | null
    submittedAt?: Date | string
    status?: $Enums.SubmissionStatus
    teacherFeedback?: string | null
    grade?: string | null
    gradedAt?: Date | string | null
  }

  export type AssignmentSubmissionCreateOrConnectWithoutStudentInput = {
    where: AssignmentSubmissionWhereUniqueInput
    create: XOR<AssignmentSubmissionCreateWithoutStudentInput, AssignmentSubmissionUncheckedCreateWithoutStudentInput>
  }

  export type AssignmentSubmissionCreateManyStudentInputEnvelope = {
    data: AssignmentSubmissionCreateManyStudentInput | AssignmentSubmissionCreateManyStudentInput[]
    skipDuplicates?: boolean
  }

  export type StudentResourceAccessCreateWithoutStudentInput = {
    id?: string
    resourceId: string
    accessedAt?: Date | string
    accessDuration?: number | null
  }

  export type StudentResourceAccessUncheckedCreateWithoutStudentInput = {
    id?: string
    resourceId: string
    accessedAt?: Date | string
    accessDuration?: number | null
  }

  export type StudentResourceAccessCreateOrConnectWithoutStudentInput = {
    where: StudentResourceAccessWhereUniqueInput
    create: XOR<StudentResourceAccessCreateWithoutStudentInput, StudentResourceAccessUncheckedCreateWithoutStudentInput>
  }

  export type StudentResourceAccessCreateManyStudentInputEnvelope = {
    data: StudentResourceAccessCreateManyStudentInput | StudentResourceAccessCreateManyStudentInput[]
    skipDuplicates?: boolean
  }

  export type StudentScheduleCreateWithoutStudentInput = {
    id?: string
    groupId: string
    courseTitle: string
    teacherName: string
    cabinetNumber: string
    schedule: JsonNullValueInput | InputJsonValue
    startDate: Date | string
    endDate?: Date | string | null
    isActive?: boolean
    lastUpdated?: Date | string
  }

  export type StudentScheduleUncheckedCreateWithoutStudentInput = {
    id?: string
    groupId: string
    courseTitle: string
    teacherName: string
    cabinetNumber: string
    schedule: JsonNullValueInput | InputJsonValue
    startDate: Date | string
    endDate?: Date | string | null
    isActive?: boolean
    lastUpdated?: Date | string
  }

  export type StudentScheduleCreateOrConnectWithoutStudentInput = {
    where: StudentScheduleWhereUniqueInput
    create: XOR<StudentScheduleCreateWithoutStudentInput, StudentScheduleUncheckedCreateWithoutStudentInput>
  }

  export type StudentScheduleCreateManyStudentInputEnvelope = {
    data: StudentScheduleCreateManyStudentInput | StudentScheduleCreateManyStudentInput[]
    skipDuplicates?: boolean
  }

  export type StudentUserUpsertWithoutStudentInput = {
    update: XOR<StudentUserUpdateWithoutStudentInput, StudentUserUncheckedUpdateWithoutStudentInput>
    create: XOR<StudentUserCreateWithoutStudentInput, StudentUserUncheckedCreateWithoutStudentInput>
    where?: StudentUserWhereInput
  }

  export type StudentUserUpdateToOneWithWhereWithoutStudentInput = {
    where?: StudentUserWhereInput
    data: XOR<StudentUserUpdateWithoutStudentInput, StudentUserUncheckedUpdateWithoutStudentInput>
  }

  export type StudentUserUpdateWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentUserUncheckedUpdateWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    email?: StringFieldUpdateOperationsInput | string
    passwordHash?: StringFieldUpdateOperationsInput | string
    firstName?: StringFieldUpdateOperationsInput | string
    lastName?: StringFieldUpdateOperationsInput | string
    phone?: NullableStringFieldUpdateOperationsInput | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentProgressUpsertWithWhereUniqueWithoutStudentInput = {
    where: StudentProgressWhereUniqueInput
    update: XOR<StudentProgressUpdateWithoutStudentInput, StudentProgressUncheckedUpdateWithoutStudentInput>
    create: XOR<StudentProgressCreateWithoutStudentInput, StudentProgressUncheckedCreateWithoutStudentInput>
  }

  export type StudentProgressUpdateWithWhereUniqueWithoutStudentInput = {
    where: StudentProgressWhereUniqueInput
    data: XOR<StudentProgressUpdateWithoutStudentInput, StudentProgressUncheckedUpdateWithoutStudentInput>
  }

  export type StudentProgressUpdateManyWithWhereWithoutStudentInput = {
    where: StudentProgressScalarWhereInput
    data: XOR<StudentProgressUpdateManyMutationInput, StudentProgressUncheckedUpdateManyWithoutStudentInput>
  }

  export type StudentProgressScalarWhereInput = {
    AND?: StudentProgressScalarWhereInput | StudentProgressScalarWhereInput[]
    OR?: StudentProgressScalarWhereInput[]
    NOT?: StudentProgressScalarWhereInput | StudentProgressScalarWhereInput[]
    id?: StringFilter<"StudentProgress"> | string
    studentId?: StringFilter<"StudentProgress"> | string
    assignmentId?: StringFilter<"StudentProgress"> | string
    progressPercentage?: IntFilter<"StudentProgress"> | number
    grade?: StringNullableFilter<"StudentProgress"> | string | null
    feedback?: StringNullableFilter<"StudentProgress"> | string | null
    submittedAt?: DateTimeNullableFilter<"StudentProgress"> | Date | string | null
    gradedAt?: DateTimeNullableFilter<"StudentProgress"> | Date | string | null
    createdAt?: DateTimeFilter<"StudentProgress"> | Date | string
    updatedAt?: DateTimeFilter<"StudentProgress"> | Date | string
  }

  export type AssignmentSubmissionUpsertWithWhereUniqueWithoutStudentInput = {
    where: AssignmentSubmissionWhereUniqueInput
    update: XOR<AssignmentSubmissionUpdateWithoutStudentInput, AssignmentSubmissionUncheckedUpdateWithoutStudentInput>
    create: XOR<AssignmentSubmissionCreateWithoutStudentInput, AssignmentSubmissionUncheckedCreateWithoutStudentInput>
  }

  export type AssignmentSubmissionUpdateWithWhereUniqueWithoutStudentInput = {
    where: AssignmentSubmissionWhereUniqueInput
    data: XOR<AssignmentSubmissionUpdateWithoutStudentInput, AssignmentSubmissionUncheckedUpdateWithoutStudentInput>
  }

  export type AssignmentSubmissionUpdateManyWithWhereWithoutStudentInput = {
    where: AssignmentSubmissionScalarWhereInput
    data: XOR<AssignmentSubmissionUpdateManyMutationInput, AssignmentSubmissionUncheckedUpdateManyWithoutStudentInput>
  }

  export type AssignmentSubmissionScalarWhereInput = {
    AND?: AssignmentSubmissionScalarWhereInput | AssignmentSubmissionScalarWhereInput[]
    OR?: AssignmentSubmissionScalarWhereInput[]
    NOT?: AssignmentSubmissionScalarWhereInput | AssignmentSubmissionScalarWhereInput[]
    id?: StringFilter<"AssignmentSubmission"> | string
    studentId?: StringFilter<"AssignmentSubmission"> | string
    assignmentId?: StringFilter<"AssignmentSubmission"> | string
    submissionContent?: StringNullableFilter<"AssignmentSubmission"> | string | null
    fileUrl?: StringNullableFilter<"AssignmentSubmission"> | string | null
    submittedAt?: DateTimeFilter<"AssignmentSubmission"> | Date | string
    status?: EnumSubmissionStatusFilter<"AssignmentSubmission"> | $Enums.SubmissionStatus
    teacherFeedback?: StringNullableFilter<"AssignmentSubmission"> | string | null
    grade?: StringNullableFilter<"AssignmentSubmission"> | string | null
    gradedAt?: DateTimeNullableFilter<"AssignmentSubmission"> | Date | string | null
  }

  export type StudentResourceAccessUpsertWithWhereUniqueWithoutStudentInput = {
    where: StudentResourceAccessWhereUniqueInput
    update: XOR<StudentResourceAccessUpdateWithoutStudentInput, StudentResourceAccessUncheckedUpdateWithoutStudentInput>
    create: XOR<StudentResourceAccessCreateWithoutStudentInput, StudentResourceAccessUncheckedCreateWithoutStudentInput>
  }

  export type StudentResourceAccessUpdateWithWhereUniqueWithoutStudentInput = {
    where: StudentResourceAccessWhereUniqueInput
    data: XOR<StudentResourceAccessUpdateWithoutStudentInput, StudentResourceAccessUncheckedUpdateWithoutStudentInput>
  }

  export type StudentResourceAccessUpdateManyWithWhereWithoutStudentInput = {
    where: StudentResourceAccessScalarWhereInput
    data: XOR<StudentResourceAccessUpdateManyMutationInput, StudentResourceAccessUncheckedUpdateManyWithoutStudentInput>
  }

  export type StudentResourceAccessScalarWhereInput = {
    AND?: StudentResourceAccessScalarWhereInput | StudentResourceAccessScalarWhereInput[]
    OR?: StudentResourceAccessScalarWhereInput[]
    NOT?: StudentResourceAccessScalarWhereInput | StudentResourceAccessScalarWhereInput[]
    id?: StringFilter<"StudentResourceAccess"> | string
    studentId?: StringFilter<"StudentResourceAccess"> | string
    resourceId?: StringFilter<"StudentResourceAccess"> | string
    accessedAt?: DateTimeFilter<"StudentResourceAccess"> | Date | string
    accessDuration?: IntNullableFilter<"StudentResourceAccess"> | number | null
  }

  export type StudentScheduleUpsertWithWhereUniqueWithoutStudentInput = {
    where: StudentScheduleWhereUniqueInput
    update: XOR<StudentScheduleUpdateWithoutStudentInput, StudentScheduleUncheckedUpdateWithoutStudentInput>
    create: XOR<StudentScheduleCreateWithoutStudentInput, StudentScheduleUncheckedCreateWithoutStudentInput>
  }

  export type StudentScheduleUpdateWithWhereUniqueWithoutStudentInput = {
    where: StudentScheduleWhereUniqueInput
    data: XOR<StudentScheduleUpdateWithoutStudentInput, StudentScheduleUncheckedUpdateWithoutStudentInput>
  }

  export type StudentScheduleUpdateManyWithWhereWithoutStudentInput = {
    where: StudentScheduleScalarWhereInput
    data: XOR<StudentScheduleUpdateManyMutationInput, StudentScheduleUncheckedUpdateManyWithoutStudentInput>
  }

  export type StudentScheduleScalarWhereInput = {
    AND?: StudentScheduleScalarWhereInput | StudentScheduleScalarWhereInput[]
    OR?: StudentScheduleScalarWhereInput[]
    NOT?: StudentScheduleScalarWhereInput | StudentScheduleScalarWhereInput[]
    id?: StringFilter<"StudentSchedule"> | string
    studentId?: StringFilter<"StudentSchedule"> | string
    groupId?: StringFilter<"StudentSchedule"> | string
    courseTitle?: StringFilter<"StudentSchedule"> | string
    teacherName?: StringFilter<"StudentSchedule"> | string
    cabinetNumber?: StringFilter<"StudentSchedule"> | string
    schedule?: JsonFilter<"StudentSchedule">
    startDate?: DateTimeFilter<"StudentSchedule"> | Date | string
    endDate?: DateTimeNullableFilter<"StudentSchedule"> | Date | string | null
    isActive?: BoolFilter<"StudentSchedule"> | boolean
    lastUpdated?: DateTimeFilter<"StudentSchedule"> | Date | string
  }

  export type StudentCreateWithoutProgressInput = {
    id?: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    studentUser: StudentUserCreateNestedOneWithoutStudentInput
    submissions?: AssignmentSubmissionCreateNestedManyWithoutStudentInput
    resourceAccess?: StudentResourceAccessCreateNestedManyWithoutStudentInput
    schedules?: StudentScheduleCreateNestedManyWithoutStudentInput
  }

  export type StudentUncheckedCreateWithoutProgressInput = {
    id?: string
    studentUserId: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    submissions?: AssignmentSubmissionUncheckedCreateNestedManyWithoutStudentInput
    resourceAccess?: StudentResourceAccessUncheckedCreateNestedManyWithoutStudentInput
    schedules?: StudentScheduleUncheckedCreateNestedManyWithoutStudentInput
  }

  export type StudentCreateOrConnectWithoutProgressInput = {
    where: StudentWhereUniqueInput
    create: XOR<StudentCreateWithoutProgressInput, StudentUncheckedCreateWithoutProgressInput>
  }

  export type StudentUpsertWithoutProgressInput = {
    update: XOR<StudentUpdateWithoutProgressInput, StudentUncheckedUpdateWithoutProgressInput>
    create: XOR<StudentCreateWithoutProgressInput, StudentUncheckedCreateWithoutProgressInput>
    where?: StudentWhereInput
  }

  export type StudentUpdateToOneWithWhereWithoutProgressInput = {
    where?: StudentWhereInput
    data: XOR<StudentUpdateWithoutProgressInput, StudentUncheckedUpdateWithoutProgressInput>
  }

  export type StudentUpdateWithoutProgressInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    studentUser?: StudentUserUpdateOneRequiredWithoutStudentNestedInput
    submissions?: AssignmentSubmissionUpdateManyWithoutStudentNestedInput
    resourceAccess?: StudentResourceAccessUpdateManyWithoutStudentNestedInput
    schedules?: StudentScheduleUpdateManyWithoutStudentNestedInput
  }

  export type StudentUncheckedUpdateWithoutProgressInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentUserId?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    submissions?: AssignmentSubmissionUncheckedUpdateManyWithoutStudentNestedInput
    resourceAccess?: StudentResourceAccessUncheckedUpdateManyWithoutStudentNestedInput
    schedules?: StudentScheduleUncheckedUpdateManyWithoutStudentNestedInput
  }

  export type StudentCreateWithoutSubmissionsInput = {
    id?: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    studentUser: StudentUserCreateNestedOneWithoutStudentInput
    progress?: StudentProgressCreateNestedManyWithoutStudentInput
    resourceAccess?: StudentResourceAccessCreateNestedManyWithoutStudentInput
    schedules?: StudentScheduleCreateNestedManyWithoutStudentInput
  }

  export type StudentUncheckedCreateWithoutSubmissionsInput = {
    id?: string
    studentUserId: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    progress?: StudentProgressUncheckedCreateNestedManyWithoutStudentInput
    resourceAccess?: StudentResourceAccessUncheckedCreateNestedManyWithoutStudentInput
    schedules?: StudentScheduleUncheckedCreateNestedManyWithoutStudentInput
  }

  export type StudentCreateOrConnectWithoutSubmissionsInput = {
    where: StudentWhereUniqueInput
    create: XOR<StudentCreateWithoutSubmissionsInput, StudentUncheckedCreateWithoutSubmissionsInput>
  }

  export type StudentUpsertWithoutSubmissionsInput = {
    update: XOR<StudentUpdateWithoutSubmissionsInput, StudentUncheckedUpdateWithoutSubmissionsInput>
    create: XOR<StudentCreateWithoutSubmissionsInput, StudentUncheckedCreateWithoutSubmissionsInput>
    where?: StudentWhereInput
  }

  export type StudentUpdateToOneWithWhereWithoutSubmissionsInput = {
    where?: StudentWhereInput
    data: XOR<StudentUpdateWithoutSubmissionsInput, StudentUncheckedUpdateWithoutSubmissionsInput>
  }

  export type StudentUpdateWithoutSubmissionsInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    studentUser?: StudentUserUpdateOneRequiredWithoutStudentNestedInput
    progress?: StudentProgressUpdateManyWithoutStudentNestedInput
    resourceAccess?: StudentResourceAccessUpdateManyWithoutStudentNestedInput
    schedules?: StudentScheduleUpdateManyWithoutStudentNestedInput
  }

  export type StudentUncheckedUpdateWithoutSubmissionsInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentUserId?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    progress?: StudentProgressUncheckedUpdateManyWithoutStudentNestedInput
    resourceAccess?: StudentResourceAccessUncheckedUpdateManyWithoutStudentNestedInput
    schedules?: StudentScheduleUncheckedUpdateManyWithoutStudentNestedInput
  }

  export type StudentCreateWithoutResourceAccessInput = {
    id?: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    studentUser: StudentUserCreateNestedOneWithoutStudentInput
    progress?: StudentProgressCreateNestedManyWithoutStudentInput
    submissions?: AssignmentSubmissionCreateNestedManyWithoutStudentInput
    schedules?: StudentScheduleCreateNestedManyWithoutStudentInput
  }

  export type StudentUncheckedCreateWithoutResourceAccessInput = {
    id?: string
    studentUserId: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    progress?: StudentProgressUncheckedCreateNestedManyWithoutStudentInput
    submissions?: AssignmentSubmissionUncheckedCreateNestedManyWithoutStudentInput
    schedules?: StudentScheduleUncheckedCreateNestedManyWithoutStudentInput
  }

  export type StudentCreateOrConnectWithoutResourceAccessInput = {
    where: StudentWhereUniqueInput
    create: XOR<StudentCreateWithoutResourceAccessInput, StudentUncheckedCreateWithoutResourceAccessInput>
  }

  export type StudentUpsertWithoutResourceAccessInput = {
    update: XOR<StudentUpdateWithoutResourceAccessInput, StudentUncheckedUpdateWithoutResourceAccessInput>
    create: XOR<StudentCreateWithoutResourceAccessInput, StudentUncheckedCreateWithoutResourceAccessInput>
    where?: StudentWhereInput
  }

  export type StudentUpdateToOneWithWhereWithoutResourceAccessInput = {
    where?: StudentWhereInput
    data: XOR<StudentUpdateWithoutResourceAccessInput, StudentUncheckedUpdateWithoutResourceAccessInput>
  }

  export type StudentUpdateWithoutResourceAccessInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    studentUser?: StudentUserUpdateOneRequiredWithoutStudentNestedInput
    progress?: StudentProgressUpdateManyWithoutStudentNestedInput
    submissions?: AssignmentSubmissionUpdateManyWithoutStudentNestedInput
    schedules?: StudentScheduleUpdateManyWithoutStudentNestedInput
  }

  export type StudentUncheckedUpdateWithoutResourceAccessInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentUserId?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    progress?: StudentProgressUncheckedUpdateManyWithoutStudentNestedInput
    submissions?: AssignmentSubmissionUncheckedUpdateManyWithoutStudentNestedInput
    schedules?: StudentScheduleUncheckedUpdateManyWithoutStudentNestedInput
  }

  export type StudentCreateWithoutSchedulesInput = {
    id?: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    studentUser: StudentUserCreateNestedOneWithoutStudentInput
    progress?: StudentProgressCreateNestedManyWithoutStudentInput
    submissions?: AssignmentSubmissionCreateNestedManyWithoutStudentInput
    resourceAccess?: StudentResourceAccessCreateNestedManyWithoutStudentInput
  }

  export type StudentUncheckedCreateWithoutSchedulesInput = {
    id?: string
    studentUserId: string
    studentId: string
    dateOfBirth?: Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate: Date | string
    currentLevel?: string | null
    status?: $Enums.StudentStatus
    createdAt?: Date | string
    updatedAt?: Date | string
    progress?: StudentProgressUncheckedCreateNestedManyWithoutStudentInput
    submissions?: AssignmentSubmissionUncheckedCreateNestedManyWithoutStudentInput
    resourceAccess?: StudentResourceAccessUncheckedCreateNestedManyWithoutStudentInput
  }

  export type StudentCreateOrConnectWithoutSchedulesInput = {
    where: StudentWhereUniqueInput
    create: XOR<StudentCreateWithoutSchedulesInput, StudentUncheckedCreateWithoutSchedulesInput>
  }

  export type StudentUpsertWithoutSchedulesInput = {
    update: XOR<StudentUpdateWithoutSchedulesInput, StudentUncheckedUpdateWithoutSchedulesInput>
    create: XOR<StudentCreateWithoutSchedulesInput, StudentUncheckedCreateWithoutSchedulesInput>
    where?: StudentWhereInput
  }

  export type StudentUpdateToOneWithWhereWithoutSchedulesInput = {
    where?: StudentWhereInput
    data: XOR<StudentUpdateWithoutSchedulesInput, StudentUncheckedUpdateWithoutSchedulesInput>
  }

  export type StudentUpdateWithoutSchedulesInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    studentUser?: StudentUserUpdateOneRequiredWithoutStudentNestedInput
    progress?: StudentProgressUpdateManyWithoutStudentNestedInput
    submissions?: AssignmentSubmissionUpdateManyWithoutStudentNestedInput
    resourceAccess?: StudentResourceAccessUpdateManyWithoutStudentNestedInput
  }

  export type StudentUncheckedUpdateWithoutSchedulesInput = {
    id?: StringFieldUpdateOperationsInput | string
    studentUserId?: StringFieldUpdateOperationsInput | string
    studentId?: StringFieldUpdateOperationsInput | string
    dateOfBirth?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    emergencyContact?: NullableJsonNullValueInput | InputJsonValue
    enrollmentDate?: DateTimeFieldUpdateOperationsInput | Date | string
    currentLevel?: NullableStringFieldUpdateOperationsInput | string | null
    status?: EnumStudentStatusFieldUpdateOperationsInput | $Enums.StudentStatus
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    progress?: StudentProgressUncheckedUpdateManyWithoutStudentNestedInput
    submissions?: AssignmentSubmissionUncheckedUpdateManyWithoutStudentNestedInput
    resourceAccess?: StudentResourceAccessUncheckedUpdateManyWithoutStudentNestedInput
  }

  export type StudentProgressCreateManyStudentInput = {
    id?: string
    assignmentId: string
    progressPercentage: number
    grade?: string | null
    feedback?: string | null
    submittedAt?: Date | string | null
    gradedAt?: Date | string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AssignmentSubmissionCreateManyStudentInput = {
    id?: string
    assignmentId: string
    submissionContent?: string | null
    fileUrl?: string | null
    submittedAt?: Date | string
    status?: $Enums.SubmissionStatus
    teacherFeedback?: string | null
    grade?: string | null
    gradedAt?: Date | string | null
  }

  export type StudentResourceAccessCreateManyStudentInput = {
    id?: string
    resourceId: string
    accessedAt?: Date | string
    accessDuration?: number | null
  }

  export type StudentScheduleCreateManyStudentInput = {
    id?: string
    groupId: string
    courseTitle: string
    teacherName: string
    cabinetNumber: string
    schedule: JsonNullValueInput | InputJsonValue
    startDate: Date | string
    endDate?: Date | string | null
    isActive?: boolean
    lastUpdated?: Date | string
  }

  export type StudentProgressUpdateWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    progressPercentage?: IntFieldUpdateOperationsInput | number
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    feedback?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentProgressUncheckedUpdateWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    progressPercentage?: IntFieldUpdateOperationsInput | number
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    feedback?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentProgressUncheckedUpdateManyWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    progressPercentage?: IntFieldUpdateOperationsInput | number
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    feedback?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AssignmentSubmissionUpdateWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    submissionContent?: NullableStringFieldUpdateOperationsInput | string | null
    fileUrl?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: EnumSubmissionStatusFieldUpdateOperationsInput | $Enums.SubmissionStatus
    teacherFeedback?: NullableStringFieldUpdateOperationsInput | string | null
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type AssignmentSubmissionUncheckedUpdateWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    submissionContent?: NullableStringFieldUpdateOperationsInput | string | null
    fileUrl?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: EnumSubmissionStatusFieldUpdateOperationsInput | $Enums.SubmissionStatus
    teacherFeedback?: NullableStringFieldUpdateOperationsInput | string | null
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type AssignmentSubmissionUncheckedUpdateManyWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    assignmentId?: StringFieldUpdateOperationsInput | string
    submissionContent?: NullableStringFieldUpdateOperationsInput | string | null
    fileUrl?: NullableStringFieldUpdateOperationsInput | string | null
    submittedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    status?: EnumSubmissionStatusFieldUpdateOperationsInput | $Enums.SubmissionStatus
    teacherFeedback?: NullableStringFieldUpdateOperationsInput | string | null
    grade?: NullableStringFieldUpdateOperationsInput | string | null
    gradedAt?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
  }

  export type StudentResourceAccessUpdateWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    accessedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    accessDuration?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type StudentResourceAccessUncheckedUpdateWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    accessedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    accessDuration?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type StudentResourceAccessUncheckedUpdateManyWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    resourceId?: StringFieldUpdateOperationsInput | string
    accessedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    accessDuration?: NullableIntFieldUpdateOperationsInput | number | null
  }

  export type StudentScheduleUpdateWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    groupId?: StringFieldUpdateOperationsInput | string
    courseTitle?: StringFieldUpdateOperationsInput | string
    teacherName?: StringFieldUpdateOperationsInput | string
    cabinetNumber?: StringFieldUpdateOperationsInput | string
    schedule?: JsonNullValueInput | InputJsonValue
    startDate?: DateTimeFieldUpdateOperationsInput | Date | string
    endDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    lastUpdated?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentScheduleUncheckedUpdateWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    groupId?: StringFieldUpdateOperationsInput | string
    courseTitle?: StringFieldUpdateOperationsInput | string
    teacherName?: StringFieldUpdateOperationsInput | string
    cabinetNumber?: StringFieldUpdateOperationsInput | string
    schedule?: JsonNullValueInput | InputJsonValue
    startDate?: DateTimeFieldUpdateOperationsInput | Date | string
    endDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    lastUpdated?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StudentScheduleUncheckedUpdateManyWithoutStudentInput = {
    id?: StringFieldUpdateOperationsInput | string
    groupId?: StringFieldUpdateOperationsInput | string
    courseTitle?: StringFieldUpdateOperationsInput | string
    teacherName?: StringFieldUpdateOperationsInput | string
    cabinetNumber?: StringFieldUpdateOperationsInput | string
    schedule?: JsonNullValueInput | InputJsonValue
    startDate?: DateTimeFieldUpdateOperationsInput | Date | string
    endDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    isActive?: BoolFieldUpdateOperationsInput | boolean
    lastUpdated?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}