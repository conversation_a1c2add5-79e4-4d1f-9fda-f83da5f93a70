# CRM Architecture - 3 Services

## 📋 Quick Reference
- **3 Services**: Staff, Admin, Student
- **3 Databases**: Separate database per service
- **Technology**: Next.js 15.3.4, TypeScript, Tailwind CSS, Prisma, PostgreSQL
- **Deployment**: Vercel (separate deployments per service)

## 🎯 Simplified Architecture Overview

**Goal**: Streamlined microservices architecture with 3 core services
**Technology**: Next.js 15.3.4, TypeScript, Tailwind CSS, Prisma, PostgreSQL
**Deployment**: Vercel (separate deployments per service)
**Security**: Enhanced security for admin service, standard for others

## 🏗️ Service Structure

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Staff Portal  │    │  Admin Portal   │    │ Student Portal  │
│   (Next.js)     │    │   (Next.js)     │    │   (Next.js)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Staff Service  │    │  Admin Service  │    │ Student Service │
│                 │    │ (High Security) │    │                 │
│ • Leads         │    │ • Payments      │    │ • Portal        │
│ • Courses       │    │ • Financial     │    │ • Assignments   │
│ • Groups        │    │ • User Mgmt     │    │ • Progress      │
│ • Teachers      │    │ • System Config │    │ • Resources     │
│ • Cabinets      │    │ • Audit Logs    │    │ • Schedules     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 Service Breakdown

### **1. Staff Service** 
**Repository**: `crm-staff-service`
**Database**: `STAFF_DATABASE_URL`
**Users**: Reception, Managers, Test Checkers, Teachers

**Responsibilities**:
- **Lead Management**: Lead capture, assignment, conversion tracking
- **Course Management**: Course catalog, group creation, scheduling
- **Teacher Management**: Teacher profiles, KPIs, class assignments
- **Cabinet Management**: Classroom assignment and scheduling
- **Student Assignment Creation**: Teachers create assignments for students

**Key Features**:
- Lead capture forms and conversion workflow
- Course and group creation with capacity management
- Teacher KPI tracking and performance metrics
- Cabinet (classroom) assignment system
- Assignment creation and distribution to students
- Staff role management (Reception/Managers/Test Checkers/Teachers)

### **2. Admin Service** (Enhanced Security)
**Repository**: `crm-admin-service`
**Database**: `ADMIN_DATABASE_URL`
**Users**: Admin, Cashier, Accounting

**Responsibilities**:
- **Payment Management**: Payment records, financial tracking (note-taking)
- **Financial Oversight**: Fee structures, payment history, financial reports
- **User Management**: System-wide user administration
- **System Configuration**: Business rules, system settings
- **Audit & Compliance**: Comprehensive logging and audit trails

**Key Features**:
- Payment record creation and management (no actual processing)
- Financial data tracking and reporting
- User role and permission management across all services
- System configuration and business rule management
- Comprehensive audit logging for compliance
- Enhanced security measures for financial data

### **3. Student Service**
**Repository**: `crm-student-service`
**Database**: `STUDENT_DATABASE_URL`
**Users**: Students

**Responsibilities**:
- **Student Portal**: Personal dashboard and academic information
- **Assignment Viewing**: View assignments created by teachers
- **Resource Access**: Access to notes, books, and learning materials
- **Progress Tracking**: Academic progress and grade viewing
- **Schedule Management**: Class schedules and timetables

**Key Features**:
- Student dashboard with class information
- Assignment viewing and status tracking
- Access to learning resources (notes, books, documents)
- Academic progress and grade tracking
- Class schedule and teacher contact information
- Personal profile management

## 🗄️ Database Structure

### **Staff Database** (`STAFF_DATABASE_URL`)
```sql
-- Core Tables:
- staff_users (Reception, Managers, Test Checkers, Teachers)
- leads (Lead capture and management)
- courses (Course catalog)
- groups (Class groups and scheduling)
- cabinets (Classroom management)
- teachers (Teacher profiles and KPIs)
- student_assignments (Created by teachers)
- lead_activities (Lead tracking)
```

### **Admin Database** (`ADMIN_DATABASE_URL`) - Enhanced Security
```sql
-- Core Tables:
- admin_users (Admin, Cashier, Accounting)
- payment_records (Payment tracking - note-taking)
- fee_structures (Pricing and fees)
- financial_transactions (Financial audit trail)
- system_config (System configuration)
- audit_logs (Comprehensive logging)
- user_management (Cross-service user administration)
```

### **Student Database** (`STUDENT_DATABASE_URL`)
```sql
-- Core Tables:
- student_users (Student accounts)
- students (Student profiles and information)
- student_enrollments (Course and group enrollments)
- student_progress (Academic progress and grades)
- student_resource_access (Resource access tracking)
- assignment_submissions (Assignment status and submissions)
```

## 🔄 Inter-Service Communication

### **API Communication**
```typescript
// Service URLs
const SERVICES = {
  STAFF: process.env.STAFF_SERVICE_URL || 'https://crm-staff-service.vercel.app',
  ADMIN: process.env.ADMIN_SERVICE_URL || 'https://crm-admin-service.vercel.app',
  STUDENT: process.env.STUDENT_SERVICE_URL || 'https://crm-student-service.vercel.app'
};

// Example: Admin service calling Staff service for user data
const getStaffUser = async (userId: string) => {
  const response = await fetch(`${SERVICES.STAFF}/api/users/${userId}`, {
    headers: {
      'Authorization': `Bearer ${serviceToken}`,
      'X-Service-Name': 'admin-service'
    }
  });
  return response.json();
};
```

### **Shared Data Access**
- **Staff → Student**: Teachers create assignments, view student progress
- **Admin → Staff**: User management, system configuration
- **Admin → Student**: Payment tracking, enrollment verification
- **Student → Staff**: View assignments, access resources created by teachers

## 🔐 Security Implementation

### **Admin Service Security** (Enhanced)
```typescript
const ADMIN_SECURITY = {
  mfaRequired: true,
  sessionTimeout: 30, // minutes
  auditLogging: 'comprehensive',
  dataEncryption: 'AES-256',
  accessLogging: true,
  rateLimiting: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // requests per window
  }
};
```

### **Staff Service Security** (Standard)
```typescript
const STAFF_SECURITY = {
  roleBasedAccess: true,
  sessionTimeout: 60, // minutes
  auditLogging: 'standard',
  dataEncryption: 'AES-256',
  rateLimiting: {
    windowMs: 15 * 60 * 1000,
    max: 500 // requests per window
  }
};
```

### **Student Service Security** (Standard)
```typescript
const STUDENT_SECURITY = {
  dataProtection: 'GDPR',
  sessionTimeout: 120, // minutes
  auditLogging: 'basic',
  dataEncryption: 'AES-256',
  rateLimiting: {
    windowMs: 15 * 60 * 1000,
    max: 1000 // requests per window
  }
};
```

## 📦 Repository Structure

```
innovative-centre-crm/
├── crm-staff-service/          # Staff operations
├── crm-admin-service/          # Admin & financial operations
├── crm-student-service/        # Student portal
└── crm-shared-types/           # Shared TypeScript types
```

### **Individual Repository Structure**
```
crm-[service-name]/
├── src/
│   ├── app/                    # Next.js 15.3.4 App Router
│   │   ├── api/               # API routes
│   │   ├── (dashboard)/       # Protected dashboard routes
│   │   ├── globals.css        # Global styles
│   │   └── layout.tsx         # Root layout
│   ├── components/            # React components
│   │   ├── ui/               # Shadcn/ui components
│   │   └── forms/            # Form components
│   ├── lib/                  # Utility functions
│   │   ├── db.ts             # Database connection
│   │   ├── auth.ts           # Authentication
│   │   └── utils.ts          # Helper functions
│   └── types/                # TypeScript types
├── prisma/
│   ├── schema.prisma         # Database schema
│   └── migrations/           # Database migrations
├── package.json              # Dependencies
├── next.config.js            # Next.js configuration
└── vercel.json               # Deployment configuration
```

## 🚀 Development Workflow

### **1. Setup Order**
1. **crm-shared-types** - Common types and interfaces
2. **crm-admin-service** - User management and system foundation
3. **crm-staff-service** - Core business operations
4. **crm-student-service** - Student-facing features

### **2. Environment Variables**
```bash
# Each service
DATABASE_URL="your-database-connection-string"
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="https://your-service-url.vercel.app"

# Service URLs for inter-service communication
STAFF_SERVICE_URL="https://crm-staff-service.vercel.app"
ADMIN_SERVICE_URL="https://crm-admin-service.vercel.app"
STUDENT_SERVICE_URL="https://crm-student-service.vercel.app"

# Service authentication
SERVICE_API_KEY="your-service-api-key"
```

### **3. Deployment**
```bash
# Deploy each service separately
vercel --prod --project crm-staff-service
vercel --prod --project crm-admin-service
vercel --prod --project crm-student-service
```

## 📊 User Role Mapping

### **Staff Service Users**
- **Reception**: Lead capture, initial student contact
- **Managers**: Staff oversight, performance monitoring, system administration
- **Test Checkers**: Student assessment, progress evaluation
- **Teachers**: Class management, assignment creation, student instruction

### **Admin Service Users**
- **Admin**: System administration, user management across all services
- **Cashier**: Payment processing, financial transactions
- **Accounting**: Financial reporting, audit trails, payment verification

### **Student Service Users**
- **Students**: Academic portal, assignment viewing, resource access, progress tracking

This simplified architecture maintains the separation of concerns while being much more manageable to develop, deploy, and maintain. Each service has clear responsibilities and can be developed independently while communicating through well-defined APIs.
