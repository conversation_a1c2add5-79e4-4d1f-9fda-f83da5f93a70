{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "27552ab0ad98abae9818a55187ef91b9", "previewModeSigningKey": "824ea39e0438340ed3f0dc0e5d1b0a986443130ac7d8c2fa376757c758679620", "previewModeEncryptionKey": "24ef29d9c4fe414602b1f9a8bff2ff5fa25e669daecf7361bd6cbd41c4c401fe"}}