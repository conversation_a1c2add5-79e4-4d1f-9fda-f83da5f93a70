// Service Client Implementations for Inter-Service Communication

import { ServiceClient } from './auth';
import type {
  Lead,
  CreateLeadRequest,
  Student,
  Course,
  Group,
  PaymentRecord,
  CreatePaymentRecordRequest,
  StudentProgress,
  StudentSchedule,
  ApiResponse,
  PaginatedResponse
} from '../types';

// Admin Service Client
export class AdminServiceClient extends ServiceClient {
  constructor(baseUrl: string, serviceName: string, apiKey: string) {
    super(baseUrl, serviceName, apiKey);
  }

  // Payment Management
  async getPayments(page = 1, limit = 20): Promise<PaginatedResponse<PaymentRecord>> {
    return this.get(`/api/payments?page=${page}&limit=${limit}`);
  }

  async createPayment(data: CreatePaymentRecordRequest): Promise<ApiResponse<PaymentRecord>> {
    return this.post('/api/payments', data);
  }

  async verifyPayment(paymentId: string): Promise<ApiResponse<PaymentRecord>> {
    return this.post(`/api/payments/${paymentId}/verify`, {});
  }

  // User Management
  async getUsers(serviceName?: string): Promise<ApiResponse<any[]>> {
    const query = serviceName ? `?service=${serviceName}` : '';
    return this.get(`/api/users${query}`);
  }

  async createUser(userData: any): Promise<ApiResponse<any>> {
    return this.post('/api/users', userData);
  }

  async updateUser(userId: string, userData: any): Promise<ApiResponse<any>> {
    return this.put(`/api/users/${userId}`, userData);
  }

  // System Configuration
  async getConfig(key?: string): Promise<ApiResponse<any>> {
    const query = key ? `?key=${key}` : '';
    return this.get(`/api/config${query}`);
  }

  async updateConfig(key: string, value: any): Promise<ApiResponse<any>> {
    return this.put('/api/config', { key, value });
  }
}

// Staff Service Client
export class StaffServiceClient extends ServiceClient {
  constructor(baseUrl: string, serviceName: string, apiKey: string) {
    super(baseUrl, serviceName, apiKey);
  }

  // Lead Management
  async getLeads(status?: string, page = 1, limit = 20): Promise<PaginatedResponse<Lead>> {
    const query = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
    if (status) query.append('status', status);
    return this.get(`/api/leads?${query}`);
  }

  async createLead(data: CreateLeadRequest): Promise<ApiResponse<Lead>> {
    return this.post('/api/leads', data);
  }

  async convertLead(leadId: string, studentData: any): Promise<ApiResponse<any>> {
    return this.post(`/api/leads/${leadId}/convert`, studentData);
  }

  // Course Management
  async getCourses(page = 1, limit = 20): Promise<PaginatedResponse<Course>> {
    return this.get(`/api/courses?page=${page}&limit=${limit}`);
  }

  async getCourse(courseId: string): Promise<ApiResponse<Course>> {
    return this.get(`/api/courses/${courseId}`);
  }

  async createCourse(courseData: any): Promise<ApiResponse<Course>> {
    return this.post('/api/courses', courseData);
  }

  // Group Management
  async getGroups(courseId?: string): Promise<ApiResponse<Group[]>> {
    const query = courseId ? `?courseId=${courseId}` : '';
    return this.get(`/api/groups${query}`);
  }

  async createGroup(groupData: any): Promise<ApiResponse<Group>> {
    return this.post('/api/groups', groupData);
  }

  async enrollStudent(groupId: string, studentId: string): Promise<ApiResponse<any>> {
    return this.post(`/api/groups/${groupId}/enroll`, { studentId });
  }

  // Assignment Management
  async getAssignments(studentId?: string): Promise<ApiResponse<any[]>> {
    const query = studentId ? `?studentId=${studentId}` : '';
    return this.get(`/api/assignments${query}`);
  }

  async createAssignment(assignmentData: any): Promise<ApiResponse<any>> {
    return this.post('/api/assignments', assignmentData);
  }

  // Resource Management
  async getResources(courseId?: string, groupId?: string): Promise<ApiResponse<any[]>> {
    const query = new URLSearchParams();
    if (courseId) query.append('courseId', courseId);
    if (groupId) query.append('groupId', groupId);
    return this.get(`/api/resources?${query}`);
  }

  async createResource(resourceData: any): Promise<ApiResponse<any>> {
    return this.post('/api/resources', resourceData);
  }

  // Teacher Management
  async getTeachers(): Promise<ApiResponse<any[]>> {
    return this.get('/api/teachers');
  }

  async getTeacherKPIs(teacherId: string): Promise<ApiResponse<any[]>> {
    return this.get(`/api/teachers/${teacherId}/kpis`);
  }
}

// Student Service Client
export class StudentServiceClient extends ServiceClient {
  constructor(baseUrl: string, serviceName: string, apiKey: string) {
    super(baseUrl, serviceName, apiKey);
  }

  // Student Management
  async getStudent(studentId: string): Promise<ApiResponse<Student>> {
    return this.get(`/api/students/${studentId}`);
  }

  async createStudent(studentData: any): Promise<ApiResponse<Student>> {
    return this.post('/api/students', studentData);
  }

  async updateStudent(studentId: string, studentData: any): Promise<ApiResponse<Student>> {
    return this.put(`/api/students/${studentId}`, studentData);
  }

  // Progress Management
  async getProgress(studentId: string): Promise<ApiResponse<any>> {
    return this.get(`/api/progress?studentId=${studentId}`);
  }

  async updateProgress(progressData: any): Promise<ApiResponse<StudentProgress>> {
    return this.post('/api/progress', progressData);
  }

  // Assignment Submissions
  async getSubmissions(studentId: string): Promise<ApiResponse<any[]>> {
    return this.get(`/api/submissions?studentId=${studentId}`);
  }

  async submitAssignment(submissionData: any): Promise<ApiResponse<any>> {
    return this.post('/api/submissions', submissionData);
  }

  // Schedule Management
  async getSchedule(studentId: string): Promise<ApiResponse<StudentSchedule[]>> {
    return this.get(`/api/schedule?studentId=${studentId}`);
  }

  async syncSchedule(studentId: string): Promise<ApiResponse<any>> {
    return this.post('/api/schedule/sync', { studentId });
  }

  // Resource Access
  async trackResourceAccess(studentId: string, resourceId: string): Promise<ApiResponse<any>> {
    return this.post('/api/resources/access', { studentId, resourceId });
  }
}

// Service Client Factory
export class ServiceClientFactory {
  private adminClient?: AdminServiceClient;
  private staffClient?: StaffServiceClient;
  private studentClient?: StudentServiceClient;

  constructor(
    private config: {
      adminServiceUrl: string;
      staffServiceUrl: string;
      studentServiceUrl: string;
      serviceName: string;
      apiKey: string;
    }
  ) {}

  getAdminClient(): AdminServiceClient {
    if (!this.adminClient) {
      this.adminClient = new AdminServiceClient(
        this.config.adminServiceUrl,
        this.config.serviceName,
        this.config.apiKey
      );
    }
    return this.adminClient;
  }

  getStaffClient(): StaffServiceClient {
    if (!this.staffClient) {
      this.staffClient = new StaffServiceClient(
        this.config.staffServiceUrl,
        this.config.serviceName,
        this.config.apiKey
      );
    }
    return this.staffClient;
  }

  getStudentClient(): StudentServiceClient {
    if (!this.studentClient) {
      this.studentClient = new StudentServiceClient(
        this.config.studentServiceUrl,
        this.config.serviceName,
        this.config.apiKey
      );
    }
    return this.studentClient;
  }
}
