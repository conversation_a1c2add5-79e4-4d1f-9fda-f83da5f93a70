# Student Service Environment Variables

# Database Connection
STUDENT_DATABASE_URL="postgresql://username:password@host:port/student_database"

# Authentication (NextAuth.js v5)
NEXTAUTH_SECRET="your-nextauth-secret-for-student-service"
NEXTAUTH_URL="https://crm-student-service.vercel.app"

# Service URLs for Inter-Service Communication
ADMIN_SERVICE_URL="https://crm-admin-service.vercel.app"
STAFF_SERVICE_URL="https://crm-staff-service.vercel.app"
SHARED_TYPES_URL="https://crm-shared-types.vercel.app"

# Service Authentication
SERVICE_API_KEY="your-service-api-key"
SERVICE_NAME="crm-student-service"

# Security Settings
SESSION_TIMEOUT=7200
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# JWT Configuration
JWT_SECRET="your-jwt-secret-for-student-service"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-email-password"

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH="/uploads"
ALLOWED_FILE_TYPES="pdf,doc,docx,txt,jpg,png,gif"

# Student Portal Settings
ENABLE_ASSIGNMENT_SUBMISSIONS=true
ENABLE_RESOURCE_DOWNLOADS=true
ENABLE_PROGRESS_TRACKING=true
ENABLE_SCHEDULE_VIEW=true

# Assignment Settings
MAX_SUBMISSION_SIZE=5242880
SUBMISSION_FORMATS="pdf,doc,docx,txt"
AUTO_SAVE_DRAFTS=true
DRAFT_SAVE_INTERVAL=30

# Resource Access Settings
TRACK_RESOURCE_ACCESS=true
RESOURCE_ACCESS_ANALYTICS=true
OFFLINE_RESOURCE_CACHE=false

# Progress Tracking
REAL_TIME_PROGRESS_UPDATES=true
PROGRESS_SYNC_INTERVAL=300
GRADE_NOTIFICATION_ENABLED=true

# Schedule Settings
SCHEDULE_SYNC_INTERVAL=3600
SHOW_UPCOMING_CLASSES=true
CLASS_REMINDER_MINUTES=30

# Notification Settings
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_PUSH_NOTIFICATIONS=false
ASSIGNMENT_DUE_REMINDER_DAYS=3
GRADE_NOTIFICATION_IMMEDIATE=true

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=150

# Logging
LOG_LEVEL="info"
LOG_RETENTION_DAYS=60

# Cache Settings
CACHE_ENABLED=true
CACHE_TTL=3600
REDIS_URL="redis://localhost:6379"
