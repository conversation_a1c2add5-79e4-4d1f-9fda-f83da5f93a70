import type { Metadata } from "next";
import Link from "next/link";
import "../styles/globals.css";

export const metadata: Metadata = {
  title: "Staff Portal - Innovative Centre CRM",
  description: "Lead management, course catalog, and teacher administration portal",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="min-h-screen bg-gray-50">
        <div className="flex min-h-screen">
          {/* Sidebar Navigation */}
          <aside className="w-64 bg-white shadow-sm border-r border-gray-200">
            <div className="p-6">
              <h1 className="text-xl font-bold text-gray-900">Staff Portal</h1>
              <p className="text-sm text-gray-600">Innovative Centre CRM</p>
            </div>
            <nav className="mt-6">
              <div className="px-3">
                <div className="space-y-1">
                  <Link href="/" className="bg-blue-50 text-blue-700 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    Dashboard
                  </Link>
                  <Link href="/leads" className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    Lead Management
                  </Link>
                  <Link href="/courses" className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    Course Catalog
                  </Link>
                  <Link href="/groups" className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    Group Management
                  </Link>
                  <Link href="/teachers" className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    Teacher Management
                  </Link>
                  <Link href="/assignments" className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    Assignments
                  </Link>
                  <Link href="/resources" className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                    Resources
                  </Link>
                </div>
              </div>
            </nav>
          </aside>

          {/* Main Content */}
          <main className="flex-1">
            {children}
          </main>
        </div>
      </body>
    </html>
  );
}
