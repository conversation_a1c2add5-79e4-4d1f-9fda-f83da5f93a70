// Service Client Implementations for Inter-Service Communication
import { ServiceClient } from './auth';
// Admin Service Client
export class AdminServiceClient extends ServiceClient {
    constructor(baseUrl, serviceName, apiKey) {
        super(baseUrl, serviceName, apiKey);
    }
    // Payment Management
    async getPayments(page = 1, limit = 20) {
        return this.get(`/api/payments?page=${page}&limit=${limit}`);
    }
    async createPayment(data) {
        return this.post('/api/payments', data);
    }
    async verifyPayment(paymentId) {
        return this.post(`/api/payments/${paymentId}/verify`, {});
    }
    // User Management
    async getUsers(serviceName) {
        const query = serviceName ? `?service=${serviceName}` : '';
        return this.get(`/api/users${query}`);
    }
    async createUser(userData) {
        return this.post('/api/users', userData);
    }
    async updateUser(userId, userData) {
        return this.put(`/api/users/${userId}`, userData);
    }
    // System Configuration
    async getConfig(key) {
        const query = key ? `?key=${key}` : '';
        return this.get(`/api/config${query}`);
    }
    async updateConfig(key, value) {
        return this.put('/api/config', { key, value });
    }
}
// Staff Service Client
export class StaffServiceClient extends ServiceClient {
    constructor(baseUrl, serviceName, apiKey) {
        super(baseUrl, serviceName, apiKey);
    }
    // Lead Management
    async getLeads(status, page = 1, limit = 20) {
        const query = new URLSearchParams({ page: page.toString(), limit: limit.toString() });
        if (status)
            query.append('status', status);
        return this.get(`/api/leads?${query}`);
    }
    async createLead(data) {
        return this.post('/api/leads', data);
    }
    async convertLead(leadId, studentData) {
        return this.post(`/api/leads/${leadId}/convert`, studentData);
    }
    // Course Management
    async getCourses(page = 1, limit = 20) {
        return this.get(`/api/courses?page=${page}&limit=${limit}`);
    }
    async getCourse(courseId) {
        return this.get(`/api/courses/${courseId}`);
    }
    async createCourse(courseData) {
        return this.post('/api/courses', courseData);
    }
    // Group Management
    async getGroups(courseId) {
        const query = courseId ? `?courseId=${courseId}` : '';
        return this.get(`/api/groups${query}`);
    }
    async createGroup(groupData) {
        return this.post('/api/groups', groupData);
    }
    async enrollStudent(groupId, studentId) {
        return this.post(`/api/groups/${groupId}/enroll`, { studentId });
    }
    // Assignment Management
    async getAssignments(studentId) {
        const query = studentId ? `?studentId=${studentId}` : '';
        return this.get(`/api/assignments${query}`);
    }
    async createAssignment(assignmentData) {
        return this.post('/api/assignments', assignmentData);
    }
    // Resource Management
    async getResources(courseId, groupId) {
        const query = new URLSearchParams();
        if (courseId)
            query.append('courseId', courseId);
        if (groupId)
            query.append('groupId', groupId);
        return this.get(`/api/resources?${query}`);
    }
    async createResource(resourceData) {
        return this.post('/api/resources', resourceData);
    }
    // Teacher Management
    async getTeachers() {
        return this.get('/api/teachers');
    }
    async getTeacherKPIs(teacherId) {
        return this.get(`/api/teachers/${teacherId}/kpis`);
    }
}
// Student Service Client
export class StudentServiceClient extends ServiceClient {
    constructor(baseUrl, serviceName, apiKey) {
        super(baseUrl, serviceName, apiKey);
    }
    // Student Management
    async getStudent(studentId) {
        return this.get(`/api/students/${studentId}`);
    }
    async createStudent(studentData) {
        return this.post('/api/students', studentData);
    }
    async updateStudent(studentId, studentData) {
        return this.put(`/api/students/${studentId}`, studentData);
    }
    // Progress Management
    async getProgress(studentId) {
        return this.get(`/api/progress?studentId=${studentId}`);
    }
    async updateProgress(progressData) {
        return this.post('/api/progress', progressData);
    }
    // Assignment Submissions
    async getSubmissions(studentId) {
        return this.get(`/api/submissions?studentId=${studentId}`);
    }
    async submitAssignment(submissionData) {
        return this.post('/api/submissions', submissionData);
    }
    // Schedule Management
    async getSchedule(studentId) {
        return this.get(`/api/schedule?studentId=${studentId}`);
    }
    async syncSchedule(studentId) {
        return this.post('/api/schedule/sync', { studentId });
    }
    // Resource Access
    async trackResourceAccess(studentId, resourceId) {
        return this.post('/api/resources/access', { studentId, resourceId });
    }
}
// Service Client Factory
export class ServiceClientFactory {
    constructor(config) {
        this.config = config;
    }
    getAdminClient() {
        if (!this.adminClient) {
            this.adminClient = new AdminServiceClient(this.config.adminServiceUrl, this.config.serviceName, this.config.apiKey);
        }
        return this.adminClient;
    }
    getStaffClient() {
        if (!this.staffClient) {
            this.staffClient = new StaffServiceClient(this.config.staffServiceUrl, this.config.serviceName, this.config.apiKey);
        }
        return this.staffClient;
    }
    getStudentClient() {
        if (!this.studentClient) {
            this.studentClient = new StudentServiceClient(this.config.studentServiceUrl, this.config.serviceName, this.config.apiKey);
        }
        return this.studentClient;
    }
}
