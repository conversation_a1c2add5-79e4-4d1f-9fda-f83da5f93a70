import { Users, BookOpen, GraduationCap, UserCheck, FileText, TrendingUp, Calendar, Target } from "lucide-react";

export default function StaffDashboard() {
  return (
    <div className="p-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Staff Dashboard</h1>
        <p className="text-gray-600">Lead management, course catalog, and teacher administration</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Leads</p>
              <p className="text-2xl font-bold text-gray-900">0</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <BookOpen className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Courses</p>
              <p className="text-2xl font-bold text-gray-900">0</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <GraduationCap className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Groups</p>
              <p className="text-2xl font-bold text-gray-900">0</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <UserCheck className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Teachers</p>
              <p className="text-2xl font-bold text-gray-900">0</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Features Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
        {/* Lead Management */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <Users className="h-5 w-5 mr-2 text-blue-600" />
              Lead Management
            </h2>
            <p className="text-gray-600 text-sm mt-1">Track and convert potential students</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">New Leads</div>
                <div className="text-sm text-gray-600">View and assign new leads</div>
              </button>
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Lead Activities</div>
                <div className="text-sm text-gray-600">Track follow-ups and interactions</div>
              </button>
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Conversion Pipeline</div>
                <div className="text-sm text-gray-600">Monitor lead conversion progress</div>
              </button>
            </div>
          </div>
        </div>

        {/* Course Management */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <BookOpen className="h-5 w-5 mr-2 text-green-600" />
              Course Catalog
            </h2>
            <p className="text-gray-600 text-sm mt-1">Manage courses and curriculum</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Create Course</div>
                <div className="text-sm text-gray-600">Add new courses to catalog</div>
              </button>
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Course Levels</div>
                <div className="text-sm text-gray-600">Manage course difficulty levels</div>
              </button>
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Pricing & Duration</div>
                <div className="text-sm text-gray-600">Set course fees and schedules</div>
              </button>
            </div>
          </div>
        </div>

        {/* Group Management */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <GraduationCap className="h-5 w-5 mr-2 text-purple-600" />
              Group Management
            </h2>
            <p className="text-gray-600 text-sm mt-1">Create and manage student groups</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Create Group</div>
                <div className="text-sm text-gray-600">Form new student groups</div>
              </button>
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Assign Teachers</div>
                <div className="text-sm text-gray-600">Match teachers to groups</div>
              </button>
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Schedule Classes</div>
                <div className="text-sm text-gray-600">Set class times and rooms</div>
              </button>
            </div>
          </div>
        </div>

        {/* Teacher Management */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <UserCheck className="h-5 w-5 mr-2 text-orange-600" />
              Teacher Management
            </h2>
            <p className="text-gray-600 text-sm mt-1">Manage teacher profiles and KPIs</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Teacher Profiles</div>
                <div className="text-sm text-gray-600">View teacher information</div>
              </button>
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">KPI Tracking</div>
                <div className="text-sm text-gray-600">Monitor performance metrics</div>
              </button>
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Performance Reviews</div>
                <div className="text-sm text-gray-600">Conduct teacher evaluations</div>
              </button>
            </div>
          </div>
        </div>

        {/* Assignment Management */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <FileText className="h-5 w-5 mr-2 text-red-600" />
              Assignment Management
            </h2>
            <p className="text-gray-600 text-sm mt-1">Create and track student assignments</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Create Assignment</div>
                <div className="text-sm text-gray-600">Assign tasks to students</div>
              </button>
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Track Progress</div>
                <div className="text-sm text-gray-600">Monitor completion status</div>
              </button>
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Grade Assignments</div>
                <div className="text-sm text-gray-600">Review and grade submissions</div>
              </button>
            </div>
          </div>
        </div>

        {/* Resource Management */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <Target className="h-5 w-5 mr-2 text-indigo-600" />
              Resource Management
            </h2>
            <p className="text-gray-600 text-sm mt-1">Manage learning materials and resources</p>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Upload Resources</div>
                <div className="text-sm text-gray-600">Add books, documents, and materials</div>
              </button>
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Organize by Course</div>
                <div className="text-sm text-gray-600">Categorize learning materials</div>
              </button>
              <button className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="font-medium text-gray-900">Access Control</div>
                <div className="text-sm text-gray-600">Manage resource permissions</div>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="mt-8">
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
              Recent Activity
            </h2>
          </div>
          <div className="p-6">
            <div className="text-center text-gray-500 py-8">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No recent activity to display</p>
              <p className="text-sm">Activity will appear here as you use the system</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
