import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { convertLeadToStudent } from '@/lib/service-clients';
import { z } from 'zod';

// Validation schema for lead conversion
const convertLeadSchema = z.object({
  dateOfBirth: z.string().optional().transform((str) => str ? new Date(str) : undefined),
  emergencyContact: z.record(z.any()).optional(),
  currentLevel: z.string().optional(),
});

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const leadId = params.id;
    const body = await request.json();
    const validatedData = convertLeadSchema.parse(body);

    // Get the lead
    const lead = await prisma.lead.findUnique({
      where: { id: leadId },
      include: {
        assignedToUser: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    if (!lead) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'LEAD_NOT_FOUND',
          message: 'Lead not found',
        },
      }, { status: 404 });
    }

    if (lead.status === 'converted') {
      return NextResponse.json({
        success: false,
        error: {
          code: 'LEAD_ALREADY_CONVERTED',
          message: 'Lead has already been converted',
        },
      }, { status: 400 });
    }

    if (!lead.email) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'MISSING_EMAIL',
          message: 'Lead must have an email address to be converted',
        },
      }, { status: 400 });
    }

    try {
      // Convert lead to student using inter-service communication
      const student = await convertLeadToStudent({
        email: lead.email,
        firstName: lead.firstName,
        lastName: lead.lastName,
        dateOfBirth: validatedData.dateOfBirth,
        emergencyContact: validatedData.emergencyContact,
        currentLevel: validatedData.currentLevel || 'beginner',
      });

      // Update lead status
      const updatedLead = await prisma.lead.update({
        where: { id: leadId },
        data: {
          status: 'converted',
          convertedToStudentId: student.id,
          conversionDate: new Date(),
        },
        include: {
          assignedToUser: {
            select: {
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
      });

      // Create conversion activity
      await prisma.leadActivity.create({
        data: {
          leadId,
          activityType: 'follow_up',
          description: `Lead converted to student (ID: ${student.studentId})`,
          performedBy: lead.assignedTo || 'system',
          completedAt: new Date(),
        },
      });

      return NextResponse.json({
        success: true,
        data: {
          lead: updatedLead,
          student: student,
          message: 'Lead successfully converted to student',
        },
      }, { status: 200 });

    } catch (conversionError) {
      console.error('Error during lead conversion:', conversionError);
      
      // If conversion fails, create an activity log
      await prisma.leadActivity.create({
        data: {
          leadId,
          activityType: 'follow_up',
          description: `Lead conversion failed: ${conversionError instanceof Error ? conversionError.message : 'Unknown error'}`,
          performedBy: lead.assignedTo || 'system',
          completedAt: new Date(),
        },
      });

      return NextResponse.json({
        success: false,
        error: {
          code: 'CONVERSION_FAILED',
          message: 'Failed to convert lead to student',
          details: conversionError instanceof Error ? conversionError.message : 'Unknown error',
        },
      }, { status: 500 });
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid conversion data',
          details: error.errors,
        },
      }, { status: 400 });
    }

    console.error('Error converting lead:', error);
    return NextResponse.json({
      success: false,
      error: {
        code: 'CONVERT_LEAD_ERROR',
        message: 'Failed to convert lead',
      },
    }, { status: 500 });
  }
}
