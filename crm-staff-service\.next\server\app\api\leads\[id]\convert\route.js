(()=>{var e={};e.id=359,e.ids=[359],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1678:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});let s=require("@prisma/client"),a=globalThis.prisma??new s.PrismaClient({log:["query","error","warn"]})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4182:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>N,workUnitAsyncStorage:()=>E});var s={};r.r(s),r.d(s,{POST:()=>f});var a=r(6559),n=r(8088),o=r(7719),i=r(2190),c=r(1678);!function(){var e=Error("Cannot find module 'crm-shared-types'");throw e.code="MODULE_NOT_FOUND",e}();let d=Object(function(){var e=Error("Cannot find module 'crm-shared-types'");throw e.code="MODULE_NOT_FOUND",e}())({adminServiceUrl:process.env.ADMIN_SERVICE_URL||"http://localhost:3001",staffServiceUrl:process.env.STAFF_SERVICE_URL||"http://localhost:3002",studentServiceUrl:process.env.STUDENT_SERVICE_URL||"http://localhost:3003",serviceName:"crm-staff-service",apiKey:process.env.SERVICE_API_KEY||"default-api-key"}),l=d.getAdminClient(),u=d.getStudentClient();async function p(e){try{let t=await u.createStudent({email:e.email,firstName:e.firstName,lastName:e.lastName,dateOfBirth:e.dateOfBirth,emergencyContact:e.emergencyContact,enrollmentDate:new Date,currentLevel:e.currentLevel||"beginner"});if(!t.success)throw Error("Failed to create student");return await l.createUser({serviceName:"crm-student-service",serviceUserId:t.data.studentUserId,email:e.email,role:"student",isActive:!0}),t.data}catch(e){throw console.error("Error converting lead to student:",e),e}}var m=r(5697);let v=m.z.object({dateOfBirth:m.z.string().optional().transform(e=>e?new Date(e):void 0),emergencyContact:m.z.record(m.z.any()).optional(),currentLevel:m.z.string().optional()});async function f(e,{params:t}){try{let r=t.id,s=await e.json(),a=v.parse(s),n=await c.z.lead.findUnique({where:{id:r},include:{assignedToUser:{select:{firstName:!0,lastName:!0,email:!0}}}});if(!n)return i.NextResponse.json({success:!1,error:{code:"LEAD_NOT_FOUND",message:"Lead not found"}},{status:404});if("converted"===n.status)return i.NextResponse.json({success:!1,error:{code:"LEAD_ALREADY_CONVERTED",message:"Lead has already been converted"}},{status:400});if(!n.email)return i.NextResponse.json({success:!1,error:{code:"MISSING_EMAIL",message:"Lead must have an email address to be converted"}},{status:400});try{let e=await p({email:n.email,firstName:n.firstName,lastName:n.lastName,dateOfBirth:a.dateOfBirth,emergencyContact:a.emergencyContact,currentLevel:a.currentLevel||"beginner"}),t=await c.z.lead.update({where:{id:r},data:{status:"converted",convertedToStudentId:e.id,conversionDate:new Date},include:{assignedToUser:{select:{firstName:!0,lastName:!0,email:!0}}}});return await c.z.leadActivity.create({data:{leadId:r,activityType:"follow_up",description:`Lead converted to student (ID: ${e.studentId})`,performedBy:n.assignedTo||"system",completedAt:new Date}}),i.NextResponse.json({success:!0,data:{lead:t,student:e,message:"Lead successfully converted to student"}},{status:200})}catch(e){return console.error("Error during lead conversion:",e),await c.z.leadActivity.create({data:{leadId:r,activityType:"follow_up",description:`Lead conversion failed: ${e instanceof Error?e.message:"Unknown error"}`,performedBy:n.assignedTo||"system",completedAt:new Date}}),i.NextResponse.json({success:!1,error:{code:"CONVERSION_FAILED",message:"Failed to convert lead to student",details:e instanceof Error?e.message:"Unknown error"}},{status:500})}}catch(e){if(e instanceof m.z.ZodError)return i.NextResponse.json({success:!1,error:{code:"VALIDATION_ERROR",message:"Invalid conversion data",details:e.errors}},{status:400});return console.error("Error converting lead:",e),i.NextResponse.json({success:!1,error:{code:"CONVERT_LEAD_ERROR",message:"Failed to convert lead"}},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/leads/[id]/convert/route",pathname:"/api/leads/[id]/convert",filename:"route",bundlePath:"app/api/leads/[id]/convert/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\leads\\[id]\\convert\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:N,workUnitAsyncStorage:E,serverHooks:y}=g;function h(){return(0,o.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:E})}},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580,697],()=>r(4182));module.exports=s})();