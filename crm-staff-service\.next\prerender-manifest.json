{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "29afa67eac418841c4c8ddae7348eb6c", "previewModeSigningKey": "ffaf36f6dec02d8bd9de334ff8e4c36d0845a857371aa5e6ba368c76462e76d8", "previewModeEncryptionKey": "bb8b1d6094231d9a0571dc5c566a0b4401ce9a4ed334c3b5d3da41651286cc83"}}