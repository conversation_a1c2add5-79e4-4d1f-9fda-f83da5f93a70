export interface ServiceAuthConfig {
    serviceName: string;
    apiKey: string;
    allowedServices: string[];
}
export interface ServiceAuthRequest {
    serviceName: string;
    timestamp: number;
    signature: string;
}
export interface AuthenticatedRequest extends Request {
    serviceAuth?: {
        serviceName: string;
        isAuthenticated: boolean;
    };
}
export declare function generateServiceSignature(serviceName: string, apiKey: string, timestamp: number): string;
export declare function verifyServiceSignature(serviceName: string, apiKey: string, timestamp: number, signature: string): boolean;
export declare function createServiceAuthHeaders(serviceName: string, apiKey: string): Record<string, string>;
export declare function createServiceAuthMiddleware(config: ServiceAuthConfig): (request: Request) => AuthenticatedRequest;
export declare class ServiceClient {
    private baseUrl;
    private serviceName;
    private apiKey;
    constructor(baseUrl: string, serviceName: string, apiKey: string);
    private getAuthHeaders;
    get<T>(endpoint: string): Promise<T>;
    post<T>(endpoint: string, data: any): Promise<T>;
    put<T>(endpoint: string, data: any): Promise<T>;
    delete<T>(endpoint: string): Promise<T>;
}
//# sourceMappingURL=auth.d.ts.map