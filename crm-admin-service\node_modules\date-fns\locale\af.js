import { formatDistance } from "./af/_lib/formatDistance.js";
import { formatLong } from "./af/_lib/formatLong.js";
import { formatRelative } from "./af/_lib/formatRelative.js";
import { localize } from "./af/_lib/localize.js";
import { match } from "./af/_lib/match.js";

/**
 * @category Locales
 * @summary Afrikaans locale.
 * @language Afrikaans
 * @iso-639-2 afr
 * <AUTHOR> Weststrate [@marnusw](https://github.com/marnusw)
 */
export const af = {
  code: "af",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default af;
