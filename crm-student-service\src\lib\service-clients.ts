// Service Clients Configuration for Student Service

import { ServiceClientFactory, AdminServiceClient, StaffServiceClient } from 'crm-shared-types';

// Service client factory instance
const serviceClientFactory = new ServiceClientFactory({
  adminServiceUrl: process.env.ADMIN_SERVICE_URL || 'http://localhost:3001',
  staffServiceUrl: process.env.STAFF_SERVICE_URL || 'http://localhost:3002',
  studentServiceUrl: process.env.STUDENT_SERVICE_URL || 'http://localhost:3003',
  serviceName: 'crm-student-service',
  apiKey: process.env.SERVICE_API_KEY || 'default-api-key',
});

// Export service clients
export const adminServiceClient = serviceClientFactory.getAdminClient();
export const staffServiceClient = serviceClientFactory.getStaffClient();

// Helper functions for common operations

// Get assignments from staff service
export async function getStudentAssignments(studentId: string) {
  try {
    const response = await staffServiceClient.getAssignments(studentId);
    return response.data;
  } catch (error) {
    console.error('Error getting student assignments:', error);
    throw error;
  }
}

// Get resources from staff service
export async function getStudentResources(courseId?: string, groupId?: string) {
  try {
    const response = await staffServiceClient.getResources(courseId, groupId);
    return response.data;
  } catch (error) {
    console.error('Error getting student resources:', error);
    throw error;
  }
}

// Get course information from staff service
export async function getCourseInfo(courseId: string) {
  try {
    const response = await staffServiceClient.getCourse(courseId);
    return response.data;
  } catch (error) {
    console.error('Error getting course info:', error);
    throw error;
  }
}

// Get group information from staff service
export async function getGroupInfo(courseId?: string) {
  try {
    const response = await staffServiceClient.getGroups(courseId);
    return response.data;
  } catch (error) {
    console.error('Error getting group info:', error);
    throw error;
  }
}

// Sync schedule data from staff service
export async function syncScheduleFromStaff(studentId: string) {
  try {
    // Get groups for the student from staff service
    const groups = await staffServiceClient.getGroups();
    
    // Filter groups that include this student (this would need proper filtering in staff service)
    // For now, we'll return all groups as example
    return groups.data;
  } catch (error) {
    console.error('Error syncing schedule from staff:', error);
    throw error;
  }
}

// Notify admin service of payment-related activities
export async function notifyPaymentActivity(studentId: string, activityData: any) {
  try {
    // This could be used to notify admin service when student completes courses
    // or other payment-related milestones
    const response = await adminServiceClient.createPayment({
      studentId,
      amount: activityData.amount,
      paymentDate: new Date(),
      paymentMethod: 'system',
      description: activityData.description,
      notes: 'Automated system notification',
    });
    
    return response;
  } catch (error) {
    console.error('Error notifying payment activity:', error);
    throw error;
  }
}
