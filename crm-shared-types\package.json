{"name": "crm-shared-types", "version": "1.0.0", "description": "Shared TypeScript types and interfaces for Innovative Centre CRM", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "exports": {".": {"import": "./dist/src/index.js", "require": "./dist/src/index.js", "types": "./dist/src/index.d.ts"}}, "files": ["dist/**/*"], "scripts": {"dev": "next dev", "build": "next build && npm run build:types", "build:types": "tsc --outDir dist", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.4"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.4", "@eslint/eslintrc": "^3"}, "keywords": ["crm", "typescript", "types", "interfaces", "education", "innovative-centre"], "author": "Innovative Centre", "license": "MIT"}