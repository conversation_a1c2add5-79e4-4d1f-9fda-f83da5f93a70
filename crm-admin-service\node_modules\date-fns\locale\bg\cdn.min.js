(()=>{var M;function $(B,G){var J=Object.keys(B);if(Object.getOwnPropertySymbols){var X=Object.getOwnPropertySymbols(B);G&&(X=X.filter(function(Z){return Object.getOwnPropertyDescriptor(B,Z).enumerable})),J.push.apply(J,X)}return J}function I(B){for(var G=1;G<arguments.length;G++){var J=arguments[G]!=null?arguments[G]:{};G%2?$(Object(J),!0).forEach(function(X){v(B,X,J[X])}):Object.getOwnPropertyDescriptors?Object.defineProperties(B,Object.getOwnPropertyDescriptors(J)):$(Object(J)).forEach(function(X){Object.defineProperty(B,X,Object.getOwnPropertyDescriptor(J,X))})}return B}function v(B,G,J){if(G=w(G),G in B)Object.defineProperty(B,G,{value:J,enumerable:!0,configurable:!0,writable:!0});else B[G]=J;return B}function w(B){var G=D(B,"string");return K(G)=="symbol"?G:String(G)}function D(B,G){if(K(B)!="object"||!B)return B;var J=B[Symbol.toPrimitive];if(J!==void 0){var X=J.call(B,G||"default");if(K(X)!="object")return X;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(B)}function h(B,G){return _(B)||f(B,G)||k(B,G)||b()}function b(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function k(B,G){if(!B)return;if(typeof B==="string")return j(B,G);var J=Object.prototype.toString.call(B).slice(8,-1);if(J==="Object"&&B.constructor)J=B.constructor.name;if(J==="Map"||J==="Set")return Array.from(B);if(J==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(J))return j(B,G)}function j(B,G){if(G==null||G>B.length)G=B.length;for(var J=0,X=new Array(G);J<G;J++)X[J]=B[J];return X}function f(B,G){var J=B==null?null:typeof Symbol!="undefined"&&B[Symbol.iterator]||B["@@iterator"];if(J!=null){var X,Z,C,U,H=[],Y=!0,Q=!1;try{if(C=(J=J.call(B)).next,G===0){if(Object(J)!==J)return;Y=!1}else for(;!(Y=(X=C.call(J)).done)&&(H.push(X.value),H.length!==G);Y=!0);}catch(q){Q=!0,Z=q}finally{try{if(!Y&&J.return!=null&&(U=J.return(),Object(U)!==U))return}finally{if(Q)throw Z}}return H}}function _(B){if(Array.isArray(B))return B}function K(B){return K=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},K(B)}var g=Object.defineProperty,wB=function B(G,J){for(var X in J)g(G,X,{get:J[X],enumerable:!0,configurable:!0,set:function Z(C){return J[X]=function(){return C}}})},c={lessThanXSeconds:{one:"\u043F\u043E-\u043C\u0430\u043B\u043A\u043E \u043E\u0442 \u0441\u0435\u043A\u0443\u043D\u0434\u0430",other:"\u043F\u043E-\u043C\u0430\u043B\u043A\u043E \u043E\u0442 {{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438"},xSeconds:{one:"1 \u0441\u0435\u043A\u0443\u043D\u0434\u0430",other:"{{count}} \u0441\u0435\u043A\u0443\u043D\u0434\u0438"},halfAMinute:"\u043F\u043E\u043B\u043E\u0432\u0438\u043D \u043C\u0438\u043D\u0443\u0442\u0430",lessThanXMinutes:{one:"\u043F\u043E-\u043C\u0430\u043B\u043A\u043E \u043E\u0442 \u043C\u0438\u043D\u0443\u0442\u0430",other:"\u043F\u043E-\u043C\u0430\u043B\u043A\u043E \u043E\u0442 {{count}} \u043C\u0438\u043D\u0443\u0442\u0438"},xMinutes:{one:"1 \u043C\u0438\u043D\u0443\u0442\u0430",other:"{{count}} \u043C\u0438\u043D\u0443\u0442\u0438"},aboutXHours:{one:"\u043E\u043A\u043E\u043B\u043E \u0447\u0430\u0441",other:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0447\u0430\u0441\u0430"},xHours:{one:"1 \u0447\u0430\u0441",other:"{{count}} \u0447\u0430\u0441\u0430"},xDays:{one:"1 \u0434\u0435\u043D",other:"{{count}} \u0434\u043D\u0438"},aboutXWeeks:{one:"\u043E\u043A\u043E\u043B\u043E \u0441\u0435\u0434\u043C\u0438\u0446\u0430",other:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0441\u0435\u0434\u043C\u0438\u0446\u0438"},xWeeks:{one:"1 \u0441\u0435\u0434\u043C\u0438\u0446\u0430",other:"{{count}} \u0441\u0435\u0434\u043C\u0438\u0446\u0438"},aboutXMonths:{one:"\u043E\u043A\u043E\u043B\u043E \u043C\u0435\u0441\u0435\u0446",other:"\u043E\u043A\u043E\u043B\u043E {{count}} \u043C\u0435\u0441\u0435\u0446\u0430"},xMonths:{one:"1 \u043C\u0435\u0441\u0435\u0446",other:"{{count}} \u043C\u0435\u0441\u0435\u0446\u0430"},aboutXYears:{one:"\u043E\u043A\u043E\u043B\u043E \u0433\u043E\u0434\u0438\u043D\u0430",other:"\u043E\u043A\u043E\u043B\u043E {{count}} \u0433\u043E\u0434\u0438\u043D\u0438"},xYears:{one:"1 \u0433\u043E\u0434\u0438\u043D\u0430",other:"{{count}} \u0433\u043E\u0434\u0438\u043D\u0438"},overXYears:{one:"\u043D\u0430\u0434 \u0433\u043E\u0434\u0438\u043D\u0430",other:"\u043D\u0430\u0434 {{count}} \u0433\u043E\u0434\u0438\u043D\u0438"},almostXYears:{one:"\u043F\u043E\u0447\u0442\u0438 \u0433\u043E\u0434\u0438\u043D\u0430",other:"\u043F\u043E\u0447\u0442\u0438 {{count}} \u0433\u043E\u0434\u0438\u043D\u0438"}},m=function B(G,J,X){var Z,C=c[G];if(typeof C==="string")Z=C;else if(J===1)Z=C.one;else Z=C.other.replace("{{count}}",String(J));if(X!==null&&X!==void 0&&X.addSuffix)if(X.comparison&&X.comparison>0)return"\u0441\u043B\u0435\u0434 "+Z;else return"\u043F\u0440\u0435\u0434\u0438 "+Z;return Z};function z(B){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},J=G.width?String(G.width):B.defaultWidth,X=B.formats[J]||B.formats[B.defaultWidth];return X}}var y={full:"EEEE, dd MMMM yyyy",long:"dd MMMM yyyy",medium:"dd MMM yyyy",short:"dd.MM.yyyy"},p={full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"H:mm"},d={any:"{{date}} {{time}}"},l={date:z({formats:y,defaultWidth:"full"}),time:z({formats:p,defaultWidth:"full"}),dateTime:z({formats:d,defaultWidth:"any"})},DB=7,u=365.2425,i=Math.pow(10,8)*24*60*60*1000,hB=-i,bB=604800000,kB=86400000,fB=60000,_B=3600000,gB=1000,cB=525600,mB=43200,yB=1440,pB=60,dB=3,lB=12,uB=4,s=3600,iB=60,L=s*24,sB=L*7,n=L*u,r=n/12,nB=r*3,x=Symbol.for("constructDateFrom");function O(B,G){if(typeof B==="function")return B(G);if(B&&K(B)==="object"&&x in B)return B[x](G);if(B instanceof Date)return new B.constructor(G);return new Date(G)}function o(B){for(var G=arguments.length,J=new Array(G>1?G-1:0),X=1;X<G;X++)J[X-1]=arguments[X];var Z=O.bind(null,B||J.find(function(C){return K(C)==="object"}));return J.map(Z)}function a(){return P}function rB(B){P=B}var P={};function R(B,G){return O(G||B,B)}function S(B,G){var J,X,Z,C,U,H,Y=a(),Q=(J=(X=(Z=(C=G===null||G===void 0?void 0:G.weekStartsOn)!==null&&C!==void 0?C:G===null||G===void 0||(U=G.locale)===null||U===void 0||(U=U.options)===null||U===void 0?void 0:U.weekStartsOn)!==null&&Z!==void 0?Z:Y.weekStartsOn)!==null&&X!==void 0?X:(H=Y.locale)===null||H===void 0||(H=H.options)===null||H===void 0?void 0:H.weekStartsOn)!==null&&J!==void 0?J:0,q=R(B,G===null||G===void 0?void 0:G.in),E=q.getDay(),vB=(E<Q?7:0)+E-Q;return q.setDate(q.getDate()-vB),q.setHours(0,0,0,0),q}function W(B,G,J){var X=o(J===null||J===void 0?void 0:J.in,B,G),Z=h(X,2),C=Z[0],U=Z[1];return+S(C,J)===+S(U,J)}function t(B){var G=V[B];switch(B){case 0:case 3:case 6:return"'\u043C\u0438\u043D\u0430\u043B\u0430\u0442\u0430 "+G+" \u0432' p";case 1:case 2:case 4:case 5:return"'\u043C\u0438\u043D\u0430\u043B\u0438\u044F "+G+" \u0432' p"}}function F(B){var G=V[B];if(B===2)return"'\u0432\u044A\u0432 "+G+" \u0432' p";else return"'\u0432 "+G+" \u0432' p"}function e(B){var G=V[B];switch(B){case 0:case 3:case 6:return"'\u0441\u043B\u0435\u0434\u0432\u0430\u0449\u0430\u0442\u0430 "+G+" \u0432' p";case 1:case 2:case 4:case 5:return"'\u0441\u043B\u0435\u0434\u0432\u0430\u0449\u0438\u044F "+G+" \u0432' p"}}var V=["\u043D\u0435\u0434\u0435\u043B\u044F","\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u043D\u0438\u043A","\u0432\u0442\u043E\u0440\u043D\u0438\u043A","\u0441\u0440\u044F\u0434\u0430","\u0447\u0435\u0442\u0432\u044A\u0440\u0442\u044A\u043A","\u043F\u0435\u0442\u044A\u043A","\u0441\u044A\u0431\u043E\u0442\u0430"],BB=function B(G,J,X){var Z=R(G),C=Z.getDay();if(W(Z,J,X))return F(C);else return t(C)},GB=function B(G,J,X){var Z=R(G),C=Z.getDay();if(W(Z,J,X))return F(C);else return e(C)},JB={lastWeek:BB,yesterday:"'\u0432\u0447\u0435\u0440\u0430 \u0432' p",today:"'\u0434\u043D\u0435\u0441 \u0432' p",tomorrow:"'\u0443\u0442\u0440\u0435 \u0432' p",nextWeek:GB,other:"P"},XB=function B(G,J,X,Z){var C=JB[G];if(typeof C==="function")return C(J,X,Z);return C};function A(B){return function(G,J){var X=J!==null&&J!==void 0&&J.context?String(J.context):"standalone",Z;if(X==="formatting"&&B.formattingValues){var C=B.defaultFormattingWidth||B.defaultWidth,U=J!==null&&J!==void 0&&J.width?String(J.width):C;Z=B.formattingValues[U]||B.formattingValues[C]}else{var H=B.defaultWidth,Y=J!==null&&J!==void 0&&J.width?String(J.width):B.defaultWidth;Z=B.values[Y]||B.values[H]}var Q=B.argumentCallback?B.argumentCallback(G):G;return Z[Q]}}function ZB(B){return B==="year"||B==="week"||B==="minute"||B==="second"}function CB(B){return B==="quarter"}function N(B,G,J,X,Z){var C=CB(G)?Z:ZB(G)?X:J;return B+"-"+C}var UB={narrow:["\u043F\u0440.\u043D.\u0435.","\u043D.\u0435."],abbreviated:["\u043F\u0440\u0435\u0434\u0438 \u043D. \u0435.","\u043D. \u0435."],wide:["\u043F\u0440\u0435\u0434\u0438 \u043D\u043E\u0432\u0430\u0442\u0430 \u0435\u0440\u0430","\u043D\u043E\u0432\u0430\u0442\u0430 \u0435\u0440\u0430"]},HB={narrow:["1","2","3","4"],abbreviated:["1-\u0432\u043E \u0442\u0440\u0438\u043C\u0435\u0441.","2-\u0440\u043E \u0442\u0440\u0438\u043C\u0435\u0441.","3-\u0442\u043E \u0442\u0440\u0438\u043C\u0435\u0441.","4-\u0442\u043E \u0442\u0440\u0438\u043C\u0435\u0441."],wide:["1-\u0432\u043E \u0442\u0440\u0438\u043C\u0435\u0441\u0435\u0447\u0438\u0435","2-\u0440\u043E \u0442\u0440\u0438\u043C\u0435\u0441\u0435\u0447\u0438\u0435","3-\u0442\u043E \u0442\u0440\u0438\u043C\u0435\u0441\u0435\u0447\u0438\u0435","4-\u0442\u043E \u0442\u0440\u0438\u043C\u0435\u0441\u0435\u0447\u0438\u0435"]},QB={abbreviated:["\u044F\u043D\u0443","\u0444\u0435\u0432","\u043C\u0430\u0440","\u0430\u043F\u0440","\u043C\u0430\u0439","\u044E\u043D\u0438","\u044E\u043B\u0438","\u0430\u0432\u0433","\u0441\u0435\u043F","\u043E\u043A\u0442","\u043D\u043E\u0435","\u0434\u0435\u043A"],wide:["\u044F\u043D\u0443\u0430\u0440\u0438","\u0444\u0435\u0432\u0440\u0443\u0430\u0440\u0438","\u043C\u0430\u0440\u0442","\u0430\u043F\u0440\u0438\u043B","\u043C\u0430\u0439","\u044E\u043D\u0438","\u044E\u043B\u0438","\u0430\u0432\u0433\u0443\u0441\u0442","\u0441\u0435\u043F\u0442\u0435\u043C\u0432\u0440\u0438","\u043E\u043A\u0442\u043E\u043C\u0432\u0440\u0438","\u043D\u043E\u0435\u043C\u0432\u0440\u0438","\u0434\u0435\u043A\u0435\u043C\u0432\u0440\u0438"]},YB={narrow:["\u041D","\u041F","\u0412","\u0421","\u0427","\u041F","\u0421"],short:["\u043D\u0434","\u043F\u043D","\u0432\u0442","\u0441\u0440","\u0447\u0442","\u043F\u0442","\u0441\u0431"],abbreviated:["\u043D\u0435\u0434","\u043F\u043E\u043D","\u0432\u0442\u043E","\u0441\u0440\u044F","\u0447\u0435\u0442","\u043F\u0435\u0442","\u0441\u044A\u0431"],wide:["\u043D\u0435\u0434\u0435\u043B\u044F","\u043F\u043E\u043D\u0435\u0434\u0435\u043B\u043D\u0438\u043A","\u0432\u0442\u043E\u0440\u043D\u0438\u043A","\u0441\u0440\u044F\u0434\u0430","\u0447\u0435\u0442\u0432\u044A\u0440\u0442\u044A\u043A","\u043F\u0435\u0442\u044A\u043A","\u0441\u044A\u0431\u043E\u0442\u0430"]},qB={wide:{am:"\u043F\u0440\u0435\u0434\u0438 \u043E\u0431\u044F\u0434",pm:"\u0441\u043B\u0435\u0434 \u043E\u0431\u044F\u0434",midnight:"\u0432 \u043F\u043E\u043B\u0443\u043D\u043E\u0449",noon:"\u043D\u0430 \u043E\u0431\u044F\u0434",morning:"\u0441\u0443\u0442\u0440\u0438\u043D\u0442\u0430",afternoon:"\u0441\u043B\u0435\u0434\u043E\u0431\u0435\u0434",evening:"\u0432\u0435\u0447\u0435\u0440\u0442\u0430",night:"\u043F\u0440\u0435\u0437 \u043D\u043E\u0449\u0442\u0430"}},KB=function B(G,J){var X=Number(G),Z=J===null||J===void 0?void 0:J.unit;if(X===0)return N(0,Z,"\u0435\u0432","\u0435\u0432\u0430","\u0435\u0432\u043E");else if(X%1000===0)return N(X,Z,"\u0435\u043D","\u043D\u0430","\u043D\u043E");else if(X%100===0)return N(X,Z,"\u0442\u0435\u043D","\u0442\u043D\u0430","\u0442\u043D\u043E");var C=X%100;if(C>20||C<10)switch(C%10){case 1:return N(X,Z,"\u0432\u0438","\u0432\u0430","\u0432\u043E");case 2:return N(X,Z,"\u0440\u0438","\u0440\u0430","\u0440\u043E");case 7:case 8:return N(X,Z,"\u043C\u0438","\u043C\u0430","\u043C\u043E")}return N(X,Z,"\u0442\u0438","\u0442\u0430","\u0442\u043E")},NB={ordinalNumber:KB,era:A({values:UB,defaultWidth:"wide"}),quarter:A({values:HB,defaultWidth:"wide",argumentCallback:function B(G){return G-1}}),month:A({values:QB,defaultWidth:"wide"}),day:A({values:YB,defaultWidth:"wide"}),dayPeriod:A({values:qB,defaultWidth:"wide"})};function T(B){return function(G){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=J.width,Z=X&&B.matchPatterns[X]||B.matchPatterns[B.defaultMatchWidth],C=G.match(Z);if(!C)return null;var U=C[0],H=X&&B.parsePatterns[X]||B.parsePatterns[B.defaultParseWidth],Y=Array.isArray(H)?AB(H,function(E){return E.test(U)}):EB(H,function(E){return E.test(U)}),Q;Q=B.valueCallback?B.valueCallback(Y):Y,Q=J.valueCallback?J.valueCallback(Q):Q;var q=G.slice(U.length);return{value:Q,rest:q}}}function EB(B,G){for(var J in B)if(Object.prototype.hasOwnProperty.call(B,J)&&G(B[J]))return J;return}function AB(B,G){for(var J=0;J<B.length;J++)if(G(B[J]))return J;return}function TB(B){return function(G){var J=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=G.match(B.matchPattern);if(!X)return null;var Z=X[0],C=G.match(B.parsePattern);if(!C)return null;var U=B.valueCallback?B.valueCallback(C[0]):C[0];U=J.valueCallback?J.valueCallback(U):U;var H=G.slice(Z.length);return{value:U,rest:H}}}var IB=/^(\d+)(-?[врмт][аи]|-?т?(ен|на)|-?(ев|ева))?/i,MB=/\d+/i,zB={narrow:/^((пр)?н\.?\s?е\.?)/i,abbreviated:/^((пр)?н\.?\s?е\.?)/i,wide:/^(преди новата ера|новата ера|нова ера)/i},RB={any:[/^п/i,/^н/i]},VB={narrow:/^[1234]/i,abbreviated:/^[1234](-?[врт]?o?)? тримес.?/i,wide:/^[1234](-?[врт]?о?)? тримесечие/i},$B={any:[/1/i,/2/i,/3/i,/4/i]},jB={narrow:/^[нпвсч]/i,short:/^(нд|пн|вт|ср|чт|пт|сб)/i,abbreviated:/^(нед|пон|вто|сря|чет|пет|съб)/i,wide:/^(неделя|понеделник|вторник|сряда|четвъртък|петък|събота)/i},LB={narrow:[/^н/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н[ед]/i,/^п[он]/i,/^вт/i,/^ср/i,/^ч[ет]/i,/^п[ет]/i,/^с[ъб]/i]},xB={abbreviated:/^(яну|фев|мар|апр|май|юни|юли|авг|сеп|окт|ное|дек)/i,wide:/^(януари|февруари|март|април|май|юни|юли|август|септември|октомври|ноември|декември)/i},OB={any:[/^я/i,/^ф/i,/^мар/i,/^ап/i,/^май/i,/^юн/i,/^юл/i,/^ав/i,/^се/i,/^окт/i,/^но/i,/^де/i]},PB={any:/^(преди о|след о|в по|на о|през|веч|сут|следо)/i},SB={any:{am:/^преди о/i,pm:/^след о/i,midnight:/^в пол/i,noon:/^на об/i,morning:/^сут/i,afternoon:/^следо/i,evening:/^веч/i,night:/^през н/i}},WB={ordinalNumber:TB({matchPattern:IB,parsePattern:MB,valueCallback:function B(G){return parseInt(G,10)}}),era:T({matchPatterns:zB,defaultMatchWidth:"wide",parsePatterns:RB,defaultParseWidth:"any"}),quarter:T({matchPatterns:VB,defaultMatchWidth:"wide",parsePatterns:$B,defaultParseWidth:"any",valueCallback:function B(G){return G+1}}),month:T({matchPatterns:xB,defaultMatchWidth:"wide",parsePatterns:OB,defaultParseWidth:"any"}),day:T({matchPatterns:jB,defaultMatchWidth:"wide",parsePatterns:LB,defaultParseWidth:"any"}),dayPeriod:T({matchPatterns:PB,defaultMatchWidth:"any",parsePatterns:SB,defaultParseWidth:"any"})},FB={code:"bg",formatDistance:m,formatLong:l,formatRelative:XB,localize:NB,match:WB,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=I(I({},window.dateFns),{},{locale:I(I({},(M=window.dateFns)===null||M===void 0?void 0:M.locale),{},{bg:FB})})})();

//# debugId=8F78B1C189CFC93D64756E2164756E21
