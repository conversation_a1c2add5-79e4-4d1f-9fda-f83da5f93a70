[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\index.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\auth.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\service-clients.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\assignments.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\courses.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\index.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\kpis.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\leads.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\payments.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\system.ts": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\users.ts": "13"}, {"size": 393, "mtime": 1750448012916, "results": "14", "hashOfConfig": "15"}, {"size": 776, "mtime": 1750448088581, "results": "16", "hashOfConfig": "15"}, {"size": 1114, "mtime": 1750479321127, "results": "17", "hashOfConfig": "15"}, {"size": 4879, "mtime": 1750479424984, "results": "18", "hashOfConfig": "15"}, {"size": 7447, "mtime": 1750479309183, "results": "19", "hashOfConfig": "15"}, {"size": 3179, "mtime": 1750448143049, "results": "20", "hashOfConfig": "15"}, {"size": 2933, "mtime": 1750448126681, "results": "21", "hashOfConfig": "15"}, {"size": 2377, "mtime": 1750448214501, "results": "22", "hashOfConfig": "15"}, {"size": 3025, "mtime": 1750448178915, "results": "23", "hashOfConfig": "15"}, {"size": 1877, "mtime": 1750448112529, "results": "24", "hashOfConfig": "15"}, {"size": 3402, "mtime": 1750448160746, "results": "25", "hashOfConfig": "15"}, {"size": 3685, "mtime": 1750448199461, "results": "26", "hashOfConfig": "15"}, {"size": 2157, "mtime": 1750448101387, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "197hkse", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 30, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\auth.ts", ["67", "68"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\lib\\service-clients.ts", ["69", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\assignments.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\courses.ts", ["99", "100", "101", "102", "103"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\index.ts", ["104"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\kpis.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\leads.ts", ["105"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\payments.ts", ["106", "107"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\system.ts", ["108", "109", "110", "111", "112", "113", "114", "115"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-shared-types\\src\\types\\users.ts", ["116", "117", "118"], [], {"ruleId": "119", "severity": 2, "message": "120", "line": 139, "column": 41, "nodeType": "121", "messageId": "122", "endLine": 139, "endColumn": 44, "suggestions": "123"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 153, "column": 40, "nodeType": "121", "messageId": "122", "endLine": 153, "endColumn": 43, "suggestions": "124"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 38, "column": 61, "nodeType": "121", "messageId": "122", "endLine": 38, "endColumn": 64, "suggestions": "125"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 43, "column": 30, "nodeType": "121", "messageId": "122", "endLine": 43, "endColumn": 33, "suggestions": "126"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 43, "column": 56, "nodeType": "121", "messageId": "122", "endLine": 43, "endColumn": 59, "suggestions": "127"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 47, "column": 46, "nodeType": "121", "messageId": "122", "endLine": 47, "endColumn": 49, "suggestions": "128"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 47, "column": 72, "nodeType": "121", "messageId": "122", "endLine": 47, "endColumn": 75, "suggestions": "129"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 52, "column": 54, "nodeType": "121", "messageId": "122", "endLine": 52, "endColumn": 57, "suggestions": "130"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 57, "column": 42, "nodeType": "121", "messageId": "122", "endLine": 57, "endColumn": 45, "suggestions": "131"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 57, "column": 68, "nodeType": "121", "messageId": "122", "endLine": 57, "endColumn": 71, "suggestions": "132"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 79, "column": 50, "nodeType": "121", "messageId": "122", "endLine": 79, "endColumn": 53, "suggestions": "133"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 79, "column": 76, "nodeType": "121", "messageId": "122", "endLine": 79, "endColumn": 79, "suggestions": "134"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 92, "column": 34, "nodeType": "121", "messageId": "122", "endLine": 92, "endColumn": 37, "suggestions": "135"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 102, "column": 32, "nodeType": "121", "messageId": "122", "endLine": 102, "endColumn": 35, "suggestions": "136"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 106, "column": 80, "nodeType": "121", "messageId": "122", "endLine": 106, "endColumn": 83, "suggestions": "137"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 111, "column": 65, "nodeType": "121", "messageId": "122", "endLine": 111, "endColumn": 68, "suggestions": "138"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 116, "column": 42, "nodeType": "121", "messageId": "122", "endLine": 116, "endColumn": 45, "suggestions": "139"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 116, "column": 68, "nodeType": "121", "messageId": "122", "endLine": 116, "endColumn": 71, "suggestions": "140"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 121, "column": 80, "nodeType": "121", "messageId": "122", "endLine": 121, "endColumn": 83, "suggestions": "141"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 128, "column": 38, "nodeType": "121", "messageId": "122", "endLine": 128, "endColumn": 41, "suggestions": "142"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 128, "column": 64, "nodeType": "121", "messageId": "122", "endLine": 128, "endColumn": 67, "suggestions": "143"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 133, "column": 44, "nodeType": "121", "messageId": "122", "endLine": 133, "endColumn": 47, "suggestions": "144"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 137, "column": 64, "nodeType": "121", "messageId": "122", "endLine": 137, "endColumn": 67, "suggestions": "145"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 153, "column": 36, "nodeType": "121", "messageId": "122", "endLine": 153, "endColumn": 39, "suggestions": "146"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 157, "column": 55, "nodeType": "121", "messageId": "122", "endLine": 157, "endColumn": 58, "suggestions": "147"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 162, "column": 61, "nodeType": "121", "messageId": "122", "endLine": 162, "endColumn": 64, "suggestions": "148"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 166, "column": 38, "nodeType": "121", "messageId": "122", "endLine": 166, "endColumn": 41, "suggestions": "149"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 171, "column": 64, "nodeType": "121", "messageId": "122", "endLine": 171, "endColumn": 67, "suggestions": "150"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 175, "column": 42, "nodeType": "121", "messageId": "122", "endLine": 175, "endColumn": 45, "suggestions": "151"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 175, "column": 68, "nodeType": "121", "messageId": "122", "endLine": 175, "endColumn": 71, "suggestions": "152"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 184, "column": 62, "nodeType": "121", "messageId": "122", "endLine": 184, "endColumn": 65, "suggestions": "153"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 189, "column": 89, "nodeType": "121", "messageId": "122", "endLine": 189, "endColumn": 92, "suggestions": "154"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 34, "column": 28, "nodeType": "121", "messageId": "122", "endLine": 34, "endColumn": 31, "suggestions": "155"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 47, "column": 30, "nodeType": "121", "messageId": "122", "endLine": 47, "endColumn": 33, "suggestions": "156"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 96, "column": 28, "nodeType": "121", "messageId": "122", "endLine": 96, "endColumn": 31, "suggestions": "157"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 106, "column": 29, "nodeType": "121", "messageId": "122", "endLine": 106, "endColumn": 32, "suggestions": "158"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 114, "column": 30, "nodeType": "121", "messageId": "122", "endLine": 114, "endColumn": 33, "suggestions": "159"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 37, "column": 28, "nodeType": "121", "messageId": "122", "endLine": 37, "endColumn": 31, "suggestions": "160"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 61, "column": 39, "nodeType": "121", "messageId": "122", "endLine": 61, "endColumn": 42, "suggestions": "161"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 57, "column": 24, "nodeType": "121", "messageId": "122", "endLine": 57, "endColumn": 27, "suggestions": "162"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 106, "column": 28, "nodeType": "121", "messageId": "122", "endLine": 106, "endColumn": 31, "suggestions": "163"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 5, "column": 31, "nodeType": "121", "messageId": "122", "endLine": 5, "endColumn": 34, "suggestions": "164"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 19, "column": 30, "nodeType": "121", "messageId": "122", "endLine": 19, "endColumn": 33, "suggestions": "165"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 20, "column": 30, "nodeType": "121", "messageId": "122", "endLine": 20, "endColumn": 33, "suggestions": "166"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 34, "column": 28, "nodeType": "121", "messageId": "122", "endLine": 34, "endColumn": 31, "suggestions": "167"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 71, "column": 34, "nodeType": "121", "messageId": "122", "endLine": 71, "endColumn": 37, "suggestions": "168"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 77, "column": 15, "nodeType": "121", "messageId": "122", "endLine": 77, "endColumn": 18, "suggestions": "169"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 107, "column": 10, "nodeType": "121", "messageId": "122", "endLine": 107, "endColumn": 13, "suggestions": "170"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 115, "column": 10, "nodeType": "121", "messageId": "122", "endLine": 115, "endColumn": 13, "suggestions": "171"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 48, "column": 35, "nodeType": "121", "messageId": "122", "endLine": 48, "endColumn": 38, "suggestions": "172"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 50, "column": 39, "nodeType": "121", "messageId": "122", "endLine": 50, "endColumn": 42, "suggestions": "173"}, {"ruleId": "119", "severity": 2, "message": "120", "line": 62, "column": 37, "nodeType": "121", "messageId": "122", "endLine": 62, "endColumn": 40, "suggestions": "174"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["175", "176"], ["177", "178"], ["179", "180"], ["181", "182"], ["183", "184"], ["185", "186"], ["187", "188"], ["189", "190"], ["191", "192"], ["193", "194"], ["195", "196"], ["197", "198"], ["199", "200"], ["201", "202"], ["203", "204"], ["205", "206"], ["207", "208"], ["209", "210"], ["211", "212"], ["213", "214"], ["215", "216"], ["217", "218"], ["219", "220"], ["221", "222"], ["223", "224"], ["225", "226"], ["227", "228"], ["229", "230"], ["231", "232"], ["233", "234"], ["235", "236"], ["237", "238"], ["239", "240"], ["241", "242"], ["243", "244"], ["245", "246"], ["247", "248"], ["249", "250"], ["251", "252"], ["253", "254"], ["255", "256"], ["257", "258"], ["259", "260"], ["261", "262"], ["263", "264"], ["265", "266"], ["267", "268"], ["269", "270"], ["271", "272"], ["273", "274"], ["275", "276"], ["277", "278"], {"messageId": "279", "fix": "280", "desc": "281"}, {"messageId": "282", "fix": "283", "desc": "284"}, {"messageId": "279", "fix": "285", "desc": "281"}, {"messageId": "282", "fix": "286", "desc": "284"}, {"messageId": "279", "fix": "287", "desc": "281"}, {"messageId": "282", "fix": "288", "desc": "284"}, {"messageId": "279", "fix": "289", "desc": "281"}, {"messageId": "282", "fix": "290", "desc": "284"}, {"messageId": "279", "fix": "291", "desc": "281"}, {"messageId": "282", "fix": "292", "desc": "284"}, {"messageId": "279", "fix": "293", "desc": "281"}, {"messageId": "282", "fix": "294", "desc": "284"}, {"messageId": "279", "fix": "295", "desc": "281"}, {"messageId": "282", "fix": "296", "desc": "284"}, {"messageId": "279", "fix": "297", "desc": "281"}, {"messageId": "282", "fix": "298", "desc": "284"}, {"messageId": "279", "fix": "299", "desc": "281"}, {"messageId": "282", "fix": "300", "desc": "284"}, {"messageId": "279", "fix": "301", "desc": "281"}, {"messageId": "282", "fix": "302", "desc": "284"}, {"messageId": "279", "fix": "303", "desc": "281"}, {"messageId": "282", "fix": "304", "desc": "284"}, {"messageId": "279", "fix": "305", "desc": "281"}, {"messageId": "282", "fix": "306", "desc": "284"}, {"messageId": "279", "fix": "307", "desc": "281"}, {"messageId": "282", "fix": "308", "desc": "284"}, {"messageId": "279", "fix": "309", "desc": "281"}, {"messageId": "282", "fix": "310", "desc": "284"}, {"messageId": "279", "fix": "311", "desc": "281"}, {"messageId": "282", "fix": "312", "desc": "284"}, {"messageId": "279", "fix": "313", "desc": "281"}, {"messageId": "282", "fix": "314", "desc": "284"}, {"messageId": "279", "fix": "315", "desc": "281"}, {"messageId": "282", "fix": "316", "desc": "284"}, {"messageId": "279", "fix": "317", "desc": "281"}, {"messageId": "282", "fix": "318", "desc": "284"}, {"messageId": "279", "fix": "319", "desc": "281"}, {"messageId": "282", "fix": "320", "desc": "284"}, {"messageId": "279", "fix": "321", "desc": "281"}, {"messageId": "282", "fix": "322", "desc": "284"}, {"messageId": "279", "fix": "323", "desc": "281"}, {"messageId": "282", "fix": "324", "desc": "284"}, {"messageId": "279", "fix": "325", "desc": "281"}, {"messageId": "282", "fix": "326", "desc": "284"}, {"messageId": "279", "fix": "327", "desc": "281"}, {"messageId": "282", "fix": "328", "desc": "284"}, {"messageId": "279", "fix": "329", "desc": "281"}, {"messageId": "282", "fix": "330", "desc": "284"}, {"messageId": "279", "fix": "331", "desc": "281"}, {"messageId": "282", "fix": "332", "desc": "284"}, {"messageId": "279", "fix": "333", "desc": "281"}, {"messageId": "282", "fix": "334", "desc": "284"}, {"messageId": "279", "fix": "335", "desc": "281"}, {"messageId": "282", "fix": "336", "desc": "284"}, {"messageId": "279", "fix": "337", "desc": "281"}, {"messageId": "282", "fix": "338", "desc": "284"}, {"messageId": "279", "fix": "339", "desc": "281"}, {"messageId": "282", "fix": "340", "desc": "284"}, {"messageId": "279", "fix": "341", "desc": "281"}, {"messageId": "282", "fix": "342", "desc": "284"}, {"messageId": "279", "fix": "343", "desc": "281"}, {"messageId": "282", "fix": "344", "desc": "284"}, {"messageId": "279", "fix": "345", "desc": "281"}, {"messageId": "282", "fix": "346", "desc": "284"}, {"messageId": "279", "fix": "347", "desc": "281"}, {"messageId": "282", "fix": "348", "desc": "284"}, {"messageId": "279", "fix": "349", "desc": "281"}, {"messageId": "282", "fix": "350", "desc": "284"}, {"messageId": "279", "fix": "351", "desc": "281"}, {"messageId": "282", "fix": "352", "desc": "284"}, {"messageId": "279", "fix": "353", "desc": "281"}, {"messageId": "282", "fix": "354", "desc": "284"}, {"messageId": "279", "fix": "355", "desc": "281"}, {"messageId": "282", "fix": "356", "desc": "284"}, {"messageId": "279", "fix": "357", "desc": "281"}, {"messageId": "282", "fix": "358", "desc": "284"}, {"messageId": "279", "fix": "359", "desc": "281"}, {"messageId": "282", "fix": "360", "desc": "284"}, {"messageId": "279", "fix": "361", "desc": "281"}, {"messageId": "282", "fix": "362", "desc": "284"}, {"messageId": "279", "fix": "363", "desc": "281"}, {"messageId": "282", "fix": "364", "desc": "284"}, {"messageId": "279", "fix": "365", "desc": "281"}, {"messageId": "282", "fix": "366", "desc": "284"}, {"messageId": "279", "fix": "367", "desc": "281"}, {"messageId": "282", "fix": "368", "desc": "284"}, {"messageId": "279", "fix": "369", "desc": "281"}, {"messageId": "282", "fix": "370", "desc": "284"}, {"messageId": "279", "fix": "371", "desc": "281"}, {"messageId": "282", "fix": "372", "desc": "284"}, {"messageId": "279", "fix": "373", "desc": "281"}, {"messageId": "282", "fix": "374", "desc": "284"}, {"messageId": "279", "fix": "375", "desc": "281"}, {"messageId": "282", "fix": "376", "desc": "284"}, {"messageId": "279", "fix": "377", "desc": "281"}, {"messageId": "282", "fix": "378", "desc": "284"}, {"messageId": "279", "fix": "379", "desc": "281"}, {"messageId": "282", "fix": "380", "desc": "284"}, {"messageId": "279", "fix": "381", "desc": "281"}, {"messageId": "282", "fix": "382", "desc": "284"}, {"messageId": "279", "fix": "383", "desc": "281"}, {"messageId": "282", "fix": "384", "desc": "284"}, {"messageId": "279", "fix": "385", "desc": "281"}, {"messageId": "282", "fix": "386", "desc": "284"}, "suggestUnknown", {"range": "387", "text": "388"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "389", "text": "390"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "391", "text": "388"}, {"range": "392", "text": "390"}, {"range": "393", "text": "388"}, {"range": "394", "text": "390"}, {"range": "395", "text": "388"}, {"range": "396", "text": "390"}, {"range": "397", "text": "388"}, {"range": "398", "text": "390"}, {"range": "399", "text": "388"}, {"range": "400", "text": "390"}, {"range": "401", "text": "388"}, {"range": "402", "text": "390"}, {"range": "403", "text": "388"}, {"range": "404", "text": "390"}, {"range": "405", "text": "388"}, {"range": "406", "text": "390"}, {"range": "407", "text": "388"}, {"range": "408", "text": "390"}, {"range": "409", "text": "388"}, {"range": "410", "text": "390"}, {"range": "411", "text": "388"}, {"range": "412", "text": "390"}, {"range": "413", "text": "388"}, {"range": "414", "text": "390"}, {"range": "415", "text": "388"}, {"range": "416", "text": "390"}, {"range": "417", "text": "388"}, {"range": "418", "text": "390"}, {"range": "419", "text": "388"}, {"range": "420", "text": "390"}, {"range": "421", "text": "388"}, {"range": "422", "text": "390"}, {"range": "423", "text": "388"}, {"range": "424", "text": "390"}, {"range": "425", "text": "388"}, {"range": "426", "text": "390"}, {"range": "427", "text": "388"}, {"range": "428", "text": "390"}, {"range": "429", "text": "388"}, {"range": "430", "text": "390"}, {"range": "431", "text": "388"}, {"range": "432", "text": "390"}, {"range": "433", "text": "388"}, {"range": "434", "text": "390"}, {"range": "435", "text": "388"}, {"range": "436", "text": "390"}, {"range": "437", "text": "388"}, {"range": "438", "text": "390"}, {"range": "439", "text": "388"}, {"range": "440", "text": "390"}, {"range": "441", "text": "388"}, {"range": "442", "text": "390"}, {"range": "443", "text": "388"}, {"range": "444", "text": "390"}, {"range": "445", "text": "388"}, {"range": "446", "text": "390"}, {"range": "447", "text": "388"}, {"range": "448", "text": "390"}, {"range": "449", "text": "388"}, {"range": "450", "text": "390"}, {"range": "451", "text": "388"}, {"range": "452", "text": "390"}, {"range": "453", "text": "388"}, {"range": "454", "text": "390"}, {"range": "455", "text": "388"}, {"range": "456", "text": "390"}, {"range": "457", "text": "388"}, {"range": "458", "text": "390"}, {"range": "459", "text": "388"}, {"range": "460", "text": "390"}, {"range": "461", "text": "388"}, {"range": "462", "text": "390"}, {"range": "463", "text": "388"}, {"range": "464", "text": "390"}, {"range": "465", "text": "388"}, {"range": "466", "text": "390"}, {"range": "467", "text": "388"}, {"range": "468", "text": "390"}, {"range": "469", "text": "388"}, {"range": "470", "text": "390"}, {"range": "471", "text": "388"}, {"range": "472", "text": "390"}, {"range": "473", "text": "388"}, {"range": "474", "text": "390"}, {"range": "475", "text": "388"}, {"range": "476", "text": "390"}, {"range": "477", "text": "388"}, {"range": "478", "text": "390"}, {"range": "479", "text": "388"}, {"range": "480", "text": "390"}, {"range": "481", "text": "388"}, {"range": "482", "text": "390"}, {"range": "483", "text": "388"}, {"range": "484", "text": "390"}, {"range": "485", "text": "388"}, {"range": "486", "text": "390"}, {"range": "487", "text": "388"}, {"range": "488", "text": "390"}, {"range": "489", "text": "388"}, {"range": "490", "text": "390"}, {"range": "491", "text": "388"}, {"range": "492", "text": "390"}, [3784, 3787], "unknown", [3784, 3787], "never", [4176, 4179], [4176, 4179], [1072, 1075], [1072, 1075], [1223, 1226], [1223, 1226], [1249, 1252], [1249, 1252], [1353, 1356], [1353, 1356], [1379, 1382], [1379, 1382], [1526, 1529], [1526, 1529], [1668, 1671], [1668, 1671], [1694, 1697], [1694, 1697], [2428, 2431], [2428, 2431], [2454, 2457], [2454, 2457], [2861, 2864], [2861, 2864], [3184, 3187], [3184, 3187], [3352, 3355], [3352, 3355], [3526, 3529], [3526, 3529], [3693, 3696], [3693, 3696], [3719, 3722], [3719, 3722], [3894, 3897], [3894, 3897], [4140, 4143], [4140, 4143], [4166, 4169], [4166, 4169], [4300, 4303], [4300, 4303], [4416, 4419], [4416, 4419], [4875, 4878], [4875, 4878], [5024, 5027], [5024, 5027], [5216, 5219], [5216, 5219], [5327, 5330], [5327, 5330], [5522, 5525], [5522, 5525], [5642, 5645], [5642, 5645], [5668, 5671], [5668, 5671], [5973, 5976], [5973, 5976], [6154, 6157], [6154, 6157], [870, 873], [870, 873], [1168, 1171], [1168, 1171], [2154, 2157], [2154, 2157], [2357, 2360], [2357, 2360], [2528, 2531], [2528, 2531], [717, 720], [717, 720], [1438, 1441], [1438, 1441], [1579, 1582], [1579, 1582], [2576, 2579], [2576, 2579], [131, 134], [131, 134], [434, 437], [434, 437], [469, 472], [469, 472], [816, 819], [816, 819], [1633, 1636], [1633, 1636], [1737, 1740], [1737, 1740], [2297, 2300], [2297, 2300], [2435, 2438], [2435, 2438], [1147, 1150], [1147, 1150], [1211, 1214], [1211, 1214], [1470, 1473], [1470, 1473]]