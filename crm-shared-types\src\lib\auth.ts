// Service-to-Service Authentication Utilities

export interface ServiceAuthConfig {
  serviceName: string;
  apiKey: string;
  allowedServices: string[];
}

export interface ServiceAuthRequest {
  serviceName: string;
  timestamp: number;
  signature: string;
}

export interface AuthenticatedRequest extends Request {
  serviceAuth?: {
    serviceName: string;
    isAuthenticated: boolean;
  };
}

// Generate service signature for authentication
export function generateServiceSignature(
  serviceName: string,
  apiKey: string,
  timestamp: number
): string {
  const payload = `${serviceName}:${timestamp}`;
  // In production, use proper HMAC-SHA256 with the API key
  return Buffer.from(`${payload}:${apiKey}`).toString('base64');
}

// Verify service signature
export function verifyServiceSignature(
  serviceName: string,
  apiKey: string,
  timestamp: number,
  signature: string
): boolean {
  const expectedSignature = generateServiceSignature(serviceName, apiKey, timestamp);
  
  // Check timestamp is within 5 minutes
  const now = Date.now();
  const timestampAge = now - timestamp;
  const maxAge = 5 * 60 * 1000; // 5 minutes
  
  if (timestampAge > maxAge) {
    return false;
  }
  
  return signature === expectedSignature;
}

// Service authentication headers
export function createServiceAuthHeaders(serviceName: string, apiKey: string): Record<string, string> {
  const timestamp = Date.now();
  const signature = generateServiceSignature(serviceName, apiKey, timestamp);
  
  return {
    'X-Service-Name': serviceName,
    'X-Service-Timestamp': timestamp.toString(),
    'X-Service-Signature': signature,
    'Content-Type': 'application/json',
  };
}

// Middleware for service authentication
export function createServiceAuthMiddleware(config: ServiceAuthConfig) {
  return function serviceAuthMiddleware(request: Request): AuthenticatedRequest {
    const serviceName = request.headers.get('X-Service-Name');
    const timestamp = request.headers.get('X-Service-Timestamp');
    const signature = request.headers.get('X-Service-Signature');
    
    const authenticatedRequest = request as AuthenticatedRequest;
    
    if (!serviceName || !timestamp || !signature) {
      authenticatedRequest.serviceAuth = {
        serviceName: 'unknown',
        isAuthenticated: false,
      };
      return authenticatedRequest;
    }
    
    // Check if service is allowed
    if (!config.allowedServices.includes(serviceName)) {
      authenticatedRequest.serviceAuth = {
        serviceName,
        isAuthenticated: false,
      };
      return authenticatedRequest;
    }
    
    // Verify signature
    const isValid = verifyServiceSignature(
      serviceName,
      config.apiKey,
      parseInt(timestamp),
      signature
    );
    
    authenticatedRequest.serviceAuth = {
      serviceName,
      isAuthenticated: isValid,
    };
    
    return authenticatedRequest;
  };
}

// Service client base class
export class ServiceClient {
  private baseUrl: string;
  private serviceName: string;
  private apiKey: string;
  
  constructor(baseUrl: string, serviceName: string, apiKey: string) {
    this.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
    this.serviceName = serviceName;
    this.apiKey = apiKey;
  }
  
  private getAuthHeaders(): Record<string, string> {
    return createServiceAuthHeaders(this.serviceName, this.apiKey);
  }
  
  async get<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Service request failed: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
  }
  
  async post<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`Service request failed: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
  }
  
  async put<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'PUT',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(data),
    });
    
    if (!response.ok) {
      throw new Error(`Service request failed: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
  }
  
  async delete<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Service request failed: ${response.status} ${response.statusText}`);
    }
    
    return response.json();
  }
}
