(()=>{var e={};e.id=299,e.ids=[299],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1678:(e,t,s)=>{"use strict";s.d(t,{z:()=>a});let r=require("@prisma/client"),a=globalThis.prisma??new r.PrismaClient({log:["query","error","warn"]})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},8072:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>v,routeModule:()=>m,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var r={};s.r(r),s.d(r,{GET:()=>u,POST:()=>p});var a=s(6559),i=s(8088),n=s(7719),o=s(2190),d=s(1678),c=s(5697);let l=c.z.object({firstName:c.z.string().min(1),lastName:c.z.string().min(1),email:c.z.string().email().optional(),phone:c.z.string().optional(),source:c.z.enum(["website","referral","social_media","walk_in","phone","advertisement"]),notes:c.z.string().optional(),assignedTo:c.z.string().optional()});async function u(e){try{let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),r=parseInt(t.get("limit")||"20"),a=t.get("status"),i=(s-1)*r,n=a?{status:a}:{},[c,l]=await Promise.all([d.z.lead.findMany({where:n,skip:i,take:r,orderBy:{createdAt:"desc"},include:{assignedToUser:{select:{id:!0,firstName:!0,lastName:!0,email:!0}},activities:{orderBy:{createdAt:"desc"},take:3,include:{performedByUser:{select:{firstName:!0,lastName:!0}}}}}}),d.z.lead.count({where:n})]);return o.NextResponse.json({success:!0,data:c,meta:{page:s,limit:r,total:l,totalPages:Math.ceil(l/r)}})}catch(e){return console.error("Error fetching leads:",e),o.NextResponse.json({success:!1,error:{code:"FETCH_LEADS_ERROR",message:"Failed to fetch leads"}},{status:500})}}async function p(e){try{let t=await e.json(),s=l.parse(t),r=await d.z.lead.create({data:{firstName:s.firstName,lastName:s.lastName,email:s.email,phone:s.phone,source:s.source,notes:s.notes,assignedTo:s.assignedTo},include:{assignedToUser:{select:{id:!0,firstName:!0,lastName:!0,email:!0}}}});return r.assignedTo&&await d.z.leadActivity.create({data:{leadId:r.id,activityType:"follow_up",description:"Lead assigned for initial contact",performedBy:r.assignedTo,scheduledAt:new Date(Date.now()+864e5)}}),o.NextResponse.json({success:!0,data:r},{status:201})}catch(e){if(e instanceof c.z.ZodError)return o.NextResponse.json({success:!1,error:{code:"VALIDATION_ERROR",message:"Invalid lead data",details:e.errors}},{status:400});return console.error("Error creating lead:",e),o.NextResponse.json({success:!1,error:{code:"CREATE_LEAD_ERROR",message:"Failed to create lead"}},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/leads/route",pathname:"/api/leads",filename:"route",bundlePath:"app/api/leads/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Innovative Centre\\crm-staff-service\\src\\app\\api\\leads\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:x}=m;function v(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580,697],()=>s(8072));module.exports=r})();